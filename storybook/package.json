{"name": "design-system", "private": true, "version": "0.15.0", "type": "module", "scripts": {"build": "vue-tsc -b && vite build", "build-storybook": "storybook build", "dev": "vite", "preview": "vite preview", "storybook": "storybook dev -p 6006 --ci", "test": "vitest"}, "exports": {".": {"import": "./src/components/index.ts", "require": "./src/components/index.ts"}}, "dependencies": {"@accor/icons": "^0.2.1", "@shared/composables": "workspace:*", "@storybook/addon-viewport": "^8.6.11", "@vuepic/vue-datepicker": "^11.0.1", "@vueuse/core": "^12.5.0", "fuse.js": "^7.1.0", "include-media": "^2.0.0", "normalize.css": "^8.0.1", "vue": "^3.5.13", "vue-i18n": "^9.9.4"}, "devDependencies": {"@accor/ads-components": "^2.4.2", "@accor/ads-components-locales": "^0.2.0", "@accor/shared-utils": "^1.2.3", "@chromatic-com/storybook": "^3.2.4", "@storybook/addon-essentials": "^8.5.0", "@storybook/addon-interactions": "^8.5.0", "@storybook/addon-onboarding": "^8.5.0", "@storybook/blocks": "^8.5.0", "@storybook/test": "^8.5.0", "@storybook/vue3": "^8.5.0", "@storybook/vue3-vite": "^8.5.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "sass": "^1.83.4", "storybook": "^8.5.0", "typescript": "~5.6.2", "vite": "^6.1.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.0"}}