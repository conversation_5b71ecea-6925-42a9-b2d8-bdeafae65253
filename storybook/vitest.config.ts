import { defineConfig, mergeConfig } from "vitest/config"
import { dirname, resolve } from "node:path"
import { fileURLToPath } from "node:url"
import viteConfig from "./vite.config"

export default defineConfig(() =>
  mergeConfig(viteConfig, {
    test: {
      environment: "happy-dom",
      root: fileURLToPath(new URL("./", import.meta.url)),
      server: {
        deps: {
          inline: ["@accor/ads-components", "@accor/icons", "@accor/shared-utils"]
        }
      },
      setupFiles: [resolve(dirname(fileURLToPath(import.meta.url)), "../tools/tsconfig/vitest.setup.ts")]
    }
  })
)
