import { defineConfig } from "vite"
import path from "path"
import svg<PERSON>oader from "vite-svg-loader"
import vue from "@vitejs/plugin-vue"

// https://vite.dev/config/
export default defineConfig({
  css: {
    preprocessorOptions: {
      scss: {
        api: "legacy",
        includePaths: [path.resolve(__dirname, `src/styles/`)],
        silenceDeprecations: ["legacy-js-api"] // or switch to modern api
      }
    }
  },
  plugins: [vue(), svgLoader()],
  resolve: {
    alias: {
      "@sb-base": path.resolve(__dirname, "src/styles/base"),
      "@sb-config": path.resolve(__dirname, "src/styles/config"),
      "@sb-fonts": path.resolve(__dirname, "public/fonts"),
      "@sb-styles": path.resolve(__dirname, "src/styles"),
      "@sb-utilities": path.resolve(__dirname, "src/styles/utilities")
    }
  },
  server: {
    allowedHosts: ["storybook.fairmont.local"]
  }
})
