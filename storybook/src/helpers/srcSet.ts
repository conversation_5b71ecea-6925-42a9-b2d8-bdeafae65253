import { type UiImageMedia, type UiImageMediaUnit } from "../components/atoms/UiImage/types"
import { keysMapping } from "../components/atoms/UiImage/constants"

export const renameKeys = (medias: UiImageMediaUnit, keysMapping: UiImageMediaUnit) => {
  const keyValues = Object.keys(medias).map((key) => {
    const newKey = keysMapping[key] || key
    return { [newKey]: medias[key] }
  })
  return Object.assign({}, ...keyValues)
}

export const generateSrcSet = (srcSet: UiImageMedia) => {
  let mappedSrcSet: string = ""

  if (srcSet.medias[0]) {
    const medias = srcSet.medias[0]

    const mappedMedias = renameKeys(medias, keysMapping)

    Object.entries(mappedMedias).forEach(([key, value], index) => {
      if (index === Object.entries(mappedMedias).length - 1) {
        mappedSrcSet += `${value} ${key}w`
      } else {
        mappedSrcSet += `${value} ${key}w, `
      }
    })
  }

  return mappedSrcSet
}
