// -------------------
// Color Configuration
// -------------------

// Colors variables maps

// Brand
$pink: (
  "100": #ffe6ef,
  "200": #fbacc8,
  "300": #f273a0,
  "400": #e53977,
  "500": #d4024d,
  "600": #be0037,
  "700": #9e0024,
  "800": #6e0015,
  "900": #3d0009
);

$stratosBlue: (
  "100": #e9e8f2,
  "200": #c9c7de,
  "300": #aba7ca,
  "400": #8d88b4,
  "500": #706b9d,
  "600": #544e85,
  "700": #38326c,
  "800": #1e1852,
  "900": #050033
);

$velvet: (
  "100": #eaedf5,
  "200": #bdc3df,
  "300": #919ac7,
  "400": #6671ad,
  "500": #3d4892,
  "600": #2a347e,
  "700": #1a2366,
  "800": #0e154b,
  "900": #06092c
);

// Brand/Fairmont
$caviarBlack: (
  "100": #f3f3f3,
  "200": #e3e3e3,
  "300": #c4c4c4,
  "400": #9e9e9e,
  "500": #6c6c6c,
  "600": #434343,
  "700": #3a3a3a,
  "800": #2f2f2f,
  "900": #000
);

$porcelainWhite: (
  "100": #ffffff,
  "200": #efefef,
  "300": #dfdfdf,
  "400": #cfcfcf,
  "500": #bfbfbf,
  "600": #afafaf,
  "700": #9f9f9f,
  "800": #8f8f8f,
  "900": #808080
);

$mistGrey: (
  "100": #e8e8e8,
  "200": #cfcfcf,
  "300": #b5b5b5,
  "400": #9c9c9c,
  "500": #828282,
  "600": #696969,
  "700": #4f4f4f,
  "800": #363636,
  "900": #1c1c1c
);

$warmGrey: (
  "100": #f9f8f7,
  "200": #f1efed,
  "300": #dbd7d5,
  "400": #c7c0bc,
  "500": #b0a5a0,
  "600": #9a8d87,
  "700": #81766f,
  "800": #564c47,
  "900": #443f3c
);

$pearlGrey: (
  "100": #fbfbfc,
  "200": #f7f7f7,
  "300": #eeeff0,
  "400": #e6e6e8,
  "500": #d8d9db,
  "600": #ccccce,
  "700": #b0b1b3,
  "800": #909192,
  "900": #666667
);

$russianBlue: (
  "500": #3b556c
);

$britishRacingGreen: (
  "500": #00430e
);

$amber: (
  "500": #b19143
);

$chestnut: (
  "500": #764732
);

// Brand/Neutral
$neutral: (
  "100": #f4f4f5,
  "200": #dadadd,
  "300": #c0bfc4,
  "400": #a5a5ac,
  "500": #8b8a93,
  "600": #6f6e77,
  "700": #575661,
  "800": #3e3d48,
  "900": #252339
);

// Brand/Neutral/White
$absoluteWhite: #fff;
$white: (
  "10": rgba($absoluteWhite, 0.1),
  "20": rgba($absoluteWhite, 0.2),
  "30": rgba($absoluteWhite, 0.3),
  "40": rgba($absoluteWhite, 0.4),
  "50": rgba($absoluteWhite, 0.5),
  "60": rgba($absoluteWhite, 0.6),
  "70": rgba($absoluteWhite, 0.7),
  "80": rgba($absoluteWhite, 0.8),
  "90": rgba($absoluteWhite, 0.9)
);

// Brand/Neutral/Black
$absoluteBlack: #000;
$black: (
  "10": rgba($absoluteBlack, 0.1),
  "20": rgba($absoluteBlack, 0.2),
  "30": rgba($absoluteBlack, 0.3),
  "40": rgba($absoluteBlack, 0.4),
  "50": rgba($absoluteBlack, 0.5),
  "60": rgba($absoluteBlack, 0.6),
  "70": rgba($absoluteBlack, 0.7),
  "80": rgba($absoluteBlack, 0.8),
  "90": rgba($absoluteBlack, 0.9)
);

// Functional
$red: (
  "100": #fbe6e7,
  "200": #eeafb1,
  "300": #e07b7c,
  "400": #ce4a49,
  "500": #ba1f1a,
  "600": #a50d07,
  "700": #890100,
  "800": #670000,
  "900": #3d0000
);

$yellow: (
  "100": #fef7df,
  "200": #fceec0,
  "300": #f9e4a1,
  "400": #f6db83,
  "500": #f2d166,
  "600": #ccab45,
  "700": #99832b,
  "800": #665916,
  "900": #332d0f
);

$green: (
  "100": #e6f3eb,
  "200": #aed7c0,
  "300": #78b997,
  "400": #44996e,
  "500": #117846,
  "600": #056a3a,
  "700": #00582d,
  "800": #004220,
  "900": #002813
);

$blue: (
  "100": #e6f1f8,
  "200": #add1e6,
  "300": #77aed2,
  "400": #438abc,
  "600": #025291,
  "700": #003e79,
  "900": #001836
);

$orange: (
  "500": #bb5c19
);

// Loyalty Status
$silver: (
  "100": #e4e4e5,
  "200": #c8c9ca,
  "300": #adadaf,
  "400": #919295,
  "500": #76777a,
  "600": #5f6063,
  "700": #49494b,
  "800": #323234,
  "900": #1b1b1c
);

$gold: (
  "100": #f3e7c6,
  "200": #e6d49e,
  "300": #d7bf7a,
  "400": #c4a963,
  "500": #af913a,
  "600": #8d752f,
  "700": #6c5924,
  "800": #4a3d19,
  "900": #28210d
);

$platinium: (
  "100": #bec2c6,
  "200": #a8acb2,
  "300": #91969d,
  "400": #7b8188,
  "500": #656b73,
  "600": #4f565e,
  "700": #394049,
  "800": #23272d,
  "900": #0d0f11
);

$diamond: (
  "100": #d9d9d9,
  "200": #d1d1d1,
  "300": #cacaca,
  "400": #c2c2c2,
  "500": #bababa,
  "600": #969696,
  "700": #727272,
  "800": #4f4f4f,
  "900": #2b2b2b
);

$loyaltyBlack: (
  "100": #e0e0e0,
  "200": #c4c4c4,
  "300": #a8a8a8,
  "400": #8c8c8c,
  "500": #707070,
  "600": #545454,
  "700": #383838,
  "800": #1c1c1c,
  "900": #000
);

// Basics
$basics: (
  "black": $absoluteBlack,
  "white": $absoluteWhite,
  "transparent": transparent
);

// Colors added by the designer, specified in the figma but not in the design system
$customs: (
  "text-light": #737373
);
