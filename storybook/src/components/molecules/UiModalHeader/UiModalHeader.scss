@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Modal-header {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 1.8rem 1.8rem 0 1.8rem;

  &--bordered {
    padding-bottom: 1.8rem;
    border-bottom: 1px solid map.get(colors.$neutral, "200");
  }

  &__banner {
    display: flex;
    align-items: center;
    gap: 1.1rem;
    margin-bottom: 1.8rem;
  }

  &__icon {
    font-size: 1.8rem;
    pointer-events: all;
    cursor: pointer;
  }

  &__caption {
    @include text.lbf-text("caption-01-uppercase");
  }

  &__title {
    @include text.lbf-text("exp-heading-05");
  }
}
