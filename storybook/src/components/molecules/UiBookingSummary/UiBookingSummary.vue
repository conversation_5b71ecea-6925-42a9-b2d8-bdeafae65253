<script setup lang="ts">
import { computed, defineEmits, ref } from "vue"
import { DividerDirection } from "../../atoms/UiDivider/enums"
import { type UiBookingSummaryProps } from "./interface"
import { UiBookingSummaryStep } from "./enums"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiGallery from "../UiGallery/UiGallery.vue"
import UiImage from "../../atoms/UiImage/UiImage.vue"
import UiImageCount from "../UiImageCount/UiImageCount.vue"
import UiRoomsBlock from "../../organisms/UiRoomsBlock/UiRoomsBlock.vue"
import { useDate } from "../../../composables/index"
import { useI18n } from "vue-i18n"

const props = defineProps<UiBookingSummaryProps>()

defineEmits(["UiBookingSummary::validate", "UiBookingSummary::loadRateModal", "UiBookingSummary::loadRoomModal"])

const { t } = useI18n()

const { formatDateToLocale, formatTimeToLocale } = useDate()
const dateFormat: Intl.DateTimeFormatOptions = { day: "numeric", month: "short", year: "numeric" }

const isSearchStep = computed(() => props.step === UiBookingSummaryStep.SEARCH)

const isHotelGalleryOpen = ref(false)

const roomForStay = computed(() => {
  return t("ui.molecules.ui_booking_summary.room_for_stay", {
    adults: t("ui.molecules.ui_booking_summary.adult", {
      count: props.rooms.reduce((acc, curr) => acc + curr.adults, 0)
    }),
    children: props.children
      ? t("ui.molecules.ui_booking_summary.child", { count: props.rooms.reduce((acc, curr) => acc + curr.children, 0) })
      : ""
  })
})

const roomForPeople = computed(() => {
  return t("ui.molecules.ui_booking_summary.room_for_people", {
    adults: t("ui.molecules.ui_booking_summary.adult", {
      count: props.rooms.reduce((acc, curr) => acc + curr.adults, 0)
    }),
    children: props.children
      ? t("ui.molecules.ui_booking_summary.child", { count: props.rooms.reduce((acc, curr) => acc + curr.children, 0) })
      : "",
    rooms: t("ui.molecules.ui_booking_summary.room", { count: props.rooms.length })
  })
})
</script>

<template>
  <div class="Booking-summary">
    <div class="Booking-summary__media">
      <UiImage v-if="images?.[0]" v-bind="images?.[0]" />

      <UiImageCount
        v-if="images && images.length > 1"
        class="Booking-summary__button-gallery"
        :count="images?.length || 1"
        :aria-label="$t('ui.molecules.ui_booking_summary.image_count_button', { count: images?.length })"
        @ui-image-count::click="isHotelGalleryOpen = true"
      />
    </div>

    <UiGallery
      v-if="images?.length"
      :images="images"
      :is-open="isHotelGalleryOpen"
      :title="$t('ui.molecules.ui_booking_summary.hotel_gallery')"
      @ui-gallery::close="isHotelGalleryOpen = false"
    />

    <div class="Booking-summary__summary">
      <h3 class="Booking-summary__title">{{ title }}</h3>

      <div class="Booking-summary__dates">
        <div class="Booking-summary__check">
          <p class="Booking-summary__subtitle">{{ $t("ui.molecules.ui_booking_summary.check_in") }}</p>
          <p class="Booking-summary__date">
            {{ formatDateToLocale(dateIn, dateFormat) }}
          </p>
          <p class="Booking-summary__deadline">
            {{ $t("ui.molecules.ui_booking_summary.from", { t: formatTimeToLocale(checkInHour) }) }}
          </p>
        </div>

        <div class="Booking-summary__v-divider"></div>

        <div class="Booking-summary__check">
          <p class="Booking-summary__subtitle">{{ $t("ui.molecules.ui_booking_summary.check_out") }}</p>
          <p class="Booking-summary__date">
            {{ formatDateToLocale(dateOut, dateFormat) }}
          </p>
          <p class="Booking-summary__deadline">
            {{ $t("ui.molecules.ui_booking_summary.until", { t: formatTimeToLocale(checkOutHour) }) }}
          </p>
        </div>
      </div>
    </div>

    <div v-if="isSearchStep" class="Booking-summary__h-divider"></div>

    <div v-if="isSearchStep" class="Booking-summary__search">
      <p class="Booking-summary__subtitle">{{ $t("ui.molecules.ui_booking_summary.your_search") }}</p>
      <p class="Booking-summary__rooms-people">
        {{ roomForPeople }}
      </p>
    </div>

    <div v-if="!isSearchStep && rooms.length === 1" class="Booking-summary__stay">
      <p>
        {{ roomForStay }}
      </p>
    </div>

    <template v-if="!isSearchStep && rooms.length > 1">
      <UiDivider :direction="DividerDirection.HORIZONTAL" length="auto" class="Booking-summary__rooms-divider" />

      <UiRoomsBlock
        :rooms="rooms"
        :current-room-id="currentRoomId"
        @ui-rooms-block::load-rate-modal="$emit('UiBookingSummary::loadRateModal', $event)"
        @ui-rooms-block::load-room-modal="$emit('UiBookingSummary::loadRoomModal', $event)"
      />
    </template>

    <UiButton
      v-if="isSearchStep"
      :text="$t('ui.molecules.ui_booking_summary.check_rates')"
      :is-loading="loading"
      class="Booking-summary__button"
      @click="$emit('UiBookingSummary::validate')"
    />
  </div>
</template>

<style lang="scss" scoped src="./UiBookingSummary.scss"></style>
