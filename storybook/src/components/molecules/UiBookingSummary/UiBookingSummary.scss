@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Booking-summary {
  display: flex;
  flex-direction: column;
  border-style: solid;
  border-width: 1px;
  border-radius: map.get(boxes.$radii, "soft");
  border-color: map.get(colors.$caviarBlack, "200");

  @include mq.media(">=medium") {
    overflow: hidden;
  }

  &__media {
    position: relative;

    & > image {
      aspect-ratio: 16/9;
    }
  }

  &__summary {
    display: flex;
    flex-direction: column;
    padding-bottom: 2.4rem;
    gap: map.get(spaces.$sizes, "7");
    padding: map.get(spaces.$sizes, "6");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__dates {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: map.get(spaces.$sizes, "6");
  }

  &__button-gallery {
    position: absolute;
    top: 0.8rem;
    right: 0.8rem;
  }

  &__search {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
    padding-inline: map.get(spaces.$sizes, "6");
    padding-block: map.get(spaces.$sizes, "7");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__stay {
    @include text.lbf-text("body-02-strong");
    padding-block: map.get(spaces.$sizes, "7");
    padding-inline: map.get(spaces.$sizes, "6");

    color: map.get(colors.$caviarBlack, "700");
    background-color: map.get(colors.$pearlGrey, "200");
  }

  &__title {
    @include text.lbf-text("heading-03-underline");

    color: map.get(colors.$caviarBlack, "900");
  }

  &__subtitle {
    @include text.lbf-text("caption-01-strong");
  }

  &__check {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "3");
  }

  &__date {
    @include text.lbf-text("body-01-strong");
  }

  &__deadline {
    @include text.lbf-text("caption-01");
    color: map.get(colors.$caviarBlack, "500");
  }

  &__rooms-people {
    @include text.lbf-text("body-02-strong");
  }

  &__button {
    width: 100%;
  }

  &__v-divider,
  &__h-divider {
    background-color: map.get(colors.$neutral, "200");
  }

  &__v-divider {
    width: 1px;
  }

  &__h-divider {
    height: 1px;
    margin-inline: map.get(spaces.$sizes, "6");
  }

  &__button :deep(.ads-button) {
    @include text.lbf-text("body-01-uppercase");
  }

  &__rooms-divider {
    width: auto;
    margin-inline: map.get(spaces.$sizes, "6");
  }
}
