<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from "vue"
import {
  getFocusableElements,
  getNextFocusableElement,
  getPreviousFocusableElement
} from "../../../helpers/accessibility"
import UiDestinationListItem from "../../atoms/UiDestinationListItem/UiDestinationListItem.vue"
import type { UiDestinationListProps } from "./interface"
import { scrollToElement } from "../../../helpers/scrollTo"

const props = defineProps<UiDestinationListProps>()

const destinations = computed(() => props.destinations)

defineEmits(["UiDestinationList::select"])

const focusableListElements = ref<HTMLElement[]>()
const destinationList = ref()
const activeDescendant = ref<string | null>(null)

const isSelected = (id: string) => {
  return id === props.selectedHotelId
}

const getListFocusableElements = () => {
  focusableListElements.value = getFocusableElements(destinationList.value) as HTMLElement[]
}

const focusPreviousListItem = (e: Event) => {
  e.preventDefault()
  if (e?.target) {
    const previousEl = getPreviousFocusableElement(
      e.target as HTMLElement,
      focusableListElements.value as HTMLElement[]
    )
    if (previousEl) {
      scrollToElement(previousEl, destinationList.value, 50)
      previousEl?.focus()
      activeDescendant.value = previousEl.id
    }
  }
}

const focusNextListItem = (e: Event) => {
  e.preventDefault()
  if (e?.target) {
    const nextEl = getNextFocusableElement(e.target as HTMLElement, focusableListElements.value as HTMLElement[])
    if (nextEl) {
      scrollToElement(nextEl, destinationList.value, 50)
      nextEl.focus()
      activeDescendant.value = nextEl.id
    }
  }
}

const handleFocus = (e: FocusEvent) => {
  if (e.target && (e.target as HTMLElement).id) {
    activeDescendant.value = (e.target as HTMLElement).id
  }
}

const handleBlur = () => {
  activeDescendant.value = null
}

onMounted(() => {
  getListFocusableElements()
})

watch(destinations, () => {
  nextTick(() => {
    getListFocusableElements()
  })
})
</script>

<template>
  <ul
    ref="destinationList"
    class="Destination-list"
    role="listbox"
    :aria-activedescendant="activeDescendant || undefined"
    @keydown.down="focusNextListItem($event)"
    @keydown.up="focusPreviousListItem($event)"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <li v-for="destination in destinations" :key="destination.id" class="Destination-list__sublist">
      <p class="Destination-title">{{ destination.name }}</p>
      <ul class="Destination-item">
        <li v-for="item in destination.items" :key="item.id">
          <UiDestinationListItem
            :id="item.id"
            :name="item.name"
            :city="item.city"
            :state="item.state"
            :country="item.country"
            :selected="isSelected(item.id)"
            :expedia-compliant="item.expediaCompliant"
            @ui-destination-list-item::select="$emit('UiDestinationList::select', $event)"
          />
        </li>
      </ul>
    </li>
  </ul>
</template>

<style lang="scss" scoped src="./UiDestinationList.scss"></style>
