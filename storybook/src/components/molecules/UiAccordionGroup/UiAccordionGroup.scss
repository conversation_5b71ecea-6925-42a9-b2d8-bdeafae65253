@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Accordion-group {
  --AdsAccordionPadding: 64px;

  :deep(.ads-accordion__trigger-label > .ui-body-01-strong) {
    @include text.lbf-text("heading-02");
  }

  :deep(.ads-accordion__content) {
    @include text.lbf-text("body-01");

    margin-block-start: map.get(spaces.$sizes, "8");
  }

  &--thin {
    // overwrites AdsAccordionGroup AND UiAccordionGroup components, hence the stronger selector
    :deep(.ads-button .ads-accordion__trigger-label .ui-body-01-strong) {
      @include text.lbf-text("body-01");

      text-transform: initial;

      color: map.get(colors.$caviarBlack, "700");
    }

    :deep(.ads-accordion) {
      padding: 0;
      border: none !important;
    }

    :deep(.ads-accordion-group) {
      display: flex;
      flex-direction: column;
      gap: map.get(spaces.$sizes, "7");
    }

    :deep(.ads-accordion__trigger) {
      padding-inline: map.get(spaces.$sizes, "7");
    }

    :deep(.ads-accordion__content) {
      display: flex;
      flex-direction: column;
      gap: map.get(spaces.$sizes, "8");
      padding-inline: map.get(spaces.$sizes, "8");
      padding-bottom: map.get(spaces.$sizes, "7");
      background-color: map.get(colors.$pearlGrey, "200");

      @include mq.media(">=medium") {
        background-color: initial;
      }
    }

    :deep(.ads-accordion__trigger-chevron) {
      background-color: map.get(colors.$caviarBlack, "700");

      &:after {
        // CSS icon overwrite on AdsAccordionGroup icons, which isn't available as the one in AdsAccordion.
        content: url("data:image/svg+xml; utf8, <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'><path fill='currentColor' d='M6 11.75a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75a.75.75 0 0 1-.75-.75Z'/><path fill='currentColor' d='M12 18a.75.75 0 0 1-.75-.75V6.75a.75.75 0 0 1 1.5 0v10.5A.75.75 0 0 1 12 18Z'/></svg>");
        display: block;
        width: 2.4rem;
        height: 2.4rem;
      }

      .ads-icon-chevron-down {
        display: none;
      }
    }

    :deep(.ads-accordion--open .ads-accordion__trigger-chevron) {
      transform: none;

      &:after {
        // CSS icon overwrite on AdsAccordionGroup icons, which isn't available as the one in AdsAccordion.
        content: url("data:image/svg+xml; utf8, <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'><rect width='16' height='1.5' x='4' y='11' fill='currentColor' rx='.75'/></svg>");
      }
    }
  }
}
