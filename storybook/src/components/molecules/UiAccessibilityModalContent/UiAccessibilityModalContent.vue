<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import type { UiAccessibilityModalContentProps } from "./interface"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiHotelHeading from "../../molecules/UiHotelHeading/UiHotelHeading.vue"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "../../atoms/UiLink/enums"
import UiListItemGroup from "../../molecules/UiListItemGroup/UiListItemGroup.vue"
import type { UiListItemProps } from "../../atoms/UiListItem/interface"

defineProps<UiAccessibilityModalContentProps>()
</script>

<template>
  <div class="Accessibility-modal-content">
    <UiHotelHeading
      :title="$t('ui.molecules.ui_accessibility_modal_content.title')"
      :description="$t('ui.molecules.ui_accessibility_modal_content.description')"
    />

    <template v-for="category in accessibilities" :key="category.category.code">
      <UiDivider :direction="DividerDirection.HORIZONTAL" />

      <section>
        <UiListItemGroup
          :title="category.category.label"
          :items="
            category.facilities.map((facility) => {
              return {
                id: facility.code,
                title: facility.label
              } as UiListItemProps
            })
          "
          has-columns
        />
      </section>
    </template>

    <template v-if="phonePrefix && phoneNumber">
      <UiDivider :direction="DividerDirection.HORIZONTAL" />

      <section>
        <h3 class="Accessibility-modal-content__contact-title">
          {{ $t("ui.molecules.ui_accessibility_modal_content.contact_title") }}
        </h3>
        <p class="Accessibility-modal-content__contact-text">
          {{ $t("ui.molecules.ui_accessibility_modal_content.phone") }}
          <UiLink
            :href="`tel:+${phonePrefix}${phoneNumber}`"
            :text="`+${phonePrefix}${phoneNumber}`"
            :variant="UiLinkVariant.TEXT"
          />
        </p>
      </section>
    </template>
  </div>
</template>

<style lang="scss" scoped src="./UiAccessibilityModalContent.scss"></style>
