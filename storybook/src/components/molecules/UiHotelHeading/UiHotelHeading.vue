<script lang="ts" setup>
import { type UiHotelHeadingProps } from "./interface"

defineProps<UiHotelHeadingProps>()
</script>

<template>
  <section class="Hotel-heading">
    <p v-if="category" class="Hotel-heading__category">
      {{ category }}
    </p>

    <h2 v-if="title" class="Hotel-heading__title">
      {{ title }}
    </h2>

    <p v-if="description" class="Hotel-heading__description">
      {{ description }}
    </p>
  </section>
</template>

<style lang="scss" scoped src="./UiHotelHeading.scss"></style>
