<script setup lang="ts">
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import type { UiImageCountProps } from "./interface"

defineEmits(["UiImageCount::click"])
defineProps<UiImageCountProps>()
</script>

<template>
  <button class="Image-count" :aria-label="ariaLabel" @click="$emit('UiImageCount::click')">
    <UiIcon name="iconGallery" />
    <p>{{ count }}</p>
  </button>
</template>

<style lang="scss" scoped src="./UiImageCount.scss"></style>
