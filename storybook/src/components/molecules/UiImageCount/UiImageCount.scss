@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";

.Image-count {
  display: flex;
  border-radius: 2px;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2rem);
  transition: background-color 0.2s ease;
  gap: map.get(spaces.$sizes, "3");
  padding: map.get(spaces.$sizes, "5");
  color: map.get(colors.$porcelainWhite, "100");

  &:hover {
    background-color: rgba(0, 0, 0, 0.6);
  }

  :deep(.Icon) {
    font-size: map.get(text.$sizes, "s");
  }

  p {
    @include text.lbf-text("caption-01-strong");
  }
}
