@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.List-item-group {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "8");

  &__label {
    @include text.lbf-text("exp-heading-05");

    @include mq.media(">=small") {
      gap: map.get(spaces.$sizes, "10");
    }
  }

  &__title {
    @include text.lbf-text("exp-heading-05");

    @include mq.media(">=small") {
      font-size: 2.8rem;
    }
  }

  &__items {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "7");

    &--columns {
      @include mq.media(">=small") {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: map.get(spaces.$sizes, "7");
      }

      @include mq.media(">=medium") {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
      }
    }
  }
}
