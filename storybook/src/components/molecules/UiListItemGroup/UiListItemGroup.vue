<script setup lang="ts">
import UiListItem from "../../atoms/UiListItem/UiListItem.vue"
import { type UiListItemGroupProps } from "./interface"

defineProps<UiListItemGroupProps>()
</script>

<template>
  <div class="List-item-group">
    <span class="List-item-group__label">{{ title }}</span>

    <ul :class="['List-item-group__items', { 'List-item-group__items--columns': hasColumns }]">
      <li v-for="item in items" :key="item.id" class="List-item-group__item">
        <UiListItem :id="item.id" :subtitle="item.subtitle" :title="item.title" />
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped src="./UiListItemGroup.scss"></style>
