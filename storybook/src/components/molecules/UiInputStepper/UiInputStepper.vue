<script setup lang="ts">
import { AdsInputStepper } from "@accor/ads-components"
import type { UiInputStepperProps } from "./interface"
import { useVModel } from "@vueuse/core"

const props = defineProps<UiInputStepperProps>()
const emit = defineEmits(["update:modelValue"])

const count = useVModel(props, "modelValue", emit)
</script>

<template>
  <div class="Input-stepper">
    <div class="Input-stepper__labels">
      <label :id="name" class="Input-stepper__label">{{ label }}</label>
      <p v-if="subLabel" class="Input-stepper__sub-label">{{ subLabel }}</p>
    </div>

    <AdsInputStepper v-model="count" :min="min" :max="max" :aria-labelledby="name" />
  </div>
</template>

<style lang="scss" scoped src="./UiInputStepper.scss"></style>
