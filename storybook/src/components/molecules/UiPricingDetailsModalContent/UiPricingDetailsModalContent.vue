<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import type { UiPricingDetailsModalContentProps } from "./interface"
import { useDate } from "../../../composables/useDate"

defineProps<UiPricingDetailsModalContentProps>()

const { formatDateToLocale } = useDate()

const formatDailyRateDate = (date: string) => {
  return formatDateToLocale(date, {
    day: "numeric",
    month: "short",
    weekday: "long",
    year: "numeric"
  })
}
</script>

<template>
  <div class="Pricing-details-modal-content">
    <h2 class="Pricing-details-modal-content__title">{{ $t("ui.molecules.ui_pricing_details_modal.title") }}</h2>

    <div class="Pricing-details-modal-content__header">
      <h3 class="Pricing-details-modal-content__section-title">
        {{ $t("ui.molecules.ui_pricing_details_modal.rate_title") }}
      </h3>
      <h4 class="Pricing-details-modal-content__section-subtitle">{{ rate.title }}</h4>
      <p class="Pricing-details-modal-content__section-description">
        {{ rate.description }}
      </p>
    </div>

    <div class="Pricing-details-modal-content__content">
      <h3 class="Pricing-details-modal-content__section-title">
        {{ $t("ui.molecules.ui_pricing_details_modal.daily_title") }}
      </h3>

      <ul class="Pricing-details-modal-content__rate-section">
        <li v-for="(dailyRate, index) in dailyRates" :key="`Pricing-details-modal-rate-section-${index}`">
          <div class="Pricing-details-modal-content__rate-section-content">
            <div class="Pricing-details-modal-content__rate-content Pricing-details-modal-content__rate-content--rate">
              <p>
                {{ formatDailyRateDate(dailyRate.date) }}
              </p>
              <p>
                {{ dailyRate.price }}
              </p>
            </div>

            <div class="Pricing-details-modal-content__rate-content Pricing-details-modal-content__rate-content--fee">
              <p>{{ dailyRate.fee.label }}</p>
            </div>
          </div>

          <UiDivider :direction="DividerDirection.HORIZONTAL" />
        </li>
      </ul>

      <div class="Pricing-details-modal-content__total-rate">
        <p class="Pricing-details-modal-content__item">
          {{ $t("ui.molecules.ui_pricing_details_modal.total_rate") }}
        </p>

        <div class="Pricing-details-modal-content__item Pricing-details-modal-content__item--price">
          <p class="Pricing-details-modal-content__section-title">
            {{ totalRate }}
          </p>
          <p class="Pricing-details-modal-content__tax">
            {{ taxLabel }}
          </p>
        </div>
      </div>
    </div>

    <div class="Pricing-details-modal-content__policies-section">
      <h3 class="Pricing-details-modal-content__section-title">
        {{ $t("ui.molecules.ui_pricing_details_modal.policies") }}
      </h3>

      <ul class="Pricing-details-modal-content__policies-list">
        <li
          v-for="(policie, index) in policies"
          :key="`Pricing-details-modal-policie-${index}`"
          class="Pricing-details-modal-content__policies-content"
        >
          <p class="Pricing-details-modal-content__policies-label">{{ policie.label }}</p>
          <p class="Pricing-details-modal-content__policies-text">{{ policie.text }}</p>
        </li>
      </ul>
    </div>
  </div>
</template>

<style lang="scss" scoped src="./UiPricingDetailsModalContent.scss"></style>
