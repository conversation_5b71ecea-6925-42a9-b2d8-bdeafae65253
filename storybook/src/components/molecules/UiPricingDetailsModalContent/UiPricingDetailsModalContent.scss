@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Pricing-details-modal-content {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "7");

  &__title {
    @include text.lbf-text("heading-01");
    padding-inline: map.get(spaces.$sizes, "6");

    @include mq.media(">=medium") {
      padding-inline: map.get(spaces.$sizes, "7");
    }
  }

  &__header {
    display: flex;
    flex-direction: column;
    padding: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=small") {
      padding: map.get(spaces.$sizes, "7");
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    padding-inline: map.get(spaces.$sizes, "6");

    @include mq.media(">=medium") {
      padding-inline: map.get(spaces.$sizes, "7");
    }
  }

  &__section-title {
    @include text.lbf-text("heading-03");

    padding-block-end: map.get(spaces.$sizes, "4");
  }

  &__section-subtitle {
    @include text.lbf-text("body-02-strong");

    color: map.get(colors.$caviarBlack, "700");
  }

  &__section-description {
    @include text.lbf-text("body-02");

    color: map.get(colors.$caviarBlack, "500");
  }

  &__rate-section {
    display: flex;
    flex-direction: column;
  }

  &__rate-section-content {
    padding-block: map.get(spaces.$sizes, "6");
  }

  &__rate-content {
    @include text.lbf-text("body-02");
    display: flex;
    justify-content: space-between;

    &--rate {
      color: map.get(colors.$caviarBlack, "700");
    }

    &--fee {
      color: map.get(colors.$caviarBlack, "500");
    }
  }

  &__total-rate {
    display: flex;
    justify-content: space-between;
    padding-inline: map.get(spaces.$sizes, "6");
  }

  &__item {
    @include text.lbf-text("label-01");
    flex: 1;

    color: map.get(colors.$caviarBlack, "700");

    &--price {
      @include text.lbf-text("label-01");
      display: flex;
      flex-direction: column;
      align-items: end;
      text-align: end;
    }
  }

  &__tax {
    @include text.lbf-text("caption-01");
    padding-block-start: map.get(spaces.$sizes, "3");
    color: map.get(colors.$caviarBlack, "500");
  }

  &__policies-section {
    display: flex;
    flex-direction: column;
    padding: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "7");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=medium") {
      padding: map.get(spaces.$sizes, "7");
    }
  }

  &__policies-list {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "7");
  }

  &__policies-content {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
    color: map.get(colors.$caviarBlack, "700");
  }

  &__policies-label {
    @include text.lbf-text("label-01");
  }

  &__policies-text {
    @include text.lbf-text("body-02");
  }
}
