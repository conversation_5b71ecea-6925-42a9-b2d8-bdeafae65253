export interface UiPricingDetailsModalContentProps {
  /**
   * Contains information about daily rates, including fee and price
   */
  dailyRates: DailyRate[]
  /**
   * Indicates whether the modal is currently loading data
   */
  isLoading?: boolean
  /**
   * An array containing details about various policies
   */
  policies: PoliciesType[]
  /**
   * Information about the rate
   */
  rate: RateType
  /**
   * Label describing the tax information
   */
  taxLabel: string
  /**
   * Total rate to be displayed
   */
  totalRate: string
}

export interface DailyRate {
  /**
   * The date for which the rate applies
   */
  date: string
  /**
   * The fee associated with the rate
   */
  fee: FeeType
  /**
   * The price for the day
   */
  price: string
}

export interface RateType {
  /**
   * Description of the rate
   */
  description: string
  title: string
}

export interface FeeType {
  /**
   * Label describing the type of fee
   */
  label: string
}

export interface PoliciesType {
  /**
   * Label describing the policy
   */
  label: string
  /**
   * Text providing details about the policy
   */
  text: string
}
