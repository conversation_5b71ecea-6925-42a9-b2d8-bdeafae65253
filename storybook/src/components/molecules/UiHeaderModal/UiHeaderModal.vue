<script setup lang="ts">
import { addTrapFocusToElement, focusFirstElement, removeTrapFocusToElement } from "../../../helpers/accessibility"
import { allowBodyOverflow, preventBodyOverflow } from "../../../helpers/bodyOverflow"
import { nextTick, useTemplateRef, watch } from "vue"
import type { UiHeaderModalProps } from "./interface"
import { onClickOutside } from "@vueuse/core"

const emit = defineEmits(["UiHeaderModal::close"])
const props = defineProps<UiHeaderModalProps>()

const headerModal = useTemplateRef("header-modal")

// Accessibility
watch(
  () => props.isOpen,
  () => {
    nextTick(() => {
      if (props.isOpen) {
        preventBodyOverflow()
        document.addEventListener("keydown", handleKeyPress)

        if (headerModal.value) {
          addTrapFocusToElement(headerModal.value)
          focusFirstElement(headerModal.value)
        }
      } else {
        document.removeEventListener("keydown", handleKeyPress)
        allowBodyOverflow()

        if (headerModal.value) {
          allowBodyOverflow()
          removeTrapFocusToElement(headerModal.value)
        }
      }
    })
  }
)

const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    emit("UiHeaderModal::close")
  }
}

nextTick(() => {
  onClickOutside(
    headerModal,
    () => {
      emit("UiHeaderModal::close")
    },
    { ignore: [props.activator] }
  )
})
</script>

<template>
  <div v-if="isOpen" class="Header-modal">
    <div class="Header-modal__overlay" />

    <div ref="header-modal" class="Header-modal__content">
      <slot name="default" />
    </div>
  </div>
</template>

<style lang="scss" scoped src="./UiHeaderModal.scss"></style>
