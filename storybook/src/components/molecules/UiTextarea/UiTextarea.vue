<script setup lang="ts">
import { AdsTextarea } from "@accor/ads-components"
import { type UiTextareaProps } from "./interface"

const props = defineProps<UiTextareaProps>()
const model = defineModel<string>({ default: "" })
</script>

<template>
  <div class="Textarea">
    <AdsTextarea v-bind="props" v-model="model" />
  </div>
</template>

<style scoped lang="scss" src="./UiTextarea.scss"></style>
