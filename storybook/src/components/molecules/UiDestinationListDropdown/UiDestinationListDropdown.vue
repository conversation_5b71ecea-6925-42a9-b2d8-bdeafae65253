<script setup lang="ts">
import { computed, ref, watch } from "vue"
import UiAccordionGroup from "../../molecules/UiAccordionGroup/UiAccordionGroup.vue"
import { type UiAccordionGroupItem } from "../../molecules/UiAccordionGroup/interface"
import { UiAccordionGroupVariation } from "../../molecules/UiAccordionGroup/enums"
import type { UiDestinationListDropdownProps } from "./interface"
import UiDestinationListRegion from "../../molecules/UiDestinationListRegion/UiDestinationListRegion.vue"

const props = withDefaults(defineProps<UiDestinationListDropdownProps>(), {
  collapseAllAccordion: true
})

defineEmits(["UiDestinationListDropdown::select"])

const accordionItemsToOpen = ref<string[]>([])

const accordionGroupItems = computed(() => {
  return props.items
    .filter((item) => item.items.length > 0)
    .map((item) => ({
      id: item.name,
      isOpen: accordionItemsToOpen.value.includes(item.name),
      name: item.name,
      title: item.name
    })) as unknown as UiAccordionGroupItem[]
})

const destinations = computed(() => {
  return props.items.map((item) => ({
    id: item.id,
    items: item.items,
    name: item.name,
    title: item.name
  }))
})

watch(
  () => props.collapseAllAccordion,
  (newCollapseValue) => {
    if (newCollapseValue) {
      accordionItemsToOpen.value = []
      return
    }
    accordionItemsToOpen.value = props.items.map((item) => item.name)
  }
)
</script>

<template>
  <div class="Destination-list-dropdown" role="listbox" aria-labelledby="combo1-label" aria-activedescendant="">
    <UiAccordionGroup
      :allow-multiple="true"
      :items="accordionGroupItems"
      :variation="UiAccordionGroupVariation.THIN"
      :opened-items="accordionItemsToOpen"
    >
      <template v-for="destination in destinations" #[`content-${destination.name}`]>
        <UiDestinationListRegion
          v-for="item in destination.items"
          :id="item.id"
          :key="item.id"
          :name="item.name"
          :items="item.items"
          :selected-item-id="selectedHotelId"
          @ui-destination-list-region::select="$emit('UiDestinationListDropdown::select', $event)"
        />
      </template>
    </UiAccordionGroup>
  </div>
</template>

<style lang="scss" scoped src="./UiDestinationListDropdown.scss"></style>
