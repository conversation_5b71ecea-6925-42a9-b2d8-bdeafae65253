<script setup lang="ts">
import { type UiGalleryProps } from "./interface"
import UiModal from "../../molecules/UiModal/UiModal.vue"
import UiSlider from "../../molecules/UiSlider/UiSlider.vue"

withDefaults(defineProps<UiGalleryProps>(), {})

defineEmits(["UiGallery::close"])
</script>

<template>
  <div class="Gallery-room">
    <UiModal
      :is-open="isOpen"
      no-header-shadow
      with-overlay
      mobile-full-screen
      @ui-modal::close="$emit('UiGallery::close')"
    >
      <div class="Gallery-room__content">
        <h3 class="heading-01">{{ title }}</h3>
        <UiSlider :images="images" />
      </div>
    </UiModal>
  </div>
</template>
<style lang="scss" scoped src="./UiGallery.scss"></style>
