import type { UiAmenityProps } from "../../atoms/UiAmenity/interface"
import type { rateCardPriceDisplay } from "./constants"

export interface UiRateCardProps {
  /**
   * Label to be displayed on top of the rate card
   */
  aggregationLabel: string
  /**
   * List of amenities detailing the room's features
   */
  amenities: UiAmenityProps[]
  /**
   *  Currency used for pricing and display
   */
  currency: string
  /**
   *  Formatted string presenting tax details
   */
  formattedTaxType?: string
  /**
   *  Loading state enabled
   */
  isLoading?: boolean
  /**
   *  Array of member pricing details
   * Display type for the member price
   */
  memberPriceDisplay?: rateCardPriceDisplay
  /**
   * Prominent Member price
   */
  prominentMemberPrice?: string
  /**
   * Public price
   */
  publicPrice: string
  /**
   * Display type for the public price
   */
  publicPriceDisplay?: rateCardPriceDisplay
  /**
   *  Unique identifier for the rate card
   */
  rateCardId: string
  /**
   * Secondary Member price
   */
  secondaryMemberPrice?: string
  /**
   * Secondary Public price
   */
  secondaryPublicPrice?: string
  /**
   * URI for user sign-in
   */
  signInUri?: string
  /**
   * User localization information
   */
  userLocalization?: string
  /**
   * Rate card title
   */
  title: string
  /**
   * Rate card link
   */
  link?: string
}
