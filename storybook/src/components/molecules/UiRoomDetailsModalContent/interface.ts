import type { KeyFeatureType } from "./types"
import type { UiImageProps } from "../../atoms/UiImage/interface"

export interface UiRoomDetailsModalContentProps {
  /**
   * List of room amenities.
   */
  amenities: Amenity[]
  /**
   * Room class code
   */
  classCode: string
  /**
   * Detailed description of the room.
   */
  description?: string
  /**
   * Array of image properties for the room.
   */
  images: UiImageProps[]
  /**
   * Key features of the room.
   */
  keyFeatures: KeyFeatureType[]
  /**
   * Title of the room.
   */
  title: string
}

export interface FacilityItem {
  /**
   * Unique identifier for the facility item
   */
  id: number
  /**
   * Main title for the facility item
   */
  title: string
  /**
   * Optional subtitle providing additional information about the facility item
   */
  subtitle?: string
}

export interface Facility {
  /**
   * Label that categorizes or describes the facility
   */
  label: string
  /**
   * A list of items detailing various aspects of the facility
   */
  items: FacilityItem[]
}

export interface Amenity {
  /**
   * Unique identifier for the amenity
   */
  id: number
  /**
   * Name of the amenity
   */
  name: string
  /**
   * Title offering a descriptive overview of the amenity
   */
  title: string
  /**
   * A list of facilities associated with the amenity
   */
  items: Facility[]
}
