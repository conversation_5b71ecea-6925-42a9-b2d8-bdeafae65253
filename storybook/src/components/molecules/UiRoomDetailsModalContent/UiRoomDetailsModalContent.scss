@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Room-details-modal-content {
  &__image {
    :deep(.Image) {
      aspect-ratio: 1.2;

      @include mq.media(">=small") {
        aspect-ratio: 1.77;
      }
    }
  }

  &__header {
    margin: 0 auto 5.6rem;
    text-align: center;

    @include mq.media(">=large") {
      margin-bottom: 6.4rem;
    }
  }

  &__content {
    padding-block-start: map.get(spaces.$sizes, "8");
    padding-inline: map.get(spaces.$sizes, "6");
    padding-block-end: map.get(spaces.$sizes, "11");

    @include mq.media(">=small") {
      padding-inline: map.get(spaces.$sizes, "8");
      padding-block: map.get(spaces.$sizes, "12");
    }

    @include mq.media(">=medium") {
      padding-inline: map.get(spaces.$sizes, "11");
    }
  }

  &__key-features {
    display: flex;
    flex-direction: column;
    text-align: center;
    padding: map.get(spaces.$sizes, "8");
    gap: map.get(spaces.$sizes, "7");
    color: map.get(colors.$caviarBlack, "700");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=small") {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: map.get(spaces.$sizes, "8");
    }

    @include mq.media(">=medium") {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__description {
    @include text.lbf-text("heading-03");
    padding-block-end: map.get(spaces.$sizes, "3");
  }

  &__label {
    @include text.lbf-text("body-02");
  }

  &__accordion {
    :deep(.List-item-group__label) {
      color: map.get(colors.$neutral, "900");
    }

    :deep(.List-item__subtitle) {
      color: map.get(colors.$caviarBlack, "500");
    }

    :deep(.ads-accordion) {
      padding: map.get(spaces.$sizes, "10") map.get(spaces.$sizes, "6");

      @include mq.media(">=small") {
        padding: 5.6rem map.get(spaces.$sizes, "11");
      }

      @include mq.media(">=medium") {
        padding: map.get(spaces.$sizes, "11");
      }

      .ads-accordion__trigger-label {
        span {
          font-size: 1.6rem;

          @include mq.media(">=small") {
            font-size: 2rem;
          }
        }
      }
    }

    :deep(.ads-accordion__trigger-label) {
      font-size: 1.6rem;
    }

    :deep(.ads-accordion__content) {
      display: flex;
      flex-direction: column;
      gap: map.get(spaces.$sizes, "8");

      @include mq.media(">=small") {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: map.get(spaces.$sizes, "10");
      }

      @include mq.media(">=medium") {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}
