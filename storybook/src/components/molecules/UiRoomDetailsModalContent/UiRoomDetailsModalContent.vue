<script setup lang="ts">
import UiAccordionGroup from "../../molecules/UiAccordionGroup/UiAccordionGroup.vue"
import UiHotelHeading from "../../molecules/UiHotelHeading/UiHotelHeading.vue"
import UiImage from "../../atoms/UiImage/UiImage.vue"
import UiListItemGroup from "../../molecules/UiListItemGroup/UiListItemGroup.vue"
import type { UiRoomDetailsModalContentProps } from "./interface"
import UiSlider from "../../molecules/UiSlider/UiSlider.vue"
import { computed } from "vue"

const props = defineProps<UiRoomDetailsModalContentProps>()

const hasSingleImage = computed(() => props.images.length === 1)
</script>

<template>
  <article class="Room-details-modal-content">
    <UiImage v-if="hasSingleImage" class="Room-details-modal-content__image" v-bind="images[0]" />

    <UiSlider v-else :images="images" />

    <div class="Room-details-modal-content__content">
      <UiHotelHeading class="Room-details-modal-content__header" :description="description" :title="title" />

      <ul class="Room-details-modal-content__key-features">
        <li v-for="keyFeature in keyFeatures" :key="keyFeature.label">
          <p class="Room-details-modal-content__description">{{ keyFeature.description }}</p>
          <p class="Room-details-modal-content__label">{{ keyFeature.label }}</p>
        </li>
      </ul>
    </div>

    <UiAccordionGroup
      v-if="amenities && amenities.length > 0"
      class="Room-details-modal-content__accordion"
      :items="amenities"
    >
      <template v-for="amenitie in amenities" :key="amenitie.id" #[`content-${amenitie.name}`]>
        <UiListItemGroup
          v-for="item in amenitie.items"
          :key="item.label"
          :items="item.items"
          :title="item.label"
          :has-columns="false"
        />
      </template>
    </UiAccordionGroup>
  </article>
</template>

<style lang="scss" scoped src="./UiRoomDetailsModalContent.scss"></style>
