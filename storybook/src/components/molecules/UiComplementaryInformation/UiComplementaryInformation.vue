<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import { type UiComplementaryInformationProps } from "./interface"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "../../atoms/UiLink/enums"

defineProps<UiComplementaryInformationProps>()
</script>

<template>
  <article class="Complementary-information">
    <div class="Complementary-information__text">
      <h3 v-if="title" class="Complementary-information__title">
        {{ title }}
      </h3>

      <div class="Complementary-information__content">
        <slot name="content"></slot>
      </div>
    </div>

    <UiLink
      v-if="topLinkText && topLinkHref"
      :href="topLinkHref"
      class="Complementary-information__link"
      :target="topLinkTarget"
      :text="topLinkText"
      :variant="UiLinkVariant.TEXT"
    />

    <div v-if="bottomLinkHref && bottomLinkText" class="Complementary-information__divider">
      <UiDivider :direction="DividerDirection.HORIZONTAL" />
    </div>

    <UiLink
      v-if="bottomLinkHref && bottomLinkText"
      :href="bottomLinkHref"
      class="Complementary-information__link"
      :target="bottomLinkTarget"
      :text="bottomLinkText"
      :variant="UiLinkVariant.TEXT"
    />
  </article>
</template>

<style lang="scss" scoped src="./UiComplementaryInformation.scss"></style>
