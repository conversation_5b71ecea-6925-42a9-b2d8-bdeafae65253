<script setup lang="ts">
import UiDestinationListItem from "../../atoms/UiDestinationListItem/UiDestinationListItem.vue"
import type { UiDestinationListRegionProps } from "./interface"

const props = defineProps<UiDestinationListRegionProps>()

defineEmits(["UiDestinationListRegion::select"])

const isSelected = (id: string) => {
  return id === props.selectedItemId
}
</script>

<template>
  <div class="Destination-list-region">
    <p class="Destination-list-region__title">
      {{ name }}
    </p>
    <ul class="Destination-list-region__list">
      <li v-for="item in items" :key="item.name">
        <UiDestinationListItem
          :id="item.id"
          :name="item.name"
          :city="item.city"
          :state="item.state"
          :country="item.country"
          :selected="isSelected(item.id)"
          :expedia-compliant="item.expediaCompliant"
          @ui-destination-list-item::select="$emit('UiDestinationListRegion::select', $event)"
        />
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped src="./UiDestinationListRegion.scss"></style>
