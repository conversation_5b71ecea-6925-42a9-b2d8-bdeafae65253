<script setup lang="ts">
import {
  addTrapFocusToElement,
  allowBodyOverflow,
  focusFirstElement,
  preventBodyOverflow,
  removeTrapFocusToElement
} from "../../../helpers/index"
import { computed, nextTick, onUnmounted, useTemplateRef, watch } from "vue"
import { ModalSize } from "./enums"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { type UiModalProps } from "./interface"
import { onClickOutside } from "@vueuse/core"

const props = withDefaults(defineProps<UiModalProps>(), {
  isCloseStyleInverse: false,
  isOpen: true,
  mobileFullScreen: false,
  mobileMinHeight: false,
  modalSize: ModalSize.DEFAULT,
  noHeader: false,
  noHeaderShadow: false,
  transition: undefined,
  withOverlay: false
})

const emit = defineEmits(["UiModal::close"])

const modal = useTemplateRef("modal")

// Computed
const getModalClasses = computed(() => {
  const classes = ["Modal"]

  if (props.withOverlay) {
    classes.push("Modal--with-overlay")

    if (props.mobileFullScreen) {
      classes.push("Modal--mobile-fullscreen")
    }

    if (props.tabletFullScreen) {
      classes.push("Modal--tablet-full-screen")
    }

    if (props.modalSize && props.modalSize !== ModalSize.DEFAULT) {
      classes.push(`Modal--${props.modalSize}`)
    }

    if (props.mobileMinHeight) {
      classes.push("Modal--mobile-min-height")
    }
  }

  if (props.class) classes.push(props.class)

  return classes.join(" ")
})

const handleClose = () => {
  emit("UiModal::close")
}

const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    emit("UiModal::close")
  }
}

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeyPress)
  allowBodyOverflow()

  if (modal.value) {
    removeTrapFocusToElement(modal.value)
  }
})

watch(
  () => props.isOpen,
  (newVal) => {
    nextTick(() => {
      if (newVal && modal.value) {
        preventBodyOverflow()
        addTrapFocusToElement(modal.value)
        focusFirstElement(modal.value)
        document.addEventListener("keydown", handleKeyPress)
      } else if (!newVal) {
        document.removeEventListener("keydown", handleKeyPress)
        allowBodyOverflow()

        if (modal.value) {
          removeTrapFocusToElement(modal.value)
        }
      }
    })
  }
)

const handleClickOutside = () => {
  emit("UiModal::close")
}

onClickOutside(modal, handleClickOutside)
</script>

<template>
  <Transition :name="transition">
    <div v-if="withOverlay && isOpen" class="Modal__overlay"></div>
  </Transition>

  <Transition :name="transition">
    <div
      v-if="isOpen"
      ref="modal"
      role="dialog"
      :aria-labelledby="$slots.header ? 'modal_label' : ariaLabelledBy"
      aria-modal="true"
      :class="getModalClasses"
    >
      <div
        v-if="!noHeader"
        class="Modal__header"
        :class="[
          { 'Modal__header--default': !$slots.header && !withOverlay },
          { 'Modal__header--overlay': withOverlay },
          { 'Modal__header--no-shadow': noHeaderShadow }
        ]"
      >
        <slot name="header">
          <div id="modal_label">
            <slot name="title"></slot>
          </div>

          <button
            class="Modal__close"
            type="button"
            :aria-label="$t('ui.molecules.ui_modal.close_button')"
            :class="{ 'Modal__close--inverse': withOverlay && isCloseStyleInverse }"
            @click="handleClose"
          >
            <slot name="close">
              <UiIcon name="close" />
            </slot>
          </button>
        </slot>
      </div>

      <div class="Modal__content">
        <slot></slot>
      </div>

      <div
        v-if="$slots.actions"
        class="Modal__actions"
        :class="{ [`Modal__actions--${actionAlignment}`]: actionAlignment }"
      >
        <slot name="actions"></slot>
      </div>
    </div>
  </Transition>
</template>

<style lang="scss" scoped src="./UiModal.scss"></style>
