<script setup lang="ts">
import { type UiPricingSectionProps } from "./interface"

const props = defineProps<UiPricingSectionProps>()
</script>

<template>
  <div class="Pricing-section">
    <div class="Pricing-section__princing-content">
      <p class="Pricing-section__label">
        {{ $t("ui.molecules.ui_pricing_section.label") }}
      </p>
      <span class="Pricing-section__price-container">
        <span
          :class="[
            'Pricing-section__current-price',
            {
              'Pricing-section__member-price': memberPrice
            }
          ]"
        >
          <span class="sr-only">{{ $t("ui.molecules.ui_pricing_section.accessibility.main_price") }}</span>
          <p class="Pricing-section__main-price">
            {{ props.mainPricing.formattedAmount }}
            <span class="Pricing-section__main-price--currency">{{ props.currency }}</span>
          </p>
        </span>
      </span>
      <p class="Pricing-section__caption">{{ props.formattedAggregationType }}</p>
    </div>
    <p class="Pricing-section__caption">{{ props.formattedTaxType }}</p>
  </div>
</template>

<style lang="scss" scoped src="./UiPrincingSection.scss"></style>
