@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Pricing-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: map.get(spaces.$sizes, "4");
  padding: map.get(spaces.$sizes, "6");

  @include mq.media(">=medium") {
    padding: map.get(spaces.$sizes, "7") map.get(spaces.$sizes, "6");
  }

  &__princing-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
  }

  &__label {
    @include text.lbf-text("label-01");

    color: map.get(colors.$caviarBlack, "500");
  }

  &__price-container {
    text-align: center;
  }

  &__main-price {
    @include text.lbf-text("heading-01");

    margin-inline-end: map.get(spaces.$sizes, "3");

    &--currency {
      @include text.lbf-text("body-02");
    }
  }

  &__current-price {
    display: flex;
    align-items: baseline;
    margin-block-end: map.get(spaces.$sizes, "2");
  }

  &__price {
    @include text.lbf-text("body-02");
    text-decoration: line-through;

    color: map.get(colors.$caviarBlack, "500");
  }

  &__member-price {
    color: map.get(colors.$velvet, "500");
  }

  &__caption {
    @include text.lbf-text("caption-01");
    max-width: 20rem;
    text-align: center;

    color: map.get(colors.$caviarBlack, "500");
  }
}
