<script setup lang="ts">
import { AdsCarousel, type Media } from "@accor/ads-components"
import { computed, useTemplateRef } from "vue"
import { type UiSliderProps } from "./interface"
import { generateSrcSet } from "../../../helpers/srcSet"
import { useDebounceFn } from "@vueuse/core"

const props = defineProps<UiSliderProps>()

const slider = useTemplateRef("slider")

const leftArrow = computed(() => slider.value?.querySelector(".ads-pagination__previous") as HTMLElement)
const rightArrow = computed(() => slider.value?.querySelector(".ads-pagination__next") as HTMLElement)

const mappedImages = computed(() => {
  return props.images.map((image, index) => ({
    description: image.alt,
    id: `image-${index}`,
    src: image.src,
    srcSet: image.srcSet ? generateSrcSet(image.srcSet) : undefined,
    type: "image"
  })) as Media[]
})

const handleScroll = useDebounceFn((e: WheelEvent) => {
  if (e.deltaY > 0) {
    rightArrow.value?.click()
  } else {
    leftArrow.value?.click()
  }
}, 100)
</script>

<template>
  <div ref="slider" class="Slider">
    <AdsCarousel
      :columns="1"
      :alignment="'start'"
      :pagination-type="'slide'"
      infinite
      :medias="mappedImages"
      @wheel="handleScroll($event)"
    />
  </div>
</template>

<style lang="scss" scoped src="./UiSlider.scss"></style>
