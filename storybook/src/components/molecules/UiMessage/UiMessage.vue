<script setup lang="ts">
import { UiMessageDisplay, UiMessageVariation } from "./enums"
import { AdsMessage } from "@accor/ads-components"
import { IconDanger } from "@accor/icons"
import { type UiMessageProps } from "./interface"
import { computed } from "vue"
import { logoMapping } from "../../../assets/logos"

const props = withDefaults(defineProps<UiMessageProps>(), {
  display: UiMessageDisplay.DEFAULT,
  isVisible: true,
  variation: UiMessageVariation.NEUTRAL
})

const getVariations = computed(() => {
  return props.variation === UiMessageVariation.WARNING ? UiMessageVariation.NEUTRAL : props.variation
})
</script>

<template>
  <div class="Message" :class="{ 'Message--horizontal': isHorizontal, 'Message--no-icon': noIcon }">
    <AdsMessage
      :class="[
        {
          [`ads-message--${UiMessageDisplay.FULL_WIDTH}`]: display === UiMessageDisplay.FULL_WIDTH
        },
        {
          ['ads-message--warning-custom']: variation === UiMessageVariation.WARNING
        },
        {
          [`ads-message--no-radius`]: noRadius
        }
      ]"
      :icon="variation === UiMessageVariation.WARNING ? IconDanger : undefined"
      :links="links"
      :message="description"
      :is-visible="isVisible"
      :variation="getVariations"
    >
      <template v-if="title || loyaltyBanner" #title>
        <component :is="logoMapping.loyaltyProgram" v-if="loyaltyBanner" class="ads-message_logo--loyalty" />
        <span v-if="title" class="ads-message__title">
          {{ title }}
        </span>
      </template>
    </AdsMessage>
  </div>
</template>

<style lang="scss" scoped src="./UiMessage.scss"></style>
