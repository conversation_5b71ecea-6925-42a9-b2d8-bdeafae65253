<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import type { UiPricingDetailsProps } from "./interface"
import { computed } from "vue"
import { useI18n } from "vue-i18n"

const props = defineProps<UiPricingDetailsProps>()

const { t } = useI18n()

const numberOfNightLabel = computed(() => {
  return t("ui.molecules.ui_pricing_details.night_label", {
    night: t("ui.molecules.ui_pricing_details.night", { count: props.numberOfNight })
  })
})
</script>

<template>
  <div class="Pricing-details">
    <h3 class="Pricing-details__title">{{ $t("ui.molecules.ui_pricing_details.title") }}</h3>

    <UiDivider :direction="DividerDirection.HORIZONTAL" />

    <div class="Pricing-details__content">
      <div class="Pricing-details__room-container">
        <div class="Pricing-details__pricing-content">
          <div class="Pricing-details__pricing-item">
            <p>{{ $t("ui.molecules.ui_pricing_details.room_label") }}</p>
            <p>
              {{ hotelCurrencyPrices.roomPrice }}
              <span>{{ hotelCurrencyPrices.currency }}</span>
            </p>
          </div>
          <p class="Pricing-details__caption">{{ numberOfNightLabel }}</p>
        </div>

        <div class="Pricing-details__pricing-item">
          <p>{{ $t("ui.molecules.ui_pricing_details.tax_label") }}</p>
          <p>
            {{ hotelCurrencyPrices.taxAndFeesPrice }}
            <span>{{ hotelCurrencyPrices.currency }}</span>
          </p>
        </div>
      </div>

      <UiDivider :direction="DividerDirection.HORIZONTAL" />

      <div class="Pricing-details__total-container">
        <div class="Pricing-details__total-content">
          <div class="Pricing-details__total-label-content">
            <p class="Pricing-details__total-title">{{ $t("ui.molecules.ui_pricing_details.total_label") }}</p>
            <p class="Pricing-details__tax-label">{{ $t("ui.molecules.ui_pricing_details.taxes_and_fees_label") }}</p>
          </div>

          <div class="Pricing-details__price-block">
            <p class="Pricing-details__total-price">
              {{ hotelCurrencyPrices.totalPrice }}
            </p>

            <p v-if="userCurrencyPrices" class="Pricing-details__user-currency-price">
              {{ $t("ui.molecules.ui_pricing_details.or") }} {{ userCurrencyPrices.totalPrice }}
            </p>
          </div>
        </div>

        <div class="Pricing-details__payment-content">
          <div class="Pricing-details__payment-item">
            <p>{{ $t("ui.molecules.ui_pricing_details.online_payment_label") }}</p>

            <div class="Pricing-details__price-block">
              <p>
                {{ hotelCurrencyPrices.onlineAmount }}
              </p>

              <p v-if="userCurrencyPrices" class="Pricing-details__user-currency-price">
                {{ $t("ui.molecules.ui_pricing_details.or") }} {{ userCurrencyPrices.onlineAmount }}
              </p>
            </div>
          </div>

          <UiDivider :direction="DividerDirection.HORIZONTAL" />

          <div class="Pricing-details__payment-item">
            <p>{{ $t("ui.molecules.ui_pricing_details.hotel_payment_label") }}</p>

            <div class="Pricing-details__price-block">
              <p>
                {{ hotelCurrencyPrices.hotelAmount }}
              </p>

              <p v-if="userCurrencyPrices" class="Pricing-details__user-currency-price">
                {{ $t("ui.molecules.ui_pricing_details.or") }} {{ userCurrencyPrices.hotelAmount }}
              </p>
            </div>
          </div>
          <template v-if="userCurrencyPrices">
            <UiDivider :direction="DividerDirection.HORIZONTAL" />
            <p class="Pricing-details__local-currency-explanation">
              {{ $t("ui.molecules.ui_pricing_details.local_currency_explanation") }}
            </p>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped src="./UiPricingDetails.scss"></style>
