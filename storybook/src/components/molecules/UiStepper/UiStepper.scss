@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";

.Stepper {
  display: flex;
  justify-content: center;

  &__list {
    display: flex;
    align-items: center;
    overflow-x: scroll;
    scroll-snap-type: x mandatory;
    padding-block-start: map.get(spaces.$sizes, "7");

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    scroll-snap-align: start;

    &--disabled {
      pointer-events: none;
    }
  }

  &__separator {
    display: flex;
    align-items: center;
    width: 100%;
    font-size: 1.8rem;
    padding-inline: map.get(spaces.$sizes, "7");
    color: map.get(colors.$caviarBlack, "500");
  }
}
