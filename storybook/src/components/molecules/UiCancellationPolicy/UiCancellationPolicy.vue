<script setup lang="ts">
import UiAmenity from "../../atoms/UiAmenity/UiAmenity.vue"
import { type UiCancellationPolicyProps } from "./interface"
import { UiCancellationPolicyVariant } from "./enums"

withDefaults(defineProps<UiCancellationPolicyProps>(), {
  variant: UiCancellationPolicyVariant.DEFAULT
})
</script>

<template>
  <ul :class="['Cancellation-policy', `Cancellation-policy--${variant}`]">
    <li
      v-for="(roomCancellationPolicy, index) in roomsCancellationPolicy"
      :key="roomCancellationPolicy.id"
      class="Cancellation-policy__item"
    >
      <h3 v-if="roomsCancellationPolicy.length > 1" class="Cancellation-policy__title">
        {{ $t("ui.molecules.ui_cancellation_policy.room_label", { number: index + 1 }) }}
      </h3>

      <div class="Cancellation-policy__content">
        <UiAmenity v-bind="roomCancellationPolicy.cancellationPolicy" />
        <p class="Cancellation-policy__description">{{ roomCancellationPolicy.description }}</p>
      </div>
    </li>
  </ul>
</template>

<style lang="scss" scoped src="./UiCancellationPolicy.scss"></style>
