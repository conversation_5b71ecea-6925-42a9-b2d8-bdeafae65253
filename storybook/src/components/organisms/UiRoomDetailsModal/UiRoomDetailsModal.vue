<script setup lang="ts">
import UiModal from "../../molecules/UiModal/UiModal.vue"
import UiRoomDetailsModalContent from "../../molecules/UiRoomDetailsModalContent/UiRoomDetailsModalContent.vue"
import type { UiRoomDetailsModalProps } from "./interface"
import UiSplashScreen from "../../molecules/UiSplashScreen/UiSplashScreen.vue"

defineProps<UiRoomDetailsModalProps>()
defineEmits(["RoomDetailsModal::close"])
</script>

<template>
  <div class="Room-details-modal" aria-live="polite" :aria-busy="isLoading">
    <UiSplashScreen :is-visible="isLoading && isOpen" src="/booking/splash.svg" :alt="$t('global?brand_logo')" />
    <UiModal
      :is-open="isOpen && !isLoading"
      is-close-style-inverse
      mobile-full-screen
      tablet-full-screen
      with-overlay
      @ui-modal::close="$emit('RoomDetailsModal::close')"
    >
      <UiRoomDetailsModalContent v-if="roomDetailsModalContent" v-bind="roomDetailsModalContent" />
    </UiModal>
  </div>
</template>
