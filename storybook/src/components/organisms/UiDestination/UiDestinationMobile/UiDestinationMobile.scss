@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";

.Destination-mobile {
  display: flex;
  flex-direction: column;
  background-color: map.get(colors.$basics, "white");
}

.Destination-mobile__input {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "8");
  padding-inline: map.get(spaces.$sizes, "7");
  padding-block: map.get(spaces.$sizes, "8");
}

.Destination-mobile__modal-control-button {
  display: flex;
  flex-grow: 1;

  :deep(.ads-button) {
    width: 100%;
  }
}
