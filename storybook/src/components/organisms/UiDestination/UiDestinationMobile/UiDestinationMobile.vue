<script setup lang="ts">
import { computed, ref } from "vue"
import type { Destination } from "../../../atoms/UiDestinationListItem/interface"
import UiDestinationListDropdown from "../../../molecules/UiDestinationListDropdown/UiDestinationListDropdown.vue"
import UiDestinationListDropdownSkeleton from "../../../skeletons/UiDestinationListDropdownSkeleton/UiDestinationListDropdownSkeleton.vue"
import type { UiDestinationMobileProps } from "./interface"
import UiInput from "../../../atoms/UiInput/UiInput.vue"
import { UiInputSize } from "../../../atoms/UiInput/enums"
import UiMessage from "../../../molecules/UiMessage/UiMessage.vue"
import { UiMessageVariation } from "../../../molecules/UiMessage/enums"
import { getHotelFromDestinationListDropdown } from "../../../../helpers/getHotelFromKeyValue"
import { useDebounceFn } from "@vueuse/core"

const props = defineProps<UiDestinationMobileProps>()
const emit = defineEmits(["UiDestinationMobile::updateHotel"])

const selectedHotel = defineModel<Destination>("selectedHotel")
const inputValue = defineModel<string>("inputValue")
const selectedHotelId = ref<string>(selectedHotel.value?.id || "")

const colapseAllAccordion = computed(() => {
  return !!props.errorMessage || !inputValue.value
})

const selectHotelById = (id: string) => {
  selectedHotelId.value = id
  const hotel = getHotelFromDestinationListDropdown(props.items, "id", id)

  inputValue.value = hotel?.name || ""

  emit("UiDestinationMobile::updateHotel", hotel)
}

// using debounced fn to avoid firing the helper every time the user fires a keydown event
const selectHotelByName = useDebounceFn(() => {
  const hotel = getHotelFromDestinationListDropdown(props.items, "name", inputValue.value ?? "")
  selectedHotel.value = hotel
}, 300)
</script>

<template>
  <div class="Destination-mobile">
    <div class="Destination-mobile__input" aria-live="polite" :aria-busy="isLoading">
      <UiInput
        v-model="inputValue"
        is-label-hidden
        :placeholder="$t('ui.organisms.ui_destination.placeholder')"
        :label="$t('ui.organisms.ui_destination.destination')"
        :size="UiInputSize.SMALL"
        is-clearable
        @ui-input::keydown="selectHotelByName"
        @ui-input::clear="selectHotelByName"
      />

      <UiMessage v-if="errorMessage" :description="errorMessage" :variation="UiMessageVariation.DANGER" />
    </div>

    <UiDestinationListDropdown
      v-if="!isLoading"
      :items="items"
      :selected-hotel-id="selectedHotel?.id"
      :collapse-all-accordion="colapseAllAccordion"
      @ui-destination-list-dropdown::select="selectHotelById($event)"
    />

    <UiDestinationListDropdownSkeleton v-else />
  </div>
</template>

<style lang="scss" scoped src="./UiDestinationMobile.scss"></style>
