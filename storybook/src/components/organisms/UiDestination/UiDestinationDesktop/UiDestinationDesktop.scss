@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";

.Destination-desktop {
  &__search-input {
    min-width: 21.9rem; // custom size based on figma
    &--bordered {
      border: 1px solid map.get(colors.$neutral, "200");
      padding: map.get(spaces.$sizes, "6");
      gap: map.get(spaces.$sizes, "4");

      :deep(.Input-search__button) {
        bottom: 1.7rem;
      }
    }
  }
}
