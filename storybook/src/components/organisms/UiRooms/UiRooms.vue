<script setup lang="ts">
import type { RoomType } from "./../UiRoom/types"
import { RoomsDirection } from "./enums"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import { UiButtonVariation } from "../../atoms/UiButton/enums"
import UiRoom from "../../organisms/UiRoom/UiRoom.vue"
import { type UiRoomsProps } from "./interface"
import { ref } from "vue"

const emit = defineEmits(["UiRooms::update", "UiRooms::add", "UiRooms::validate-children-age"])
const props = withDefaults(defineProps<UiRoomsProps>(), {
  direction: RoomsDirection.HORIZONTAL,
  maxRooms: 3
})

// we create a counter to prevent from key conflicts
const counter = ref(
  props.rooms.reduce((max, room) => {
    if (room.id > max) max = room.id

    return max
  }, 0)
)

const handleUpdateModelValue = (index: number, value: RoomType) => {
  const oldRoom = props.rooms[index]

  if (value.children > oldRoom.children) {
    value.childrenAges = [...oldRoom.childrenAges, ...Array(value.children - oldRoom.children).fill(0)]
  } else if (value.children < oldRoom.children) {
    value.childrenAges = oldRoom.childrenAges.slice(0, value.children)
  }

  const updatedRooms = props.rooms.map((room, roomIndex) => (index === roomIndex ? value : room))

  emit("UiRooms::update", updatedRooms)
}

const addRoom = () => {
  if (props.rooms.length < props.maxRooms) {
    counter.value++
    emit("UiRooms::add", counter.value)
  }
}

const displayRoomErrorMessage = (roomIndex: number) => {
  return props.roomsAndGuestsInError?.includes(roomIndex)
}
</script>

<template>
  <div class="Rooms" :class="[`Rooms--${direction}`]">
    <div class="Rooms__list" :class="[`Rooms__list--${direction}`]">
      <UiRoom
        v-for="(room, index) in rooms"
        :key="`Room-${room.id}`"
        :deletable="rooms.length > 1"
        :display-error-message="displayRoomErrorMessage(index)"
        :dropdowns-in-error="room.dropdownsInError"
        :error-message="errorMessage"
        :model-value="room"
        :bordered="direction === RoomsDirection.HORIZONTAL"
        :room-number="index + 1"
        :max-adults-number="maxAdult"
        :max-children-age="maxChildAge"
        :max-children-number="maxChild"
        :max-pax="maxPax"
        @update:model-value="(value: RoomType) => handleUpdateModelValue(index, value)"
        @ui-room::delete-room="rooms.splice(index, 1)"
        @ui-room::validate-children-age="
          $emit('UiRooms::validate-children-age', { dropdownIndex: $event, roomIndex: index })
        "
      />
    </div>

    <UiButton
      class="Rooms__add-room"
      :class="[`Rooms__add-room--${direction}`]"
      icon="add"
      :disabled="rooms.length >= maxRooms"
      :text="$t('ui.organisms.ui_rooms.add_room')"
      :variation="direction === RoomsDirection.HORIZONTAL ? UiButtonVariation.PLAIN : UiButtonVariation.TERTIARY"
      @click="addRoom"
    />
  </div>
</template>

<style lang="scss" scoped src="./UiRooms.scss"></style>
