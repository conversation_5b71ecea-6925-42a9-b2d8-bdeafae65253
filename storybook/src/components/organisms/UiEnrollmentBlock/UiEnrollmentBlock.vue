<script setup lang="ts">
import { EnrollmentBlockChoice } from "./enum"
import { type UiEnrollmentBlockProps } from "./interface"
import UiMessage from "../../molecules/UiMessage/UiMessage.vue"
import { UiMessageVariation } from "../../molecules/UiMessage/enums"
import UiRadioButton from "../../atoms/UiRadioButton/UiRadioButton.vue"
import { computed } from "vue"
import { useI18n } from "vue-i18n"

const props = defineProps<UiEnrollmentBlockProps>()

const { t } = useI18n()

const selectedChoice = defineModel<EnrollmentBlockChoice>()

const displayMessage = computed(
  () =>
    selectedChoice.value === EnrollmentBlockChoice.BECOME_MEMBER ||
    (selectedChoice.value === EnrollmentBlockChoice.STAY_UNLOGGED && props.memberPrice)
)

const showMessageIcon = computed(() => selectedChoice.value === EnrollmentBlockChoice.BECOME_MEMBER)

const messageVariant = computed(() => {
  if (selectedChoice.value === EnrollmentBlockChoice.BECOME_MEMBER) {
    return UiMessageVariation.SUCCESS
  } else if (selectedChoice.value === EnrollmentBlockChoice.STAY_UNLOGGED && props.memberPrice) {
    return UiMessageVariation.WARNING
  }

  return undefined
})

const messageContent = computed(() => {
  if (selectedChoice.value === EnrollmentBlockChoice.BECOME_MEMBER) {
    if (props.memberPrice) {
      return t("ui.organisms.ui_enrollment_block.subscription_registered_with_reduction", {
        percentage: props.percentage
      })
    } else {
      return t("ui.organisms.ui_enrollment_block.subscription_registered")
    }
  } else if (selectedChoice.value === EnrollmentBlockChoice.STAY_UNLOGGED && props.memberPrice) {
    return t("ui.organisms.ui_enrollment_block.discount_removed")
  }

  return ""
})

const signInLink = computed(() => [{ href: props.signInUrl, text: t("ui.organisms.ui_enrollment_block.sign_in") }])
</script>

<template>
  <div class="Enrollment-block">
    <UiMessage
      class="Enrollment-block__already-member"
      no-icon
      :description="$t('ui.organisms.ui_enrollment_block.already_member')"
      is-horizontal
      :links="signInLink"
      :variation="UiMessageVariation.LOYALTY"
      no-loyalty-banner
    />

    <p v-if="memberPrice" class="Enrollment-block_title">
      {{ $t("ui.organisms.ui_enrollment_block.save_money", { discount }) }}
      <span class="Enrollment-block_title--thin">
        {{ $t("ui.organisms.ui_enrollment_block.by_becoming_a_member") }}
      </span>
    </p>

    <p v-else class="Enrollment-block_title">{{ $t("ui.organisms.ui_enrollment_block.join_loyalty") }}</p>

    <div class="Enrollment-block_choices">
      <UiRadioButton
        v-model="selectedChoice"
        class="Enrollment-block_choice"
        :selected-value="EnrollmentBlockChoice.BECOME_MEMBER"
      >
        {{
          memberPrice
            ? $t("ui.organisms.ui_enrollment_block.become_member_with_reduction")
            : $t("ui.organisms.ui_enrollment_block.become_member")
        }}
        <span v-if="memberPrice" class="Enrollment-block_reduction">{{ memberPrice }}</span>
        <p class="Enrollment-block_terms-and-conditions">
          <span>{{ $t("ui.organisms.ui_enrollment_block.i_accept_terms_and_conditions") }}</span>
          <a :href="termsAndConditionsLink" target="_blank">
            {{ $t("ui.organisms.ui_enrollment_block.terms_and_conditions") }}
          </a>
        </p>
      </UiRadioButton>

      <UiRadioButton
        v-model="selectedChoice"
        class="Enrollment-block_choice"
        :selected-value="EnrollmentBlockChoice.STAY_UNLOGGED"
      >
        {{
          memberPrice
            ? $t("ui.organisms.ui_enrollment_block.stay_unlogged_without_reduction")
            : $t("ui.organisms.ui_enrollment_block.stay_unlogged")
        }}
        <span v-if="memberPrice">{{ publicPrice }}</span>
      </UiRadioButton>
    </div>

    <UiMessage
      v-if="displayMessage"
      class="Enrollment-block_message"
      :description="messageContent"
      :variation="messageVariant"
      :no-icon="!showMessageIcon"
    />
  </div>
</template>

<style lang="scss" scoped src="./UiEnrollmentBlock.scss"></style>
