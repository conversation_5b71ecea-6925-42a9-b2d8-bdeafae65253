<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import UiAmenitiesCard from "../../molecules/UiAmenitiesCard/UiAmenitiesCard.vue"
import UiButton from "../../atoms/UiButton/UiButton.vue"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import UiImage from "../../atoms/UiImage/UiImage.vue"
import UiImageCount from "../../molecules/UiImageCount/UiImageCount.vue"
import UiLink from "../../atoms/UiLink/UiLink.vue"
import UiPricingSection from "../../molecules/UiPrincingSection/UiPrincingSection.vue"
import { type UiRoomCardProps } from "./interface"

withDefaults(defineProps<UiRoomCardProps>(), {
  isLoadingRateCards: false
})

defineEmits([
  "UiRoomCardGalleryButton::click",
  "UiRoomCardDetailsButton::click",
  "UiRoomCardSelectButton::click",
  "UiRoomCardCloseButton::click"
])
</script>

<template>
  <article class="Room-card">
    <div class="Room-card__container">
      <div class="Room-card__image-container">
        <UiImage v-bind="image" :alt="$t('ui.organisms.ui_room_card.accessibility.room_image', { room: title })" />
        <UiImageCount
          v-if="gallery.count > 1"
          :count="gallery.count"
          :aria-label="$t('ui.organisms.ui_room_card.accessibility.gallery_button', { count: gallery.count })"
          class="Room-card__gallery"
          @ui-image-count::click="$emit('UiRoomCardGalleryButton::click')"
        />
      </div>

      <div class="Room-card__content">
        <UiAmenitiesCard
          class="Room-card__amenities-card"
          :title="title"
          :amenities="amenities"
          :button-label="$t('ui.organisms.ui_room_card.button.details_label')"
          @ui-amenities-card-button::click="$emit('UiRoomCardDetailsButton::click')"
        />
      </div>

      <UiDivider class="Room-card__divider" :direction="DividerDirection.VERTICAL" />

      <UiPricingSection
        :class="['Room-card__pricing-section', { 'Room-card__pricing-section--open': isOpen }]"
        v-bind="pricing"
        :member-price="expediaCompliant"
      />
    </div>

    <div v-if="!isOpen" class="Room-card__action">
      <UiButton
        type="button"
        :text="$t('ui.organisms.ui_room_card.button.select_label')"
        :is-loading="isLoadingRateCards"
        @click="$emit('UiRoomCardSelectButton::click', productCode)"
      />
    </div>

    <div v-else class="Room-card__open">
      <div class="Room-card__open-content">
        <slot name="rate-cards" />
      </div>

      <div class="Room-card__action-open-content">
        <UiLink
          class="Room-card__close-button"
          type="button"
          :text="$t('ui.organisms.ui_room_card.button.close_button')"
          @click="$emit('UiRoomCardCloseButton::click')"
        >
          <template #append-content>
            <UiIcon class="Room-card__close-button--icon" name="chevronUp" />
          </template>
        </UiLink>
      </div>
    </div>
  </article>
</template>

<style lang="scss" scoped src="./UiRoomCard.scss"></style>
