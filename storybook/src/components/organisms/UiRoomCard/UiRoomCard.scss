@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Room-card {
  border: 0.1rem solid map.get(colors.$neutral, "200");
  color: map.get(colors.$caviarBlack, "700");

  &__container {
    @include mq.media(">=large") {
      display: flex;
    }
  }

  &__image-container {
    position: relative;

    :deep(.Image) {
      aspect-ratio: 2;
    }

    @include mq.media(">=large") {
      width: 36rem;
      :deep(.Image) {
        aspect-ratio: 1.33;
      }
    }
  }

  &__gallery {
    position: absolute;
    right: map.get(spaces.$sizes, "4");
    bottom: map.get(spaces.$sizes, "4");

    @include mq.media(">=small") {
      right: initial;
      left: map.get(spaces.$sizes, "4");
    }
  }

  &__details-button {
    @include text.lbf-text("body-01-underline");

    :deep(.ads-link) {
      color: colors.$absoluteBlack;
    }
  }

  &__content {
    padding-block: map.get(spaces.$sizes, "6");
    padding-inline: map.get(spaces.$sizes, "7");

    @include mq.media(">=large") {
      flex-grow: 1;
    }

    &--price {
      text-align: center;
      background-color: map.get(colors.$pearlGrey, "200");
    }
  }

  &__amenities-card {
    :deep(.Amenities-card__amenities-list) {
      @include mq.media(">=small") {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: map.get(spaces.$sizes, "6") map.get(spaces.$sizes, "8");

        .Amenity__label {
          width: max-content;
        }
      }

      @include mq.media(">=large") {
        display: flex;
        flex-direction: column;
      }
    }
  }

  &__divider {
    display: none;

    @include mq.media(">=large") {
      display: block;
      height: unset !important; // overwrite component property
    }
  }

  &__pricing-section {
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=large") {
      min-width: 20rem;
      display: flex;
      justify-content: center;
      background-color: colors.$absoluteWhite;
    }

    &--open {
      display: none;

      @include mq.media(">=large") {
        display: flex;
      }
    }
  }

  &__action {
    padding-block-start: map.get(spaces.$sizes, "1");
    padding-block-end: map.get(spaces.$sizes, "6");
    padding-inline: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=large") {
      display: flex;
      justify-content: end;
      padding: map.get(spaces.$sizes, "6");
    }

    :deep(.ads-button) {
      min-height: 5.6rem;
      white-space: unset;

      span {
        line-break: auto;
      }

      @include mq.media(">=large") {
        min-width: 29.4rem;
      }
    }
  }

  &__open {
    background-color: map.get(colors.$pearlGrey, "200");
    padding-block: map.get(spaces.$sizes, "10");
    padding-inline: map.get(spaces.$sizes, "6");

    @include mq.media(">=small") {
      padding-inline: map.get(spaces.$sizes, "8");
    }
  }

  &__action-open-content {
    display: none;

    @include mq.media(">=small") {
      display: flex;
      align-items: center;
      justify-content: end;
    }
  }

  &__open-content {
    display: flex;
    flex-direction: column;
    margin-block-end: map.get(spaces.$sizes, "8");
  }

  &__close-button {
    &.Link {
      display: flex;
      align-items: end;
    }

    :deep(.ads-link) {
      @include text.lbf-text("label-01");
      text-decoration: none;
      color: colors.$absoluteBlack;
    }

    &--icon {
      font-size: 2.4rem;
      margin-inline-start: map.get(spaces.$sizes, "4");
    }
  }
}
