import type { Meta, StoryObj } from "@storybook/vue3"
import { DefaultRoomDetails } from "../UiRooms/constants"
import UiRoomsBlock from "./UiRoomsBlock.vue"
import { storyImages } from "../../molecules/UiSlider/storyDataSet"

const meta: Meta<typeof UiRoomsBlock> = {
  args: {
    currentRoomId: 2,
    rooms: [
      {
        ...DefaultRoomDetails,
        id: 0,
        offerDetails: {
          rateModalContent: {
            dailyRates: [],
            isLoading: false,
            policies: [],
            rate: {
              description: "This a rate description",
              title: "This is the rate title"
            },
            taxLabel: "Tax Label",
            totalRate: "$250.00"
          },
          roomModalContent: {
            amenities: [],
            classCode: "Class Code",
            description: "This is a room description",
            images: storyImages,
            keyFeatures: [],
            title: "This is the room title"
          }
        },
        productCode: "678",
        rateCode: "190"
      },
      {
        ...DefaultRoomDetails,
        id: 1,
        offerDetails: {
          rateModalContent: {
            dailyRates: [],
            isLoading: false,
            policies: [],
            rate: {
              description: "This a rate description",
              title: "This is the rate title"
            },
            taxLabel: "Tax Label",
            totalRate: "$250.00"
          },
          roomModalContent: {
            amenities: [],
            classCode: "Class Code",
            description: "This is a room description",
            images: storyImages,
            keyFeatures: [],
            title: "This is the room title"
          }
        },
        productCode: "678",
        rateCode: "190"
      },
      {
        ...DefaultRoomDetails,
        id: 2
      }
    ]
  },
  component: UiRoomsBlock,
  decorators: [() => ({ template: '<div style="width: 420px;"><story/></div>' })]
} satisfies Meta<typeof UiRoomsBlock>

export default meta

type Story = StoryObj<typeof meta>

export const RoomBlock: Story = {}
