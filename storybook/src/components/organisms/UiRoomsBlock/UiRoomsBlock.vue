<script setup lang="ts">
import UiRoomBlock from "../UiRoomBlock/UiRoomBlock.vue"
import type { UiRoomsBlockProps } from "./interface"

defineEmits(["UiRoomsBlock::loadRoomModal", "UiRoomsBlock::loadRateModal"])
defineProps<UiRoomsBlockProps>()
</script>
<template>
  <div>
    <UiRoomBlock
      v-for="(room, index) in rooms"
      :key="`Room_block_${room.id}`"
      :room="room"
      :room-number="index + 1"
      :is-current-room="room.id === currentRoomId"
      @ui-room-block::load-rate-modal="$emit('UiRoomsBlock::loadRateModal', $event)"
      @ui-room-block::load-room-modal="$emit('UiRoomsBlock::loadRoomModal', $event)"
    />
  </div>
</template>
