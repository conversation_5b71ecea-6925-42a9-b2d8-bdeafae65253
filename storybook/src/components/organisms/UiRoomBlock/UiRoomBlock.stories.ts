import type { Meta, StoryObj } from "@storybook/vue3"
import { DefaultRoomDetails } from "../UiRooms/constants"
import UiRoomBlock from "./UiRoomBlock.vue"
import { storyImages } from "../../molecules/UiSlider/storyDataSet"

const meta: Meta<typeof UiRoomBlock> = {
  args: {
    room: {
      ...DefaultRoomDetails,
      id: 0,
      offerDetails: {
        rateModalContent: {
          dailyRates: [],
          isLoading: false,
          policies: [],
          rate: {
            description: "This a rate description",
            title: "This is the rate title"
          },
          taxLabel: "Tax Label",
          totalRate: "$250.00"
        },
        rateTitle: "This is the rate title",
        roomBedding: "1 queen size bed",
        roomMedias: storyImages,
        roomModalContent: {
          amenities: [],
          classCode: "Standard Room",
          description: "This is a room description",
          images: storyImages,
          keyFeatures: [],
          title: "This is the room title"
        },
        roomTitle: "This is the room title"
      },
      productCode: "678",
      rateCode: "190"
    },
    roomNumber: 1
  },
  component: UiRoomBlock,
  decorators: [() => ({ template: '<div style="width: 420px;"><story/></div>' })]
} satisfies Meta<typeof UiRoomBlock>

export default meta

type Story = StoryObj<typeof meta>

export const RoomBlock: Story = {}
