@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Room-block {
  display: grid;
  grid-template-rows: min-content 0fr;
  transition: grid-template-rows 0.3s ease-in-out;
  background-color: colors.$absoluteWhite;

  &--open {
    grid-template-rows: min-content 1fr;

    .Room-block__header {
      :deep(.Icon) {
        transform: rotateZ(180deg);
      }
    }

    .Room-block__header {
      padding-block-end: map.get(spaces.$sizes, "6");
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: padding-block-end 0.3s ease-in-out;
    padding-block: map.get(spaces.$sizes, "7");
    padding-inline: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "4");

    &--current {
      border-left: 3px solid map.get(colors.$caviarBlack, "700");
      background-color: map.get(colors.$pearlGrey, "200");
    }

    :deep(.Icon) {
      height: map.get(spaces.$sizes, "7");
      width: map.get(spaces.$sizes, "7");

      transition: transform 0.3s ease-in-out;
    }
  }

  &__titles {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
  }

  &__content {
    overflow-y: hidden;

    & > div {
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
      border-radius: 0.2rem;
      margin-inline: map.get(spaces.$sizes, "6");
      margin-block-end: map.get(spaces.$sizes, "7");
    }
  }

  &__rate,
  &__room {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
  }

  &__link {
    :deep(.ads-link) {
      @include text.lbf-text("caption-01-underline");
      color: map.get(colors.$caviarBlack, "500");
    }
  }

  &__gallery {
    position: relative;

    :deep(.Image) {
      height: 12rem;
    }

    :deep(.Image-count) {
      position: absolute;
      top: 0.8rem;
      right: 0.8rem;
    }
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
    padding: map.get(spaces.$sizes, "6");
  }

  &__room-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: map.get(spaces.$sizes, "6");
  }

  &__infos {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
  }
}
