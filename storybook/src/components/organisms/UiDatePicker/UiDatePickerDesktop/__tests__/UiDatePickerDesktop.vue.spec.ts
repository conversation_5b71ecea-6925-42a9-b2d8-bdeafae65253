import { RouterLinkStub, mount } from "@vue/test-utils"
import { afterEach, beforeEach, describe, expect, it, vitest } from "vitest"
import UiDatePickerDesktop from "../UiDatePickerDesktop.vue"

describe("UiDatePickerDesktop.vue", () => {
  let today: Date
  let tomorrow: Date

  afterEach(() => {
    vitest.useRealTimers()
  })

  beforeEach(() => {
    vitest.useFakeTimers()
    vitest.setSystemTime(new Date("2025-06-19T04:32:00.000Z"))

    today = new Date()
    tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
  })

  it("should emit selected dates", async () => {
    process.env.TZ = "America/Los_Angeles"

    const wrapper = mount(UiDatePickerDesktop, {
      global: {
        stubs: {
          RouterLink: RouterLinkStub // only prevents one warning / 1!
        }
      }
    })
    // open the date picker
    await wrapper.find(":has(> #Date-picker-desktop__check-in)").trigger("click")

    const todayCell = wrapper.find(".dp__today")
    expect(todayCell.text()).toBe("18")
    expect(todayCell.attributes).not.toContain("disabled")

    // select a date range
    await wrapper.find(`*[data-test-id="dp-2025-07-02"]`).trigger("click")

    expect(wrapper.emitted("update:dates")?.[0][0]).toStrictEqual([new Date("2025-07-02T07:00:00.000Z")])
    await wrapper.find(`*[data-test-id="dp-2025-06-21"]`).trigger("click")
    // 2nd click
    expect(wrapper.emitted("update:dates")?.[1][0]).toStrictEqual([new Date("2025-06-21T07:00:00.000Z")])
    // range
    /* @todo: currenty is reusing local time - check if ok
    expect(wrapper.emitted('update:dates')?.[2][0]).toStrictEqual([
      new Date("2025-06-21T07:00:00.000Z"),
      new Date("2025-07-02T07:00:00.000Z"),
    ]);
    */
  })

  it("should allow picking a range in the next 405 days", async () => {
    process.env.TZ = "America/Los_Angeles"

    const wrapper = mount(UiDatePickerDesktop, {
      global: {
        stubs: {
          RouterLink: RouterLinkStub // only prevents one warning / 1!
        }
      },
      props: {
        dates: [], // We set no dates since we want the reselect the dates
        maxRange: 30,
        minRange: 1
      }
    })
    // open the date picker
    await wrapper.find(":has(> #Date-picker-desktop__check-in)").trigger("click")

    // navigate to desired month
    for (let i = 0; i < 13; i++) {
      await wrapper.find(`.Date-picker-desktop__next button`).trigger("click")
    }

    // pick a range ending on the 405th day
    await wrapper.find(`*[data-test-id="dp-2026-07-25"]`).trigger("click")
    await wrapper.find(`*[data-test-id="dp-2026-07-28"]`).trigger("click")
    expect(wrapper.emitted("update:dates")?.[1][0]).toStrictEqual([
      new Date("2026-07-25T07:00:00.000Z"),
      new Date("2026-07-28T07:00:00.000Z")
    ])
  })

  it("should not allow picking a date in the past", async () => {
    const wrapper = mount(UiDatePickerDesktop, {
      global: {
        stubs: {
          RouterLink: RouterLinkStub // only prevents one warning / 1!
        }
      },
      props: {
        dates: [today, tomorrow],
        maxRange: 30,
        minRange: 1
      }
    })
    // open the date picker
    await wrapper.find(":has(> #Date-picker-desktop__check-in)").trigger("click")

    await wrapper.find(`*[data-test-id="dp-2025-06-15"]`).trigger("click")
    await wrapper.find(`*[data-test-id="dp-2025-06-21"]`).trigger("click")
    expect(wrapper.emitted("update:dates")?.[1]).toBeUndefined()
  })

  it("should not allow picking a date after 405 days", async () => {
    const wrapper = mount(UiDatePickerDesktop, {
      global: {
        stubs: {
          RouterLink: RouterLinkStub // only prevents one warning / 1!
        }
      },
      props: {
        dates: [today, tomorrow],
        maxRange: 30,
        minRange: 1
      }
    })
    // open the date picker
    await wrapper.find(":has(> #Date-picker-desktop__check-in)").trigger("click")

    // navigate to desired month
    for (let i = 0; i < 30; i++) {
      await wrapper.find(`.Date-picker-desktop__next button`).trigger("click")
    }

    await wrapper.find(`*[data-test-id="dp-2026-07-29"]`).trigger("click")
    await wrapper.find(`*[data-test-id="dp-2026-07-31"]`).trigger("click")
    expect(wrapper.emitted("update:dates")?.[1]).toBeUndefined()
  })
})
