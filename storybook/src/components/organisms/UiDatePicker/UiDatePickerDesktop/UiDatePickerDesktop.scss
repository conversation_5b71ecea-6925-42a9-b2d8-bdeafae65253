@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Date-picker-desktop {
  &__action-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  &__arrow {
    font-size: 1.6rem;
    transform: rotateZ(180deg);
  }

  &__inputs {
    display: flex;
    align-items: center;
    gap: map.get(spaces.$sizes, "4");

    &--bordered {
      display: grid;
      grid-template-columns: 1fr 1fr;

      gap: map.get(spaces.$sizes, "7");
    }
  }

  &__input {
    @include text.lbf-text("caption-01");
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    max-width: 12.9rem;
    border: 1px solid transparent;
    transition: background-color 0.2s ease-in-out;
    padding-block: map.get(spaces.$sizes, "4");
    padding-inline: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "2");

    &:hover {
      background-color: map.get(colors.$pearlGrey, "200");
    }

    label {
      width: max-content;
    }

    input {
      @include text.lbf-text("caption-01-strong");
      padding: 0;
      border: none;
      outline: none;
      background-color: transparent;
    }

    &--bordered {
      min-width: 12.9rem;
      width: min-content;
      padding-block: map.get(spaces.$sizes, "6");
      border: 1px solid map.get(colors.$neutral, "200");
      gap: map.get(spaces.$sizes, "4");
    }

    &--focused {
      border: 1px solid colors.$absoluteBlack;
      background-color: map.get(colors.$pearlGrey, "200");
    }

    &--error {
      border: 1px solid map.get(colors.$red, "500");
    }
  }

  &__error {
    display: flex;
    background-color: map.get(colors.$red, "100");
    padding-inline: map.get(spaces.$sizes, "7");
    padding-block: map.get(spaces.$sizes, "6");
    gap: map.get(spaces.$sizes, "4");
  }

  &__error-icon {
    width: 2.4rem;
    height: 2.4rem;
    color: map.get(colors.$red, "500");
  }

  &__error-message {
    @include text.lbf-text("body-01");
    color: map.get(colors.$caviarBlack, "700");
  }

  :deep(.dp__arrow_top) {
    display: none;
  }

  :deep(.ads-button) {
    display: flex;

    .Icon {
      font-size: 2.4rem;
    }
  }
}
