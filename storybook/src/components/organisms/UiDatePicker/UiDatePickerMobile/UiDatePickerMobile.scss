@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Date-picker-mobile {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: -webkit-fill-available;
  width: -moz-available;

  &__dates {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }

  &__date {
    @include text.lbf-text("body-02");
    width: 100%;
    text-align: center;
    border: none;
    border-radius: 0;
    border-bottom: 2px solid map.get(colors.$neutral, "200");
    text-transform: capitalize;
    outline: none;
    padding: map.get(spaces.$sizes, "4");

    &--active {
      border-bottom: 2px solid map.get(colors.$caviarBlack, "800");
    }

    &::placeholder {
      text-transform: none;
    }
  }

  &__arrow {
    z-index: 10;
    position: absolute;
    top: 50%;
    left: 50%;
    transform-origin: center;
    transform: translate(-50%, -50%) rotateY(180deg);
    font-size: 1.8rem;
  }

  :deep(.ads-button) {
    display: flex;

    .Icon {
      font-size: 2.4rem;
    }
  }

  // We need this cascade to be sure that the date picker's cells will take as much space as possible until they reach 4.8rem.
  :deep(.dp__main) {
    flex-grow: 1;

    & > div:last-child {
      height: 100%;

      & > div {
        height: 100%;

        & .dp__menu {
          height: 100%;
          box-shadow: none;

          & > div {
            height: 100%;

            & .dp__instance_calendar {
              height: 100%;

              & .dp__menu_inner {
                height: 100%;
                padding-inline: map.get(spaces.$sizes, "4");

                & > div {
                  display: flex;
                  flex-direction: column;
                  height: 100%;

                  & > .dp__calendar {
                    flex-grow: 1;

                    & > div[role="grid"] {
                      display: flex;
                      flex-direction: column;
                      height: 100%;

                      & > .dp__calendar {
                        display: flex;
                        flex-direction: column;
                        height: 100%;

                        & > .dp__calendar_row {
                          height: 100%;
                          max-height: 4.8rem;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
