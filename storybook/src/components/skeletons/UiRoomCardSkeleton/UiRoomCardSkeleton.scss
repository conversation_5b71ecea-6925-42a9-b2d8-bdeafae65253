@use "sass:map";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-utilities/mq";

.Room-card-skeleton {
  border: 0.1rem solid map.get(colors.$neutral, "200");
  color: map.get(colors.$caviarBlack, "700");

  &__container {
    @include mq.media(">=large") {
      display: flex;
    }
  }

  &__image-container {
    position: relative;
    aspect-ratio: 2;

    @include mq.media(">=large") {
      width: 36rem;
      aspect-ratio: 1.33;
    }
  }

  &__image {
    width: 100%;
    height: 100%;

    :deep(.Skeleton) {
      height: 100%;
    }
  }

  &__gallery {
    position: absolute;
    right: map.get(spaces.$sizes, "4");
    bottom: map.get(spaces.$sizes, "4");
    width: 5.6rem;
    height: 4rem;

    @include mq.media(">=small") {
      right: initial;
      left: map.get(spaces.$sizes, "4");
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");
    padding-block: map.get(spaces.$sizes, "6");
    padding-inline: map.get(spaces.$sizes, "7");

    @include mq.media(">=large") {
      flex-grow: 1;
    }
  }

  &__amenities {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "4");
  }

  &__amenity {
    display: flex;
    gap: map.get(spaces.$sizes, "6");
  }

  &__divider {
    display: none;

    @include mq.media(">=large") {
      display: block;
      height: unset !important; // overwrite component property
    }
  }

  &__pricing-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: map.get(spaces.$sizes, "3");
    padding: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=large") {
      min-width: 20rem;
      display: flex;
      justify-content: center;
      background-color: colors.$absoluteWhite;
    }
  }

  &__pricing-text {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__action {
    padding-block-start: map.get(spaces.$sizes, "1");
    padding-block-end: map.get(spaces.$sizes, "6");
    padding-inline: map.get(spaces.$sizes, "6");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=large") {
      display: flex;
      justify-content: end;
      padding: map.get(spaces.$sizes, "6");
    }

    :deep(.Skeleton) {
      min-height: 5.6rem;

      @include mq.media(">=large") {
        width: 29.4rem;
      }
    }
  }
}
