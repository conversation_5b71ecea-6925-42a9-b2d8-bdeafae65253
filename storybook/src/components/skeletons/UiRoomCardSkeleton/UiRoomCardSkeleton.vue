<script setup lang="ts">
import { DividerDirection } from "../../atoms/UiDivider/enums"
import UiDivider from "../../atoms/UiDivider/UiDivider.vue"
import UiSkeleton from "../../atoms/UiSkeleton/UiSkeleton.vue"
</script>

<template>
  <div class="Room-card-skeleton">
    <div class="Room-card-skeleton__container">
      <div class="Room-card-skeleton__image-container">
        <div class="Room-card-skeleton__image">
          <UiSkeleton width="100%" height="100%" border-radius=".3rem" />
        </div>
        <div class="Room-card-skeleton__gallery">
          <UiSkeleton width="5.6rem" height="4rem" border-radius=".3rem" darker />
        </div>
      </div>
      <div class="Room-card-skeleton__content">
        <UiSkeleton width="100%" height="2.4rem" border-radius=".3rem" />

        <ul class="Room-card-skeleton__amenities">
          <li v-for="index in 2" :key="index" class="Room-card-skeleton__amenity">
            <UiSkeleton width="2.4rem" height="2.4rem" border-radius="50%" />
            <UiSkeleton width="20rem" height="2.4rem" border-radius=".3rem" />
          </li>
        </ul>

        <UiSkeleton width="10rem" height="2.2rem" border-radius=".3rem" />
      </div>

      <UiDivider class="Room-card-skeleton__divider" :direction="DividerDirection.VERTICAL" />

      <div class="Room-card-skeleton__pricing-section">
        <UiSkeleton width="4.4rem" height="2.2rem" border-radius=".3rem" />
        <UiSkeleton width="10rem" height="3.2rem" border-radius=".3rem" />
        <UiSkeleton width="4.8rem" height="1.6rem" border-radius=".3rem" />

        <div class="Room-card-skeleton__pricing-text">
          <UiSkeleton width="20rem" height="1.6rem" border-radius=".3rem" />
          <UiSkeleton width="15rem" height="1.6rem" border-radius=".3rem" />
          <UiSkeleton width="10rem" height="1.6rem" border-radius=".3rem" />
        </div>
      </div>
    </div>
    <div class="Room-card-skeleton__action">
      <UiSkeleton width="100%" height="5.6rem" />
    </div>
  </div>
</template>

<style lang="scss" scoped src="./UiRoomCardSkeleton.scss"></style>
