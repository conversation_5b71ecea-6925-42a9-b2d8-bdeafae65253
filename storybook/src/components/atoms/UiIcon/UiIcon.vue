<script setup lang="ts">
import type { UiIconProps } from "./interface"
import { computed } from "vue"
import { iconMapping } from "../../../assets/icons"

const props = defineProps<UiIconProps>()

const computedIcon = computed(() => iconMapping[props.name])
</script>

<template>
  <component :is="computedIcon" class="Icon" aria-hidden="true" />
</template>

<style lang="scss" scoped src="./UiIcon.scss"></style>
