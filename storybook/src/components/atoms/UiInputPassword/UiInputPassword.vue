<script setup lang="ts">
import { AdsInput } from "@accor/ads-components"
import { type UiInputPasswordProps } from "./interface"
import { UiInputPasswordStatus } from "./enum"
import { computed } from "vue"

const props = defineProps<UiInputPasswordProps>()

const emit = defineEmits(["UiInput::blur", "UiInput::focus", "UiInput::keydown"])

const variantClasses = computed(() => {
  return `
    ${props.status === UiInputPasswordStatus.ERROR ? "ads-input--error" : ""}
    ${props.status === UiInputPasswordStatus.SUCCESS ? "ads-input--success" : ""}
  `
})

const inputValue = defineModel<string>({ default: "" })
</script>

<template>
  <div ref="input" class="Input-password">
    <AdsInput
      v-model="inputValue"
      v-bind="props"
      type="password"
      :wrapper-attributes="{ class: variantClasses }"
      @focus="emit('UiInput::focus', $event)"
      @blur="emit('UiInput::blur', $event)"
      @keydown="emit('UiInput::keydown', $event)"
    >
      <template #additional-message>
        <slot name="rules" />
      </template>
    </AdsInput>
  </div>
</template>

<style lang="scss" scoped src="./UiInputPassword.scss"></style>
