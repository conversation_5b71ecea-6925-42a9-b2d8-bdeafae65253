<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref, useSlots } from "vue"
import { AdsSelect } from "@accor/ads-components"
import { type UiSelectProps } from "./interface"
import { UiSelectStatus } from "./enums"

const slots = useSlots() as ReturnType<typeof useSlots>

const props = defineProps<UiSelectProps>()

const emit = defineEmits(["UiSelect::blur"])

const adsSelect = ref()

const model = defineModel<string | number>()

onMounted(() => {
  if (props.defaultSelected && model.value !== props.defaultSelected) {
    model.value = props.defaultSelected
  }

  // manual blur listener because we listen an event from the ads select component,
  // and we don't have access to it
  adsSelect.value.select.addEventListener("blur", () => emit("UiSelect::blur"))
})

onBeforeUnmount(() => {
  adsSelect.value.select.removeEventListener("blur", () => emit("UiSelect::blur"))
})

const variantClasses = computed(() => {
  return `
    ${props.status === UiSelectStatus.ERROR ? "ads-select--error" : ""}
    ${props.status === UiSelectStatus.SUCCESS ? "ads-select--success" : ""}
  `
})

const hasPrependInnerIconSlot = computed(() => {
  return !!slots["prepend-inner-icon"]
})
</script>

<template>
  <div class="Select">
    <AdsSelect
      ref="adsSelect"
      v-model="model"
      v-bind="props"
      :class="{ 'Select__prepend-icon-scoped': hasPrependInnerIconSlot }"
      :wrapper-attributes="{ class: variantClasses }"
    >
      <template #prepend-inner-icon>
        <slot class="Select__icon-scoped" name="prepend-inner-icon"></slot>
      </template>
      <template #append-inner-icon>
        <slot name="append-inner-icon"></slot>
      </template>
    </AdsSelect>
  </div>
</template>

<style lang="scss" scoped src="./UiSelect.scss"></style>
