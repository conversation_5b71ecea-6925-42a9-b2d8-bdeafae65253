@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";

.Input-search {
  @include text.lbf-text("caption-01");
  position: relative;
  display: flex;
  flex-direction: column;
  padding-inline: map.get(spaces.$sizes, "6");
  padding-block: map.get(spaces.$sizes, "4");
  gap: map.get(spaces.$sizes, "2");
  border-color: map.get(colors.$basics, "transparent");

  &:hover > .Input-search__effect {
    background-color: map.get(colors.$pearlGrey, "200");
  }
}

.Input-search__label {
  @include text.lbf-text("caption-01");
  z-index: 1;
}

.Input-search__button {
  position: absolute;
  right: 1.1rem;
  bottom: 0.9rem;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1.4rem;
  height: 1.4rem;
  border-radius: 50%;
  font-size: 1.2rem;
  color: map.get(colors.$basics, "white");
  background-color: map.get(colors.$caviarBlack, "800");
}

.Input-search__effect {
  position: absolute;
  inset: 0;
  transition: all 0.2s ease-in-out;

  .Input-search.Input-search--error & {
    border: map.get(boxes.$borders, "decorative");
    border-color: map.get(colors.$red, "500");
    background-color: map.get(colors.$pearlGrey, "200");
  }
}

.Input-search__input {
  @include text.lbf-text("caption-01-strong");

  z-index: 1;
  width: calc(100% - 2.4rem);
  padding: 0;
  border: none;
  background: map.get(colors.$basics, "transparent");

  &:focus-visible {
    outline: none;
  }

  &:focus ~ .Input-search__effect,
  &:focus-visible ~ .Input-search__effect {
    background-color: map.get(colors.$pearlGrey, "200");
    border: map.get(boxes.$borders, "decorative");
  }
}
.Input-search__error-message {
  z-index: 1;
  color: map.get(colors.$red, "500");
}
