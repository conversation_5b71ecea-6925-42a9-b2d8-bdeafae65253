<script setup lang="ts">
import { computed, ref } from "vue"
import { useActiveElement, useElementHover } from "@vueuse/core"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { type UiInputSearchProps } from "./interface"

const props = defineProps<UiInputSearchProps>()

const emits = defineEmits(["UiInputSearch::clear", "UiInputSearch::focus", "UiInputSearch::keydown"])

const inputValue = defineModel<string>()
const inputSearch = ref<HTMLElement | null | undefined>(null)
const input = ref<HTMLElement | null | undefined>(null)
const button = ref<HTMLElement | null | undefined>(null)
const activeElement = useActiveElement()
const inputIsHovered = useElementHover(inputSearch)

const inputHasFocus = computed(() => {
  return input.value === activeElement.value || button.value === activeElement.value
})

const ariaInvalid = computed(() => {
  // force undefined on false to ensure the attribute isn't showing in the DOM
  return props.error === true ? props.error : undefined
})

const placeHolder = computed(() => {
  // force undefined on false to ensure the attribute isn't showing in the DOM
  return inputHasFocus.value ? undefined : props.placeholder
})

const ariaDescribedBy = computed(() => {
  // force undefined on false to ensure the attribute isn't showing in the DOM
  return props.error && props.uniqueId ? `${props.uniqueId}-error` : undefined
})

const clearInput = () => {
  inputValue.value = ""
  emits("UiInputSearch::clear")
}
</script>

<template>
  <div ref="inputSearch" :class="['Input-search', { 'Input-search--error': error }]">
    <label v-if="label" :for="uniqueId" class="Input-search__label">{{ label }}</label>
    <input
      :id="uniqueId"
      ref="input"
      v-model="inputValue"
      type="text"
      :aria-label="ariaLabel"
      :aria-invalid="ariaInvalid"
      :aria-describedby="ariaDescribedBy"
      :placeholder="placeHolder"
      class="Input-search__input"
      @keydown="$emit('UiInputSearch::keydown', $event)"
      @focus="$emit('UiInputSearch::focus')"
    />
    <button
      v-if="(inputValue && inputHasFocus) || (inputValue && inputIsHovered)"
      ref="button"
      class="Input-search__button"
      :aria-label="$t('ui.atoms.ui_search_input.aria_button')"
      @click="clearInput"
    >
      <UiIcon name="close" />
    </button>
    <div class="Input-search__effect"></div>
  </div>
</template>

<style lang="scss" scoped src="./UiInputSearch.scss"></style>
