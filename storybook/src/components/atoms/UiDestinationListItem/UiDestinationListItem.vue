<script setup lang="ts">
import type { UiDestinationListItemProps } from "./interface"

defineProps<UiDestinationListItemProps>()
defineEmits(["UiDestinationListItem::select"])
</script>

<template>
  <div
    :id="id"
    :class="['Destination-list-item', { 'Destination-list-item--selected': selected }]"
    role="option"
    :aria-selected="selected"
    tabindex="1"
    @click="$emit('UiDestinationListItem::select', id as string)"
    @keydown.enter.space="$emit('UiDestinationListItem::select', id as string)"
  >
    <p class="Destination-list-item__name">{{ name }}</p>
    <p class="Destination-list-item__address">
      <span v-if="city" class="Destination-list-item__city">{{ city }},&nbsp;</span>
      <template v-if="state">
        {{ state }}
        <template v-if="country">,&nbsp;</template>
      </template>
      <template v-if="country">{{ country }}</template>
    </p>
  </div>
</template>

<style lang="scss" scoped src="./UiDestinationListItem.scss"></style>
