<script setup lang="ts">
import { UiButtonSize, UiButtonVariation } from "./enums"
import { computed, resolveComponent } from "vue"
import { AdsButton } from "@accor/ads-components"
import { type UiButtonProps } from "./interface"
import UiIcon from "../UiIcon/UiIcon.vue"

const routerLink = resolveComponent("router-link")

const props = withDefaults(defineProps<UiButtonProps>(), {
  size: UiButtonSize.DEFAULT,
  variation: UiButtonVariation.PRIMARY
})

const computedTag = computed(() => {
  if (props.to) return routerLink
  else if (props.href && !props.to) return "a"

  return "button"
})
</script>

<template>
  <div class="Button">
    <AdsButton v-bind="props" :disabled="!!isLoading || disabled" :as="computedTag" :class="[`Button--${variation}`]">
      <UiIcon v-if="icon" class="Button__icon" :name="icon" />
      <span v-if="!isLoading" class="Button__text">{{ text }}</span>
    </AdsButton>
  </div>
</template>

<style lang="scss" scoped src="./UiButton.scss"></style>
