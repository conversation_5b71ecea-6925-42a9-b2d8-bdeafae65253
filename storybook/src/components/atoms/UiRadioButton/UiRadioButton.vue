<script setup lang="ts">
import { AdsRadio } from "@accor/ads-components"
import { type UiRadioButtonProps } from "./interface"

const props = defineProps<UiRadioButtonProps>()

const modelValue = defineModel<string | boolean | number | null | undefined>()
</script>

<template>
  <div class="Radio-button">
    <AdsRadio v-model="modelValue" v-bind="props">
      <slot name="default" />
    </AdsRadio>
  </div>
</template>

<style lang="scss" scoped src="./UiRadioButton.scss"></style>
