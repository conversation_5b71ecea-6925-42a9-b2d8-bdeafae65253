<script setup lang="ts">
import UiLink from "../../atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "../../atoms/UiLink/enums"
import { type UiStepProps } from "./interface"
import { useI18n } from "vue-i18n"

defineProps<UiStepProps>()

const { t } = useI18n()
</script>

<template>
  <div
    :class="[
      'Step',
      {
        'Step--current': isCurrent,
        'Step--completed': isCompleted
      }
    ]"
    :aria-current="isCurrent ? 'step' : undefined"
    :aria-disabled="!isCurrent"
  >
    <UiLink :href="href" :text="text" :variant="UiLinkVariant.NEUTRAL" uppercase>
      <template #preprend-content>
        <span
          :aria-label="t('ui.atoms.ui_step.aria_label', { step: step, total: totalStep })"
          :class="['Step__number', { 'Step__number--current': isCurrent, 'Step__number--completed': isCompleted }]"
        >
          {{ step }}
        </span>
      </template>
    </UiLink>
  </div>
</template>

<style lang="scss" scoped src="./UiStep.scss"></style>
