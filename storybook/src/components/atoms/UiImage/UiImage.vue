<script setup lang="ts">
import { UiImageFetchPriority, UiImagefallBackImage } from "./enums"
import { type UiImageLink, type UiImageMedia, type UiImageMediaUnit } from "./types"
import { computed, ref } from "vue"
import { type UiImageProps } from "./interface"
import { generateSrcSet } from "../../../helpers/srcSet"
import logo from "../../../assets/logos/fallBackLogo.svg?url"
import logoWithBackground from "../../../assets/logos/fallBackLogoWithBackground.svg?url"
import { useHead } from "@vueuse/head"
import { useI18n } from "vue-i18n"

const props = withDefaults(defineProps<UiImageProps>(), {
  fallBackImage: UiImagefallBackImage.DEFAULT,
  fetchPriority: UiImageFetchPriority.AUTO,
  height: 260, // this is the usual smallest size that will be loaded on mobiles.
  width: 346 // this is the usual smallest size that will be loaded on mobiles.
})

const { t } = useI18n()

const activeImageSrc = ref(props.src)

const activeAlt = ref(props.alt)

const handleError = () => {
  if (logo) {
    activeImageSrc.value = props.fallBackImage === UiImagefallBackImage.DEFAULT ? logo : logoWithBackground
    activeAlt.value = t("ui.atoms.ui_image.default")
  } else {
    activeImageSrc.value = ""
    activeAlt.value = t("ui.atoms.ui_image.missing")
  }
}

const preloadLinks = computed<UiImageLink[]>(() => {
  let links: UiImageLink[] = []
  if (!props.srcSet?.medias[0] && !(props.preload && activeImageSrc.value)) {
    return []
  } else if (!props.srcSet?.medias[0] && activeImageSrc.value) {
    links = [
      {
        as: "image",
        href: activeImageSrc.value,
        rel: "preload"
      }
    ]
  } else {
    Object.entries(props.srcSet?.medias[0] as UiImageMediaUnit).forEach(([key]) => {
      links.push({
        as: "image",
        href: props.srcSet?.medias[0][key] as string,
        rel: "preload"
      })
    })
  }

  return links as UiImageLink[]
})

const generatedSrcSet = props.srcSet?.medias ? generateSrcSet(props.srcSet as UiImageMedia) : undefined

// this is the defaut value that tells the browser:
// "when you use an image, it’s going to be the entire width of the viewport".
const generatedSizes = "100vw"

useHead(() => ({
  link: preloadLinks.value
}))
</script>

<template>
  <picture class="Image">
    <source v-if="generatedSrcSet && generatedSizes" :srcset="generatedSrcSet" :sizes="generatedSizes" />
    <img
      class="Image__img"
      :alt="activeAlt"
      :fetchpriority="fetchPriority"
      :height="height"
      :loading="loading"
      :src="activeImageSrc"
      :width="width"
      @error="handleError"
    />
  </picture>
</template>

<style lang="scss" scoped src="./UiImage.scss"></style>
