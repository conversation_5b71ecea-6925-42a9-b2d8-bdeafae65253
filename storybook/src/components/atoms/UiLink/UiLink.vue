<script setup lang="ts">
import { computed, resolveComponent } from "vue"
import { AdsLink } from "@accor/ads-components"
import { type UiLinkProps } from "./interface"
import { UiLinkVariant } from "./enums"

const props = withDefaults(defineProps<UiLinkProps>(), {
  variant: UiLinkVariant.DEFAULT
})

const routerLink = resolveComponent("router-link")

const computedTag = computed(() => {
  if (props.to) return routerLink
  if (props.href) return "a"
  return "button"
})
</script>

<template>
  <div class="Link" :class="[`Link--${variant}`, { 'Link--uppercase': uppercase }]">
    <slot name="preprend-content" />

    <AdsLink v-bind="props" :as="computedTag" />

    <slot name="append-content" />
  </div>
</template>

<style lang="scss" scoped src="./UiLink.scss"></style>
