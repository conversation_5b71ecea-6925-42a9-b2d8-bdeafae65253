<script setup lang="ts">
import { computed, ref } from "vue"
import { AdsInput } from "@accor/ads-components"
import UiIcon from "../../atoms/UiIcon/UiIcon.vue"
import { type UiInputProps } from "./interface"
import { UiInputStatus } from "./enums"
import { getFirstFocusableElement } from "../../../helpers/accessibility"

// Using the AdsInput from https://ads-components.netlify.app/story/src-atoms-input-doc-input-story-js
const props = withDefaults(defineProps<UiInputProps>(), {
  isClearable: false,
  isLabelHidden: false,
  type: "text"
})

const emit = defineEmits([
  "UiInput::blur",
  "UiInput::clear",
  "UiInput::focus",
  "UiInput::input",
  "UiInput::keydown",
  "UiInput::change"
])

const input = ref<HTMLElement>()
const button = ref<HTMLElement>()
const inputValue = defineModel<string>({ default: "" })

const variantClasses = computed(() => {
  return `
    ${props.status === UiInputStatus.ERROR ? "ads-input--error" : ""}
    ${props.status === UiInputStatus.SUCCESS ? "ads-input--success" : ""}
  `
})

const clearInput = () => {
  inputValue.value = ""
  if (input.value) {
    getFirstFocusableElement(input.value)?.focus()
  }
  emit("UiInput::clear")
}
</script>

<template>
  <div ref="input" class="Input" :class="{ 'Input--hidden-label': isLabelHidden || forceAssistive }">
    <p v-if="forceAssistive" class="caption-01 pb-4 color-caviarBlack-500">
      {{ forceAssistive }}
    </p>

    <AdsInput
      v-model="inputValue"
      v-bind="props"
      :wrapper-attributes="{ class: variantClasses }"
      :size="props.size"
      @input="emit('UiInput::input', $event)"
      @focus="emit('UiInput::focus', $event)"
      @blur="emit('UiInput::blur', $event)"
      @keydown="emit('UiInput::keydown', $event)"
      @change="emit('UiInput::change', $event)"
    >
      <template #prepend-inner-icon>
        <slot name="prepend-inner-icon"></slot>
      </template>
      <template #append-inner-icon>
        <button
          v-if="inputValue && isClearable"
          ref="button"
          class="Input__button"
          :aria-label="$t('ui.atoms.ui_input.aria_button')"
          @click="clearInput"
        >
          <UiIcon name="close" />
        </button>
        <slot name="append-inner-icon"></slot>
      </template>
    </AdsInput>
  </div>
</template>

<style lang="scss" scoped src="./UiInput.scss"></style>
