<script setup lang="ts">
import { AdsSkeleton } from "@accor/ads-components"
import type { UiSkeletonProps } from "../../atoms/UiSkeleton/interface"

withDefaults(defineProps<UiSkeletonProps>(), {
  height: "10rem",
  width: "100%"
})
</script>

<template>
  <div class="Skeleton" :class="{ 'Skeleton--darker': darker }">
    <AdsSkeleton />
  </div>
</template>

<style lang="scss" scoped>
@use "sass:map";
@use "@sb-config/colors";

.Skeleton {
  line-height: 0;

  :deep(.ads-skeleton) {
    height: v-bind("height");
    width: v-bind("width");
    border-radius: v-bind("borderRadius");
    background: linear-gradient(
        -70deg,
        map.get(colors.$caviarBlack, "200") 0%,
        map.get(colors.$caviarBlack, "200") 40%,
        map.get(colors.$caviarBlack, "100") 50%,
        map.get(colors.$caviarBlack, "200") 60%,
        map.get(colors.$caviarBlack, "200") 100%
      )
      repeat;
    background-size: 400% 100%;
  }

  &--darker {
    :deep(.ads-skeleton) {
      background: linear-gradient(
          -70deg,
          map.get(colors.$caviarBlack, "300") 0%,
          map.get(colors.$caviarBlack, "300") 40%,
          map.get(colors.$caviarBlack, "200") 50%,
          map.get(colors.$caviarBlack, "300") 60%,
          map.get(colors.$caviarBlack, "300") 100%
        )
        repeat;
      background-size: 400% 100%;
    }
  }
}
</style>
