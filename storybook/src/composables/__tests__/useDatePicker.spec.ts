/* eslint-disable vue/one-component-per-file */
import { afterEach, beforeEach, describe, expect, it, vitest } from "vitest"

import { defineComponent } from "vue"
import { mount } from "@vue/test-utils"
import { useDatePicker } from "../useDatePicker"

describe("useDatePicker", () => {
  describe("computeDate", () => {
    afterEach(() => {
      vitest.useRealTimers()
    })

    beforeEach(() => {
      vitest.useFakeTimers()
      // this is now now
      vitest.setSystemTime(new Date("2025-06-21T14:37:52.123Z"))
    })

    it("should compute valid dates in Paris", () => {
      // We need this because we have a useI18n inside the useDatePicker
      mount(
        defineComponent({
          setup() {
            // since node13+, setting this env var will dynamically update the global tz
            // @see https://github.com/nodejs/node/pull/20026
            process.env.TZ = "Europe/Paris"

            const { computedDate } = useDatePicker()
            expect(computedDate(new Date("2025-06-21T16:00:00.000Z"))).toBe("Today")
            // 2025-06-22 01:59:59 Paris time
            expect(computedDate(new Date("2025-06-21T23:59:59.000Z"))).toBe("Tomorrow")
            expect(computedDate(new Date("2025-05-03T09:32:00.000Z"))).toBe("Sat, May 03")
          }
        })
      )
    })

    it("should compute valid dates in Los Angeles", () => {
      mount(
        defineComponent({
          setup() {
            process.env.TZ = "America/Los_Angeles"

            const { computedDate } = useDatePicker()
            expect(computedDate(new Date("2025-06-21T03:00:00.000Z"))).toBe("Fri, Jun 20")
            expect(computedDate(new Date("2025-06-21T16:00:00.000Z"))).toBe("Today")
            expect(computedDate(new Date("2025-06-21T23:59:59.000Z"))).toBe("Today")

            // not yet tomorrow
            expect(computedDate(new Date("2025-06-22T03:59:59.000Z"))).toBe("Today")
            expect(computedDate(new Date("2025-06-22T07:01:00.000Z"))).toBe("Tomorrow")

            expect(computedDate(new Date("2025-05-03T09:32:00.000Z"))).toBe("Sat, May 03")
          }
        })
      )
    })
  })
})
