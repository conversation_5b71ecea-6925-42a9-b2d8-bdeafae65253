import type { StorybookConfig } from "@storybook/vue3-vite"

const config: StorybookConfig = {
  addons: [
    "@storybook/addon-onboarding",
    "@storybook/addon-essentials",
    "@chromatic-com/storybook",
    "@storybook/addon-interactions",
    "@storybook/addon-viewport"
  ],
  docs: {},
  framework: {
    name: "@storybook/vue3-vite",
    options: {}
  },
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  async viteFinal(config) {
    // Merge custom configuration into the default config
    const { mergeConfig } = await import("vite")

    return mergeConfig(config, {
      // Add dependencies to pre-optimization
      optimizeDeps: {
        exclude: [
          "@storybook/addon-mdx-gfm",
          "@chromatic-com/storybook",
          "@storybook/addon-essentials",
          "@storybook/addon-interactions",
          "@storybook/addon-links",
          "@storybook/blocks",
          "@storybook/components",
          "@storybook/test",
          "@storybook/vue3",
          "@storybook/vue3-vite",
          "@storybook/theming"
        ]
      },
      server: {
        allowedHosts: ["storybook.fairmont.local"]
      }
    })
  }
}
export default config
