{"name": "luxury-booking", "version": "0.17.0", "description": "Luxury Booking funnel main repository", "main": "index.js", "type": "module", "scripts": {"dev": "bash ./up.sh", "dev:install": "bash ./up.sh FORCE_INSTALL", "build": "pnpm -r run build", "test": "vitest run --coverage", "test:unit": "vitest run", "test:all": "pnpm -r run test:unit --coverage", "test:merge": "lcov-result-merger '**/coverage/lcov.info' > coverage/lcov_workspace.info", "pack": "node create_pack.cjs", "clean": "pnpm -r run clean && pnpm clean:root", "clean:root": "rm -rf node_modules && rm -rf pnpm-store && rm -rf coverage && echo 'Workspace cleaned.'", "prepare": "husky", "eslint:check": "eslint ./ --max-warnings=0", "eslint:fix": "eslint ./ --max-warnings=0 --fix", "prettier:check": "prettier --check ./", "prettier:write": "prettier --write ./", "check:fix": "pnpm run eslint:fix && pnpm run prettier:write"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.12.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.2", "@vitejs/plugin-vue": "^5.1.4", "@vitest/coverage-v8": "^2.1.3", "@vitest/eslint-plugin": "1.1.7", "@vue/compiler-dom": "^3.5.13", "@vue/eslint-config-prettier": "^10.0.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "dotenv": "^16.4.5", "dotenv-expand": "^12.0.1", "eslint": "^9.15.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-playwright": "^1.7.0", "eslint-plugin-vue": "^9.29.0", "happy-dom": "^16.7.3", "husky": "^9.1.7", "jsdom": "^25.0.1", "lcov-result-merger": "^5.0.1", "npm-run-all2": "^7.0.2", "prettier": "^3.3.3", "typescript": "^5.7.2", "typescript-eslint": "^8.21.0", "vite": "^6.0.2", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-vue-devtools": "^7.6.8", "vitest": "^2.1.2", "vue-tsc": "^2.1.10"}, "dependencies": {"@accor/ads-components": "^2.5.1", "@accor/ads-components-locales": "^0.2.3", "@accor/shared-utils": "^1.2.5", "@vueuse/head": "^2.0.0", "deepmerge": "^4.3.1", "luxury-booking": "file:", "pinia": "^2.2.4", "vue": "^3.5.12", "vue-i18n": "^9.9.4", "zod": "^3.24.2"}, "engines": {"node": ">=20 || >21", "pnpm": ">=9"}}