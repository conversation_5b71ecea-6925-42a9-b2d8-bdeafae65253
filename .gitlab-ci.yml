workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-skip\]/
      when: never
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-release\]/ && $CI_COMMIT_BRANCH == 'main'
      when: never
    - when: always

#Include devops
include:
  - project: "devops/features/pipelines/runner/k8s"
    ref: "master"
    file: "full-k8s-conf.gitlab-ci.yml"
  - project: devops/features/deployment/nexus-raw-uploader
    ref: "v19.17.0"
    file: nexus-raw-upload.gitlab-ci.yml
  - project: ecom/front/sf-management/front-common-ci
    ref: "main"
    file: gitflow.gitlab-ci.yml
  - project: ecom/front/sf-management/front-common-ci
    ref: "main"
    file: gitflow-versionning.gitlab-ci.yml

stages:
  - ".pre"
  - install
  - test
  - quality
  - build
  - tag
  - publish
  - remote_trigger
  - scm
  - cleanup
  - delivery
  - notification
  - ".post"

variables:
  NODE_BUILD_IMAGE: "registry.softfactory-accor.net/devops/tooling/docker-images/node-build:22.3-alpine3.19"
  ASSET_NAME: "fairmont-booking"
  SONAR_EXCLUSIONS_LIST: "**/node_modules/**,**/coverage/**,**/reports/**,test.html,**/test/**,**/__tests__/**,**/*.config.*,./*,*.js,*.cjs,**/Dockerfile"

.npm-test:
  artifacts:
    paths:
      - coverage/lcov.info
      - coverage/lcov-report/index.html

quality-full-master:
  variables:
    SONAR_EXCLUSIONS: "${SONAR_EXCLUSIONS_LIST}"
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/

quality-full-tags:
  variables:
    SONAR_EXCLUSIONS: "${SONAR_EXCLUSIONS_LIST}"

trivy:scan-master:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/

checkmarx-sast:scan-master:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/

checkmarx-sca:scan-master:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/

release-candidate-tag:
  rules:
    - if: $CI_COMMIT_BRANCH =~ /-release$/ && $CI_COMMIT_MESSAGE !~ /^\[ci-npm/
      when: on_success
      allow_failure: true

install-feature:
  stage: install
  before_script:
    # Install pnpm globally
    - npm install -g pnpm
  script:
    # Install dependencies using pnpm (instead of npm ci)
    - pnpm install --frozen-lockfile
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^(release\/|feature\/|bugfix\/|hotfix\/)/
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

install-master:
  extends: install-feature
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/
      when: on_success

install-tags:
  extends: install-master
  rules:
    - if: "$CI_COMMIT_TAG"
      when: on_success

build-feature:
  stage: build
  before_script:
    # Install pnpm globally
    - npm install -g pnpm
    - pnpm install --frozen-lockfile
  script:
    - pnpm build --mode "$PROJECT_ENV"
  artifacts:
    paths:
      - "**/dist/"
    expire_in: 1 hour
  dependencies:
    - install-feature
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^(feature\/|bugfix\/|hotfix\/)/
      variables:
        PROJECT_ENV: "development" # probably needs to switch to recette in technical task
    - if: $CI_COMMIT_REF_NAME =~ /^(release\/)/
      variables:
        PROJECT_ENV: "recette"
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
      variables:
        PROJECT_ENV: "development" # probably needs to switch to recette in technical task

build-master:
  extends: build-feature
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main$)$/
      variables:
        PROJECT_ENV: "production"
      when: on_success
    - if: $CI_COMMIT_REF_NAME =~ /^(develop)$/
      variables:
        PROJECT_ENV: "recette"
      when: on_success

build-tags:
  extends: build-master
  rules:
    - if: "$CI_COMMIT_TAG"
      when: on_success

build-storybook:
  stage: build
  extends:
    - .npm-install
  before_script:
    - npm install -g pnpm
    - pnpm install --frozen-lockfile
  script:
    - pnpm --filter ./storybook build-storybook
  artifacts:
    paths:
      - "storybook/storybook-static"
    expire_in: 1 hour
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

test-feature:
  stage: test
  extends: .npm-test
  before_script:
    # Install pnpm globally
    - npm install -g pnpm
    - pnpm install --frozen-lockfile
  script:
    - pnpm test
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^(release\/|feature\/|bugfix\/|hotfix\/)/
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

test-master:
  extends: test-feature
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main$|develop)$/
      when: on_success

test-tags:
  extends: test-master
  rules:
    - if: "$CI_COMMIT_TAG"
      when: on_success

quality-diff-feature:
  variables:
    SONAR_EXCLUSIONS: "${SONAR_EXCLUSIONS_LIST}"
    SONAR_LCOV_FILE: "${CI_PROJECT_DIR}/coverage/lcov.info"
  needs:
    - test-feature
  allow_failure: true
  rules:
    - if: "$CI_MERGE_REQUEST_ID != null"

sonarqube-check:
  stage: quality
  variables:
    SONAR_EXCLUSIONS: "${SONAR_EXCLUSIONS_LIST}"
    SONAR_LCOV_FILE: "${CI_PROJECT_DIR}/coverage/lcov.info"
  extends: .npm-sonar
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^(feature|hotfix|bugfix|release)\//'
      when: on_success

npm-raw-publish:
  stage: publish
  extends:
    - .nexus-raw-upload
  variables:
    ASSET_NAME: "fairmont-booking"
  before_script:
    - apk add --no-cache nodejs npm
    - npm install -g pnpm
    - pnpm install --frozen-lockfile
    - BRANCH_TYPE=$(echo $CI_COMMIT_REF_NAME | grep -E -o '(feature|hotfix|bugfix|release)/') || true
    - JIRA_ID=$(echo $CI_COMMIT_REF_NAME | grep -E -o '/[A-Z]+-[0-9]+' | cut -d "/" -f2) || true
    - echo "Branch type $BRANCH_TYPE JIRA ID $JIRA_ID"
    - |
      if [ -n "$JIRA_ID" ] ; then
        echo JIRA ID $JIRA_ID
        FILE_NAME="${ASSET_NAME}-${JIRA_ID}.tgz"
      elif [ "$BRANCH_TYPE" = 'release/' ] ; then
        RELEASE_VERSION=$(echo $CI_COMMIT_REF_NAME | grep -E -o '/[0-9]+\.[0-9]+\.[0-9]+' | cut -d "/" -f2 | tr . _)
        FILE_NAME="${ASSET_NAME}-RC-${RELEASE_VERSION}.tgz"
      elif [ "$CI_COMMIT_BRANCH" = 'develop' ] ; then
        echo "$CI_COMMIT_BRANCH"
        FILE_NAME="${ASSET_NAME}.tgz"
      fi
    - |
      if [ $FILE_NAME ]; then
          echo "FILE_NAME $FILE_NAME"
          pnpm run pack Fairmont
          NEXUS_SOURCE_FILE=$(ls | grep -E -o "^${ASSET_NAME}-[0-9]+\.[0-9]+\.[0-9]+[RC0-9\.\-]*\.tgz$")
          NEXUS_TARGET_FILE="@accor/${ASSET_NAME}/${BRANCH_TYPE}${FILE_NAME}"
      else
          echo "FILE_NAME is not set, no need to upload"
      fi
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && '$CI_COMMIT_BRANCH =~ /^(feature|hotfix|bugfix|release)\//'
    - if: $CI_COMMIT_BRANCH == 'develop'
      when: on_success
  artifacts:
    paths:
      - "*.tgz"
    expire_in: 30 minutes

nexus-publish:
  extends: .runner-2xlarge
  stage: publish
  variables:
    ASSET_NAME: "fairmont-booking"
  before_script:
    - apk add --no-cache nodejs npm
    - npm install -g pnpm
    - pnpm install --frozen-lockfile
    - pnpm run pack Fairmont
    - export NEXUS_SOURCE_FILE=$(ls | grep -E -o "^${ASSET_NAME}-[0-9]+\.[0-9]+\.[0-9]+[RC0-9\.\-]*\.tgz$")
    - export NEXUS_TARGET_FILE="@accor/${ASSET_NAME}/${NEXUS_SOURCE_FILE}"
    - echo "$NEXUS_SOURCE_FILE"
    - echo "$NEXUS_TARGET_FILE"
    - ls -al *.tgz
  script:
    #- export SF_NEXUS_URL=${SF_NEXUS_URL%*/}
    - echo "registry=${SF_NEXUS_URL}/repository/${SF_NEXUS_NPM_REPO}/" > .npmrc
    # configure nexus auth token
    - echo "$(echo ${SF_NEXUS_URL} | sed 's/https://')/repository/${SF_NEXUS_NPM_REPO}/:_authToken=${SF_NEXUS_NPM_TOKEN}" >> .npmrc
    - pnpm publish ${NEXUS_SOURCE_FILE} --no-git-checks
    - touch $CI_PROJECT_DIR/success
  artifacts:
    paths:
      - "*.tgz"
    expire_in: 30 minutes
  rules:
    - if: $CI_COMMIT_BRANCH == 'main'
      when: on_success

nexus-publish-tag:
  extends: nexus-publish
  rules:
    - if: "$CI_COMMIT_TAG"
      when: on_success
  artifacts:
    paths:
      - "*.tgz"

patch-versions:
  stage: tag
  extends: release-branch-auto
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0 # Full git history needed
  script:
    - npm install -g pnpm
    - node update-version.cjs minor
    - |
      if [[ -n "$(git status --porcelain)" ]]; then
        # Stage and commit the changes
        git restore ./package.json

        git status

        git commit -am "[ci-npm-skip] Increment pnpm workspace versions"
        git log --oneline -5
        
        # Push to the source branch of the merge request
        git push origin HEAD:$CI_COMMIT_BRANCH
      else
        echo "No version changes needed"
      fi
  rules:
    - if: $CI_COMMIT_BRANCH == 'develop' && $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      allow_failure: true

release-tag:
  extends:
    - .npm-create-release
  rules:
    - if: $CI_COMMIT_BRANCH == 'main'
      when: manual
      allow_failure: true

deploy-branch:
  stage: remote_trigger
  trigger:
    project: "rbac/cd/gdp/brands/brandsbooking/infrastructure/booking-fairmont"
    branch: "dev"
  needs: ["npm-raw-publish"]
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && '$CI_COMMIT_BRANCH =~ /^release\//'
      when: on_success
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && '$CI_COMMIT_BRANCH =~ /^(feature|hotfix|bugfix)\//'
      when: manual
      allow_failure: true
    - if: $CI_COMMIT_BRANCH == 'develop'
      when: on_success

deploy-storybook:
  stage: publish
  script:
    - mv storybook/storybook-static public
  artifacts:
    paths:
      - public
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
  when: manual
  allow_failure: true

pages:
  # temporary fix
  stage: install
  extends:
    - .npm-install
  before_script:
    - npm install -g pnpm
    - pnpm install --frozen-lockfile
  script:
    - pnpm --filter ./storybook build-storybook
    - mv storybook/storybook-static public
  artifacts:
    paths:
      - public
  rules:
    - if: $CI_COMMIT_BRANCH == 'develop'
