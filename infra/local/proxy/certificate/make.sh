#!/bin/sh

set -e

# Generate a local CA and a local certificate
#
# @see https://deliciousbrains.com/ssl-certificate-authority-for-local-https-development/
#
# - key password is `pass`
# - see the end of this file to update the list of domains to validate
# - tip: @see https://nip.io on how to use their dynamic DNS service

here="$(cd "$(dirname "$0")" && pwd)"
cd "${here}"

subj="/C=FR/ST=Herault/L=Montpellier /O=Kaliop /CN=www.kaliop.com /emailAddress=<EMAIL>"

echo "1. CA"
if [ ! -f "CA.key" ]; then
  openssl genrsa -des3 -out CA.key -passout pass:pass 2048
fi
if [ ! -f "CA.crt" ]; then
  openssl req -x509 -new -nodes \
    -key CA.key \
    -passin pass:pass \
    -sha256 \
    -days 1825 \
    -subj "${subj}" \
    -out CA.crt
fi

echo "2. website certificate"
if [ ! -f "../conf.d/server.key" ]; then
  openssl genrsa -out ../conf.d/server.key 2048
fi
if [ ! -f "server.csr" ]; then
  openssl req -new \
    -key ../conf.d/server.key \
    -passin pass:pass \
    -out server.csr \
    -subj "${subj}"
fi

if [ ! -f "server.ext" ]; then
  cat > server.ext <<EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
IP.1 = 127.0.0.1
DNS.1 = localhost
DNS.2 = *.127.0.0.1.nip.io
DNS.3 = *.fairmont.local
EOF
fi

if [ ! -f "../conf.d/server.crt" ]; then
  openssl x509 -req \
    -in server.csr \
    -CA CA.crt \
    -CAkey CA.key \
    -CAcreateserial \
    -passin pass:pass \
    -out ../conf.d/server.crt \
    -days 825 \
    -sha256 \
    -extfile server.ext
fi

