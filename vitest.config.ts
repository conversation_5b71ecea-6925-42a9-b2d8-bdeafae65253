import { configDefaults, defineConfig, mergeConfig } from "vitest/config"
import viteConfig from "./vite.config"

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      coverage: {
        all: true,
        exclude: [
          "**/node_modules/**",
          "**/coverage/**",
          "**/e2e/**",
          "**/*.config.*",
          "**/*.eslintrc.cjs",
          "**/__tests__/*",
          "**/dist/*",
          "create_pack.js",
          "./*"
        ],
        provider: "v8",
        reporter: ["text", "lcov", "json"],
        reportsDirectory: "coverage"
      },
      environment: "jsdom",
      exclude: [
        ...configDefaults.exclude,
        "**/e2e/**",
        "**/*.config.*",
        "**/*config.*",
        "**/coverage/",
        "**/dist/*",
        "**/**" //Exclude all files in workspace root
      ],
      globals: false

      //root: fileURLToPath(new URL("./", import.meta.url)),
    }
  })
)
