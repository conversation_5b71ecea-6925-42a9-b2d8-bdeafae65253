/* eslint-disable sort-keys */
import { URL, fileURLToPath } from "node:url"
import { defineConfig, loadEnv } from "vite"
import copy from "rollup-plugin-copy"
import svgLoader from "vite-svg-loader"
import { visualizer } from "rollup-plugin-visualizer"
import vue from "@vitejs/plugin-vue"
//import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const base_url = env.VITE_BASE_URL

  return {
    base: base_url,
    build: {
      assetsDir: "",
      outDir: `dist/${base_url}`,
      sourcemap: true,
      target: "es2022",
      rollupOptions: {
        output: {
          manualChunks(id: string) {
            const chunkMap = new Map([
              // Dates
              ["date-fns", "dates"],
              ["datepicker", "dates"],
              // Monitoring
              ["opentelemetry", "monitoring"],
              ["splunk", "monitoring"],
              // Accor design system
              ["@accor", "accor-design-system"],
              // Localizations
              ["i18n", "localizations"],
              ["intlify", "localizations"],
              // Forms
              ["zod", "forms"],
              // Storybook
              ["storybook/src/components", "design-system"],
              // Catch all vendors
              ["node_modules", "vendors"]
            ])

            const chunkKey = Array.from(chunkMap.keys()).find((key) => id.toLowerCase().includes(key))

            if (chunkKey) {
              return chunkMap.get(chunkKey)
            }

            return null
          }
        }
      }
    },
    css: {
      preprocessorOptions: {
        sass: {
          // api: "modern"
          silenceDeprecations: ["legacy-js-api"]
        },
        scss: {
          // api: "modern"
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    },
    plugins: [
      vue(),
      svgLoader(),
      {
        ...copy({
          hook: "never",
          targets: [
            {
              dest: "public/assets/locales",
              src: "../../node_modules/@accor/ads-components-locales/src/locales/*"
            }
          ]
        }),
        apply: "build"
      },
      {
        name: "html-transform",
        transformIndexHtml(html: string) {
          const prefetchUrls = [process.env.VITE_APP_API_BASE_URL!].filter(Boolean) as string[]
          const prefetchLinks = prefetchUrls.map((url) => `<link rel="dns-prefetch" href="${url.trim()}">`).join("\n")

          const preconnectLinks = prefetchUrls.map((url) => `<link rel="preconnect" href="${url.trim()}">`).join("\n")

          const linksCode = `
    ${preconnectLinks}
    ${prefetchLinks}
        `
          return html.replace("<!-- PREFETCH_PLACEHOLDER -->", linksCode)
        }
      },
      visualizer()
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
        "@stores": fileURLToPath(new URL("../Common/src/stores", import.meta.url)),
        "@relative-assets": fileURLToPath(new URL("./src/assets", import.meta.url)),
        "@sb-assets": fileURLToPath(new URL("../../storybook/src/assets", import.meta.url)),
        "@sb-base": fileURLToPath(new URL("../../storybook/src/styles/base", import.meta.url)),
        "@sb-components": fileURLToPath(new URL("../../storybook/src/components", import.meta.url)),
        "@sb-composables": fileURLToPath(new URL("../../storybook/src/composables", import.meta.url)),
        "@sb-config": fileURLToPath(new URL("../../storybook/src/styles/config", import.meta.url)),
        "@sb-fonts": fileURLToPath(new URL("../../storybook/public/fonts", import.meta.url)),
        "@sb-helpers": fileURLToPath(new URL("../../storybook/src/helpers", import.meta.url)),
        "@sb-i18n": fileURLToPath(new URL("../../storybook/src/i18n", import.meta.url)),
        "@sb-styles": fileURLToPath(new URL("../../storybook/src/styles", import.meta.url)),
        "@sb-utilities": fileURLToPath(new URL("../../storybook/src/styles/utilities", import.meta.url))
      }
    },
    server: {
      allowedHosts: ["booking.common.local"],
      host: "0.0.0.0",
      port: 3001,
      proxy: {
        "/api": {
          changeOrigin: true, // Update the origin of the request
          cookieDomainRewrite: "", // or set to your development server domain
          cookiePathRewrite: "/",
          rewrite: (path: string) => path.replace(/^\/api/, ""), // Remove '/api' prefix from the URL
          secure: true, // For self-signed certificates, set to false
          target: env.VITE_APP_API_BASE_URL // API server URL
        }
      }
    }
  }
})
