import { configDefaults, defineConfig, mergeConfig } from "vitest/config"
import { fileURLToPath } from "node:url"
import viteConfig from "./vite.config"

export default defineConfig((configEnv) => {
  const root = fileURLToPath(new URL("./", import.meta.url))

  return mergeConfig(
    viteConfig(configEnv),
    defineConfig({
      test: {
        environment: "happy-dom",
        exclude: [...configDefaults.exclude, "**/e2e/**"],
        root,
        server: {
          deps: {
            inline: ["@accor/ads-components", "@accor/icons", "@accor/shared-utils"]
          }
        },
        setupFiles: ["./vitest.setup.ts", "../../tools/tsconfig/vitest.setup.ts"]
      }
    })
  )
})
