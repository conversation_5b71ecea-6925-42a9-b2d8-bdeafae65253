{"name": "@apps/common", "version": "0.15.0", "private": false, "type": "module", "scripts": {"clean": "rm -rf node_modules && rm -rf dist", "dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@shared/components": "workspace:*", "@shared/composables": "workspace:*", "@shared/payment": "workspace:*", "@shared/types": "workspace:*", "@shared/utils": "workspace:*", "@splunk/otel-web": "^0.22.0", "@vueuse/core": "^12.5.0", "@vueuse/integrations": "^13.1.0", "@vueuse/router": "^13.1.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "design-system": "workspace:*", "include-media": "^2.0.0", "js-cookie": "^3.0.5", "normalize.css": "^8.0.1", "pinia": "^2.2.4", "qs": "^6.14.0", "sass": "^1.83.4", "universal-cookie": "^7", "vue-router": "^4.4.5", "zod": "^3.24.3"}, "devDependencies": {"@playwright/test": "^1.48.0", "@tools/tsconfig": "workspace:*", "@types/js-cookie": "^3.0.6", "@types/qs": "^6.9.18", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-visualizer": "^6.0.1", "sass-embedded": "^1.89.2", "vite-svg-loader": "^5.1.0"}}