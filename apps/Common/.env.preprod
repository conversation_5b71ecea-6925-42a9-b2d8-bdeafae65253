VITE_APP_PAYMENT_API_KEY=MISSING-INFORMATION #TODO update with correcte value
VITE_APP_PAYMENT_IFRAME_MODULE=https://card-module-payment.accor.com/builder/payment-card-module-builder.js
VITE_APP_API_BASE_URL=/booking/api/
## OIDC APIs
VITE_OIDC_SIGN_IN_URI=https://rec-login.accor.com/as/authorization.oauth2?client_id=com.myfairmont&persistent=yes&response_type=code&scope=openid%20PROFILE.READ%20PROFILE.WRITE%20PROFILE.WALLET&redirect_uri={BASE_URI}/booking/oidc/redirect
VITE_OIDC_SIGN_UP_URI=https://rec-login.accor.com/as/authorization.oauth2?client_id=com.myfairmont&persistent=yes&response_type=code&scope=openid%20PROFILE.READ%20PROFILE.WRITE%20PROFILE.WALLET&ui=%7B%22brand%22%3A%7B%22name%22%3A%22F<PERSON><PERSON>%22%2C%22logoSrc%22%3A%22https%3A%2F%2Fuatweb3.myfairmont.com%2Ffrhi%2Finc%2Fimg%2Ffairmont-logo-300.svg%22%2C%22logoWidth%22%3A300%7D%2C%22lcahLoginLinkDisplay%22%3Atrue%2C%22lcahOptinDefault%22%3Atrue%2C%22lcahOptinDisplay%22%3Atrue%2C%22rememberMeDisplay%22%3Atrue%2C%22buttonRegisterDisplay%22%3Atrue%2C%22newsLettersOptinCodes%22%3A%5B%22FRM%22%2C%22ALL%22%5D%2C%22newsLettersOptinDefault%22%3Atrue%2C%22newsLettersOptinDisplay%22%3Atrue%2C%22cancelButtonDisplay%22%3Afalse%2C%22socialNetworkDisplay%22%3Atrue%2C%22styles%22%3A%7B%22primaryButtonBackgroundColor%22%3A%22%23393939%22%2C%22primaryButtonColor%22%3A%22%23ffffff%22%2C%22secondaryButtonBackgroundColor%22%3A%22%23ffffff%22%2C%22secondaryButtonColor%22%3A%22%23393939%22%2C%22cancelButtonBackgroundColor%22%3A%22%23707070%22%2C%22cancelButtonColor%22%3A%22%23ffffff%22%7D%7D&accorregister=true&redirect_uri={BASE_URI}/booking/oidc/redirect