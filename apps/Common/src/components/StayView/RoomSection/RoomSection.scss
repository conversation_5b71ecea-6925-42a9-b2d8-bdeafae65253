@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Room-section {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "8");

  @include mq.media(">=small") {
    gap: map.get(spaces.$sizes, "10");
  }

  &__title-content {
    display: flex;
    flex-direction: column;
    margin: 0 -1.6rem;
    padding: map.get(spaces.$sizes, "5") 1.8rem;
    position: sticky;
    top: 0;
    z-index: 1;
    gap: map.get(spaces.$sizes, "4");
    background-color: map.get(colors.$pearlGrey, "200");

    @include mq.media(">=small") {
      flex-direction: row;
      position: relative;
      z-index: initial;
      gap: map.get(spaces.$sizes, "5");
      margin: initial;
      padding: 0;
      background-color: unset;
      align-items: center;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "7");

    @include mq.media(">=small") {
      gap: map.get(spaces.$sizes, "8");
    }
  }

  &__title {
    @include text.lbf-text("heading-03");

    @include mq.media(">=small") {
      font-size: 2rem;
    }
  }

  &__ellipsis {
    display: none;
    color: map.get(colors.$caviarBlack, "500");

    @include mq.media(">=small") {
      display: initial;
      font-size: 0.4rem;
    }
  }

  &__subtitle {
    @include text.lbf-text("caption-01-uppercase");

    color: map.get(colors.$caviarBlack, "500");
  }

  &__room-card-content {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "6");

    @include mq.media(">=small") {
      gap: map.get(spaces.$sizes, "8");
    }
  }

  &__button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    color: map.get(colors.$caviarBlack, "900");

    &--icon {
      font-size: 2.4rem;
    }
  }
}
