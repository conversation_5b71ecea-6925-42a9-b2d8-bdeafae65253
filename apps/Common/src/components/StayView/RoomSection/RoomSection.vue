<script setup lang="ts">
import { computed, watch } from "vue"
import { useGA4Event, useSearch } from "../../../composables"
import { DividerDirection } from "@sb-components/atoms/UiDivider/enums"
import { EventNameEnum } from "../../../global/enums"
import RoomCard from "../RoomCard/RoomCard.vue"
import { RoomSectionProps } from "./interface"
import { UiButtonType } from "@sb-components/atoms/UiButton/enums"
import UiDivider from "@sb-components/atoms/UiDivider/UiDivider.vue"
import UiIcon from "@sb-components/atoms/UiIcon/UiIcon.vue"
import UiLink from "@sb-components/atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "@sb-components/atoms/UiLink/enums"
import { useI18n } from "vue-i18n"

type Emits = {
  (event: "choose-offer", payload: { classCode?: string; rateId: string; productCode: string; rateCode: string }): void
}

const props = defineProps<RoomSectionProps>()
defineEmits<Emits>()

const { pushGA4Event } = useGA4Event()
const { rooms } = useSearch()
const { t } = useI18n()

const isExpanded = defineModel<boolean>("expanded")

function toggleExpand() {
  isExpanded.value = !isExpanded.value

  if (!isExpanded.value) {
    pushGA4Event("step2", "select a room", {
      eventName: EventNameEnum.bloc_interact,
      event_data: {
        bloc_interaction: "see other type of rooms",
        bloc_name: "room bloc"
      }
    })
  }
}

const chevronIcon = computed(() => (isExpanded.value ? "chevronUp" : "chevronDown"))

const buttonLabel = computed(() => {
  return isExpanded.value
    ? t("components.stay_view.room_section.reduce_label")
    : t("components.stay_view.room_section.other_room_label", { count: props.pairs.length - 1 })
})

const currentRoom = computed({
  get() {
    const currentIndex = rooms.value.findIndex((room) => !room.productCode || !room.rateId)
    return rooms.value[currentIndex]
  },
  set(newValue) {
    const currentIndex = rooms.value.findIndex((room) => !room.productCode || !room.rateId)
    rooms.value[currentIndex] = newValue
  }
})

watch(
  currentRoom,
  (_newCurrentRoom, oldCurrentRoom) => {
    if (oldCurrentRoom) return
  },
  {
    once: true
  }
)
</script>

<template>
  <div class="Room-section">
    <div class="Room-section__content">
      <div class="Room-section__title-content">
        <h3 class="Room-section__title">{{ title }}</h3>

        <UiIcon class="Room-section__ellipsis" name="ellipsis" />

        <h4 class="Room-section__subtitle">
          {{ $t("components.stay_view.room_section.subtitle", { count: pairs.length }) }}
        </h4>
      </div>

      <RoomCard
        v-if="pairs.length > 0"
        v-model="currentRoom"
        :all-rooms="rooms"
        :offer-id="pairs[0].offerMapped.id"
        :rate-code="pairs[0].offerMapped.rateId"
        :room-card-content="pairs[0].offerMapped.roomCard"
        @choose-offer="$emit('choose-offer', $event)"
      />

      <ul v-show="isExpanded" class="Room-section__room-card-content">
        <li v-for="roomCard in pairs.slice(1)" :key="roomCard.offerMapped.id">
          <RoomCard
            v-model="currentRoom"
            :all-rooms="rooms"
            :offer-id="roomCard.offerMapped.id"
            :rate-code="pairs[0].offerMapped.rateId"
            :room-card-content="roomCard.offerMapped.roomCard"
            @choose-offer="$emit('choose-offer', $event)"
          />
        </li>
      </ul>

      <UiLink
        v-if="pairs.length > 1"
        class="Room-section__button"
        :text="buttonLabel"
        :variant="UiLinkVariant.NEUTRAL"
        :type="UiButtonType.BUTTON"
        uppercase
        @click="toggleExpand"
      >
        <template #append-content>
          <UiIcon class="Room-section__button--icon" :name="chevronIcon" />
        </template>
      </UiLink>
    </div>

    <UiDivider v-if="hasDivider" :direction="DividerDirection.HORIZONTAL" />
  </div>
</template>

<style lang="scss" scoped src="./RoomSection.scss"></style>
