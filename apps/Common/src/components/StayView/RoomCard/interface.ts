import { RoomType } from "@sb-components/organisms/UiRoom/types"
import { UiRoomCardProps } from "@sb-components/organisms/UiRoomCard/interface"

export interface RoomCardProps {
  /**
   * Unique identifier of the offer
   */
  offerId: string
  /**
   * Unique identifier of the rate
   */
  rateCode: string
  /**
   * Properties for the room card, including details like amenities, gallery, image, pricing, and title.
   */
  roomCardContent: UiRoomCardProps
  allRooms: RoomType[]
}
