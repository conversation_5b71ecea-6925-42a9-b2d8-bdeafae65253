<script setup lang="ts">
import { computed, ref, watch } from "vue"
import { useCurrentWindowSize, useGA4Event, useOffersAndAccommodations } from "../../../composables"
import { EventNameEnum } from "../../../global/enums"
import MobileRoomCard from "../MobileRoomCard/MobileRoomCard.vue"
import RateCard from "../RateCard/RateCard.vue"
import { RoomCardProps } from "../RoomCard/interface"
import { RoomType } from "@sb-components/organisms/UiRoom/types"
import UiGallery from "@sb-components/molecules/UiGallery/UiGallery.vue"
import { UiRateCardProps } from "@sb-components/molecules/UiRateCard/interface"
import UiRoomCard from "@sb-components/organisms/UiRoomCard/UiRoomCard.vue"
import UiRoomDetailsModal from "@sb-components/organisms/UiRoomDetailsModal/UiRoomDetailsModal.vue"
import { UiRoomDetailsModalContentProps } from "@sb-components/molecules/UiRoomDetailsModalContent/interface"
import { accommodationMapper } from "../../../services/accommodation/accommodation.mapper"
import { getSelectedRoomRateCards } from "../../../services/offer/offer.fetch"
import { truncateApiTitle } from "@shared/utils"

type Emits = {
  (event: "choose-offer", payload: { classCode: string; rateId: string; productCode: string; rateCode: string }): void
}

const props = defineProps<RoomCardProps>()
defineEmits<Emits>()

const { isMobile } = useCurrentWindowSize()
const { getOfferAccommodationById, offers } = useOffersAndAccommodations()
const { pushGA4Event } = useGA4Event()

const modelValue = defineModel<RoomType>({ required: true })

const isLoading = ref(false)
const modalIsOpen = ref(false)
const cardIsOpen = ref(false)
const rateCards = ref<UiRateCardProps[]>([])
const loadingRateCards = ref(false)
const roomCardModalContent = ref<UiRoomDetailsModalContentProps>()
const isGalleryOpen = ref(false)

const allRooms = computed(() => props.allRooms || [])

const handleRoomCard = async () => {
  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.bloc_interact,
    event_data: {
      bloc_interaction: `select room - ${truncateApiTitle(props.roomCardContent.title)}`,
      bloc_name: "room bloc"
    }
  })

  if (rateCards.value.length === 0) {
    loadingRateCards.value = true
    const selection = allRooms.value
      .map((room) =>
        room.productCode && room.rateCode ? { productCode: room.productCode, rateCode: room.rateCode } : undefined
      )
      .filter(Boolean) as { rateCode: string; productCode: string }[]

    rateCards.value = await getSelectedRoomRateCards(props.roomCardContent.productCode, selection)

    if (rateCards.value.length > 0) {
      toggleCard()
    }
    loadingRateCards.value = false
  } else {
    toggleCard()
  }
}

const handleGalleryClicked = () => {
  isGalleryOpen.value = true

  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.bloc_interact,
    event_data: {
      bloc_interaction: "open gallery",
      bloc_name: "room bloc"
    }
  })
}

watch(
  () => props.roomCardContent.isOpen,
  (isOpen: boolean) => {
    if (isOpen) {
      handleRoomCard()
    }
  },
  { immediate: true }
)

const toggleModal = async () => {
  isLoading.value = true
  modalIsOpen.value = true

  if (!roomCardModalContent.value) {
    const offer = offers.value.find((o) => o.id === props.offerId)
    if (!offer) return

    const accommotation = await getOfferAccommodationById(offer)
    roomCardModalContent.value = accommodationMapper(offer.product, accommotation)
  }

  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.bloc_interact,
    event_data: {
      bloc_interaction: "room details",
      bloc_name: "room bloc"
    }
  })

  isLoading.value = false
}

const toggleCard = () => {
  cardIsOpen.value = !cardIsOpen.value
}

watch(isMobile, (newVal) => {
  if (newVal) {
    cardIsOpen.value = false
  }
})
</script>

<template>
  <div class="Room-card">
    <MobileRoomCard
      v-if="isMobile && cardIsOpen"
      :class-code="roomCardModalContent?.classCode || ''"
      :is-open="cardIsOpen"
      :is-loading="isLoading"
      :offer-id="offerId"
      :rate-cards="rateCards"
      :rate-code="rateCode"
      :room-card-modal-content="roomCardModalContent"
      :room-card="roomCardContent"
      :room="modelValue"
      @mobile-room-card::header-click="toggleCard"
      @choose-offer="$emit('choose-offer', $event)"
      @mobile-room-card::modal-click="toggleModal"
    />

    <UiRoomCard
      v-else
      v-bind="roomCardContent"
      :is-mobile="isMobile"
      :is-open="cardIsOpen"
      :is-loading-rate-cards="loadingRateCards"
      @ui-room-card-details-button::click="toggleModal"
      @ui-room-card-select-button::click="handleRoomCard"
      @ui-room-card-close-button::click="toggleCard"
      @ui-room-card-gallery-button::click="handleGalleryClicked"
    >
      <template #rate-cards>
        <div class="Room-card__rate-cards-title">
          {{ $t("components.stay_view.room_card.rate_title") }}
        </div>

        <RateCard
          v-for="(rateCard, index) in rateCards"
          :key="rateCard.rateCardId"
          :class="{ 'Rate-card__container--first': index === 0 }"
          :class-code="roomCardModalContent?.classCode || ''"
          :content="rateCard"
          :rate-code="rateCode"
          :room="modelValue"
          :product-code="roomCardContent.productCode"
          :rate-id="rateCard.rateCardId"
          @choose-offer="$emit('choose-offer', $event)"
        />
      </template>
    </UiRoomCard>

    <UiGallery
      v-if="roomCardContent.gallery.medias"
      :is-open="isGalleryOpen"
      :images="roomCardContent.gallery.medias"
      :title="$t('components.stay_view.room_card.gallery')"
      @ui-gallery::close="isGalleryOpen = false"
    />

    <UiRoomDetailsModal
      :is-loading="isLoading"
      :is-open="modalIsOpen"
      :room-details-modal-content="roomCardModalContent"
      @room-details-modal::close="modalIsOpen = false"
    />
  </div>
</template>

<style lang="scss" scoped src="./RoomCard.scss"></style>
