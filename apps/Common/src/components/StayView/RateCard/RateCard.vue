<script setup lang="ts">
import { computed, ref } from "vue"
import { useGA4Event, useLoader } from "../../../composables"
import { EventNameEnum } from "../../../global/enums"
import { type ProcessedAmenity } from "@sb-components/molecules/UiAmenitiesCard/interface"
import { RateCardProps } from "./interface"
import UiPricingDetailsModal from "@sb-components/organisms/UiPricingDetailsModal/UiPricingDetailsModal.vue"
import { UiPricingDetailsModalContentProps } from "@sb-components/molecules/UiPricingDetailsModalContent/interface"
import UiRateCard from "@sb-components/molecules/UiRateCard/UiRateCard.vue"
import { pricingConditionMapper } from "../../../services/offer/offer.mapper"
import { replaceUriParams } from "../../../helpers/uriHelper"
import { setOidcCookies } from "../../../helpers/oidc"
import { useI18n } from "vue-i18n"
import { usePricingCondition } from "../../../composables/usePricingCondition"
import { useRouter } from "vue-router"
import { useUserPOS } from "../../../composables/useUserPOS"
import { useUserStore } from "@stores/user/user"

type Emits = {
  (event: "choose-offer", payload: { productCode: string; rateId: string; classCode: string; rateCode: string }): void
}

const props = defineProps<RateCardProps>()
const emits = defineEmits<Emits>()

const router = useRouter()
const { locale, t } = useI18n()
const { loggedUser } = useUserStore()
const { pushGA4Event } = useGA4Event()
const { userPOS } = useUserPOS()
const { getPricingConditionById } = usePricingCondition()

const modalIsOpen = ref<boolean>(false)
const pricingDetailsModalData = ref<UiPricingDetailsModalContentProps>()
const isLoading = ref<boolean>(false)

const processedAmenities = computed(() => {
  const signInText = t("components.stay_view.rate_card.amenities.sign_in_link")

  return props.content.amenities.map((amenity) => {
    const processedAmenity: ProcessedAmenity = { ...amenity }

    if (amenity.label) {
      const labelLower = amenity.label.toLowerCase()
      const signInTextLower = signInText.toLowerCase()

      if (labelLower.includes(signInTextLower)) {
        processedAmenity.hasSignInLink = true

        const signInIndex = labelLower.indexOf(signInTextLower)

        const actualSignInText = amenity.label.substring(signInIndex, signInIndex + signInText.length)

        processedAmenity.labelParts = {
          after: amenity.label.substring(signInIndex + signInText.length),
          before: amenity.label.substring(0, signInIndex),
          signIn: actualSignInText
        }
      }
    }

    return processedAmenity
  })
})

const uriInfos = computed(() => {
  return { locale: locale.value, origin: window.location.origin }
})

const signInUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_OIDC_SIGN_IN_URI, uriInfos.value.origin, uriInfos.value.locale)
})

const { loading } = useLoader(props.content.rateCardId)

const openPricingDetailsModal = async () => {
  if (isLoading.value) {
    return
  }

  modalIsOpen.value = true
  isLoading.value = true

  try {
    pushGA4Event("step2", "select a room", {
      eventName: EventNameEnum.bloc_interact,
      event_data: {
        bloc_interaction: "rate details",
        bloc_name: "room bloc"
      }
    })

    const pricingCondition = await getPricingConditionById(props.content.rateCardId)

    if (!pricingCondition) return

    pricingDetailsModalData.value = pricingConditionMapper(pricingCondition)
  } catch {
    modalIsOpen.value = false
  } finally {
    isLoading.value = false
  }
}

const handleChooseThisRate = async () => {
  loading.value = true
  emits("choose-offer", {
    classCode: props.classCode,
    productCode: props.productCode,
    rateCode: props.rateCode,
    rateId: props.rateId
  })

  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.bloc_interact,
    event_data: {
      bloc_interaction: `${loggedUser?.isLoyaltyMember ? "choose this rate already member" : "choose this rate guest"} - ${props.content.title}`,
      bloc_name: "room bloc"
    }
  })
}

const handleSignInClick = () => {
  setOidcCookies(router.currentRoute.value)

  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.cta_authentication,
    event_data: {
      cta_name: "sign in"
    }
  })

  window.location.href = signInUri.value
}
</script>

<template>
  <div class="Rate-card__container">
    <UiRateCard
      :title="content.title"
      :rate-card-id="content.rateCardId"
      :amenities="processedAmenities"
      :currency="content.currency"
      :formatted-tax-type="content.formattedTaxType"
      :aggregation-label="content.aggregationLabel"
      :prominent-member-price="content.prominentMemberPrice"
      :secondary-member-price="content.secondaryMemberPrice"
      :public-price-display="content.publicPriceDisplay"
      :public-price="content.publicPrice"
      :secondary-public-price="content.secondaryPublicPrice"
      :sign-in-uri="signInUri"
      :user-localization="userPOS.countryMarket"
      :is-loading="loading"
      @ui-rate-card-amenities-button::click="openPricingDetailsModal"
      @ui-rate-card-chose-rate::click="handleChooseThisRate"
      @ui-rate-card-amenities-sign-in::click="handleSignInClick"
    />

    <UiPricingDetailsModal
      :content="pricingDetailsModalData"
      :is-open="modalIsOpen"
      :is-loading="isLoading"
      @pricing-details-modal::close="modalIsOpen = false"
    />
  </div>
</template>

<style lang="scss" scoped>
@use "sass:map";
@use "@sb-config/spaces";

.Rate-card__container {
  margin-top: map.get(spaces.$sizes, "7");
}

.Rate-card__container--first {
  margin-top: 0;
}
</style>
