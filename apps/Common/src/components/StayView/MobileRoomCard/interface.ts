import { RoomType } from "@sb-components/organisms/UiRoom/types"
import { UiRateCardProps } from "@sb-components/molecules/UiRateCard/interface"
import { UiRoomCardProps } from "@sb-components/organisms/UiRoomCard/interface"
import { UiRoomDetailsModalContentProps } from "@sb-components/molecules/UiRoomDetailsModalContent/interface"

export interface MobileRoomCardProps {
  /**
   * Whether the modal is loading or not
   */
  isLoading: boolean
  /**
   * Indicates whether the modal is open or closed.
   */
  isOpen: boolean
  /**
   * Unique identifier of the rate
   */
  rateCode: string
  /**
   * Identifier of the offer.
   */
  offerId: string
  /**
   * Properties for the room card, including details such as amenities, gallery, image, pricing, and title.
   */
  roomCard: UiRoomCardProps
  /**
   * Properties for the room card modal content, providing additional details about the room.
   */
  roomCardModalContent?: UiRoomDetailsModalContentProps
  /**
   * Properties for the rate card, including information on pricing, discounts, and other offers.
   */
  rateCards: UiRateCardProps[]
  room: RoomType
}
