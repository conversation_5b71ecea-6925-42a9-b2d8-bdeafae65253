<script setup lang="ts">
import { ref, watch } from "vue"
import { MobileRoomCardProps } from "./interface"
import RateCard from "../RateCard/RateCard.vue"
import UiModal from "@sb-components/molecules/UiModal/UiModal.vue"
import UiModalHeader from "@sb-components/molecules/UiModalHeader/UiModalHeader.vue"
import UiRoomCard from "@sb-components/organisms/UiRoomCard/UiRoomCard.vue"
import UiRoomDetailsModal from "@sb-components/organisms/UiRoomDetailsModal/UiRoomDetailsModal.vue"

type Emits = {
  (event: "MobileRoomCard::headerClick"): void
  (event: "MobileRoomCard::modal-click"): void
  (event: "choose-offer", payload: { classCode: string; rateId: string; productCode: string; rateCode: string }): void
}

const props = defineProps<MobileRoomCardProps>()

const emit = defineEmits<Emits>()

const isOpen = ref<boolean>(props.isOpen)

const modalIsOpen = ref(false)

const openModal = () => {
  emit("MobileRoomCard::modal-click")
}

watch(
  () => props.isLoading,
  () => {
    if (!props.isLoading && props.roomCardModalContent) {
      modalIsOpen.value = true
    }
  }
)
</script>

<template>
  <div class="Mobile-room-card">
    <UiModal :is-open="isOpen">
      <template #header>
        <UiModalHeader
          :caption="$t('components.stay_view.mobile_room_card.caption')"
          @ui-modal-header::click="$emit('MobileRoomCard::headerClick')"
        />
      </template>

      <UiRoomCard
        v-bind="roomCard"
        :is-open="true"
        @ui-room-card-details-button::click="openModal"
        @ui-room-card-close-button::click="modalIsOpen = false"
      >
        <template #rate-cards>
          <div class="Mobile-room-card__rate-cards-title exp-heading-05">
            {{ $t("components.stay_view.room_card.rate_title") }}
          </div>

          <RateCard
            v-for="rateCard in rateCards"
            :key="rateCard.rateCardId"
            :class-code="roomCardModalContent?.classCode || ''"
            :content="rateCard"
            :rate-id="rateCard.rateCardId"
            :product-code="roomCard.productCode"
            :rate-code="rateCode"
            :room="room"
            @choose-offer="$emit('choose-offer', $event)"
          />
        </template>
      </UiRoomCard>

      <UiRoomDetailsModal
        :is-loading="isLoading"
        :room-details-modal-content="roomCardModalContent"
        :is-open="modalIsOpen"
        @room-details-modal::close="modalIsOpen = false"
      />
    </UiModal>
  </div>
</template>

<style lang="scss" scoped src="./MobileRoomCard.scss"></style>
