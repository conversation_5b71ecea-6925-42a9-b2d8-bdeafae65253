<script setup lang="ts">
import { ContextError<PERSON>ey, ContextFormContextKey, FormBuilderData, FormBuilderProps, ValidationError } from "./interface"
import { FormConfig, useForm } from "../../composables/useForm"
import { computed, provide, reactive, ref, watch } from "vue"
import FormBuilderSection from "./FormBuilderSection.vue"
import UiButton from "@sb-components/atoms/UiButton/UiButton.vue"

type Emits = {
  (event: "form:validate", data: FormBuilderData): void
  (event: "form:error", errors: Record<string, ValidationError>): void
}

const props = defineProps<FormBuilderProps>()
const emits = defineEmits<Emits>()

const formConfig = computed<FormConfig>(() => ({
  basketId: props.basketId,
  countryMarket: props.countryMarket,
  currency: props.currency
}))

const { formContext, sections, initialForm } = useForm(formConfig)

const form = reactive<FormBuilderData>({})
const errors = ref<Record<string, ValidationError>>()
provide(ContextErrorKey, errors)
provide(ContextFormContextKey, formContext)

const modelValue = computed({
  get() {
    return form
  },
  set(value) {
    Object.assign(form, value)
  }
})

watch(
  initialForm,
  (newInitialForm, oldInitialForm) => {
    // Only execute this for the first instanciation once the endpoint call is resolved
    if (oldInitialForm || !newInitialForm) return

    Object.assign(form, newInitialForm)
  },
  {
    once: true
  }
)

const validate = () => {
  const validationErrors: Record<string, ValidationError> = {}
  const validationForm: FormBuilderData = {}
  let validationSuccess = true

  for (const section of sections.value) {
    const { data, error, success } = section.schema.safeParse(form)
    validationSuccess = validationSuccess && success

    if (error) {
      for (const fieldError of error.errors) {
        const field = fieldError.path.join(".")

        validationErrors[field] = {
          error: error,
          field,
          message: fieldError.message
        }
      }
    }

    if (success) {
      Object.assign(validationForm, data)
    }
  }

  errors.value = validationErrors

  if (validationSuccess) {
    emits("form:validate", validationForm)
  } else {
    emits("form:error", errors.value)
  }
}
</script>

<template>
  <form @submit.prevent="validate">
    <FormBuilderSection
      v-for="section of sections"
      :key="section.structure.key"
      v-model="modelValue"
      :section="section"
    />

    <slot name="submit">
      <UiButton type="submit" text="Submit" />
    </slot>
  </form>
</template>
