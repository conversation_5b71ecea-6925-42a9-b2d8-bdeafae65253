@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Form-builder {
  &-section {
    &__content {
      margin: auto;
      display: flex;
      flex-direction: column;
      max-width: 36rem;
      gap: map.get(spaces.$sizes, "6");
      padding-block-end: map.get(spaces.$sizes, "10");

      .Form-field-text {
        padding-block-start: map.get(spaces.$sizes, "10");
      }

      :deep(.Form-field-helper__description) {
        color: map.get(colors.$caviarBlack, "500");
      }

      .Form-field-fields {
        display: flex;
        flex-direction: row;

        & > * {
          width: 100%;
        }

        &--double {
          gap: map.get(spaces.$sizes, "7");
        }
      }
    }

    :deep(.ads-accordion) {
      margin-block-start: map.get(spaces.$sizes, "10");

      .ads-accordion__trigger-label > span.ui-body-01-strong {
        @include text.lbf-text("heading-03");
      }

      .ads-accordion__trigger {
        padding-inline: unset;
      }

      .ads-accordion__content {
        padding: unset;
      }

      .Form-field-text {
        margin-block-end: map.get(spaces.$sizes, "6");

        &:not(:first-child) {
          margin-block-start: map.get(spaces.$sizes, "7");
        }

        h3 {
          @include text.lbf-text("body-01-strong");
        }

        .caption-01 {
          color: map.get(colors.$caviarBlack, "500");
        }

        span {
          @include text.lbf-text("body-02");
          color: map.get(colors.$caviarBlack, "500");
        }
      }
    }
  }
}
