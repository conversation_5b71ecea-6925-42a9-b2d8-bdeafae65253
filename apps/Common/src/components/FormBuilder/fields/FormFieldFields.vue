<script setup lang="ts">
import {
  ContextErrorKey,
  ContextFormContextKey,
  FormBuilderData,
  FormBuilderRowProps,
  FormContext,
  ValidationError
} from "../interface"
import { FormField } from "@shared/types/FormStructure"
import UiCheckbox from "@sb-components/atoms/UiCheckbox/UiCheckbox.vue"
import UiInput from "@sb-components/atoms/UiInput/UiInput.vue"
import { UiInputStatus } from "@sb-components/atoms/UiInput/enums"
import UiRadioGroup from "@sb-components/molecules/UiRadioGroup/UiRadioGroup.vue"
import UiSelect from "@sb-components/atoms/UiSelect/UiSelect.vue"
import { UiSelectStatus } from "@sb-components/atoms/UiSelect/enums"
import UiTextarea from "@sb-components/molecules/UiTextarea/UiTextarea.vue"
import { inject } from "vue"

defineProps<FormBuilderRowProps>()

const errors = inject<Record<string, ValidationError>>(ContextErrorKey)
const formContext = inject<FormContext>(ContextFormContextKey)

const value = defineModel<FormBuilderData>()

const mapSubfieldOptionsForSelect = (subfield: FormField) => {
  if (!Array.isArray(subfield.options?.options)) return []

  return subfield.options.options.map((opt) => ({
    label: opt.label,
    value: opt.value as string
  }))
}

const mapSubfieldOptionsForRadio = (subfield: FormField) => {
  if (!Array.isArray(subfield.options?.options)) return []

  return subfield.options.options.map((opt) => ({
    label: opt.label,
    selectedValue: opt.value as string
  }))
}

const onInput = (field: string, inputValue: string) => {
  value.value = { ...value.value, [field]: inputValue }
}
</script>

<template>
  <div
    class="Form-field-fields"
    :class="{
      'Form-field-fields--double': (field.fields?.length ?? 0) > 1
    }"
  >
    <div v-for="subfield of field.fields" :key="subfield.key">
      <fieldset>
        <!-- Select -->
        <UiSelect
          v-if="subfield.options?.inputType === 'select'"
          :assistive="field.description"
          :value="modelValue?.[subfield.key]"
          :label="subfield.displayField.label"
          :options="mapSubfieldOptionsForSelect(subfield)"
          :required="subfield.displayField.isMandatory"
          :error-message="errors?.[subfield.key]?.message"
          :status="errors?.[subfield.key] && UiSelectStatus.ERROR"
          @ui-select::update="onInput(subfield.key, $event)"
        />

        <!-- Radio group -->
        <UiRadioGroup
          v-else-if="subfield.options?.inputType === 'radios'"
          v-model="modelValue![subfield.key] as string"
          :title="subfield.displayField.label"
          :items="mapSubfieldOptionsForRadio(subfield)"
          :name="subfield.key"
          :error-message="errors?.[subfield.key]?.message"
          @ui-radio-group::update="onInput(subfield.key, $event)"
        />

        <template v-else-if="subfield.options?.inputType === 'text'">
          <UiTextarea
            v-model="value![subfield.key] as string"
            :label="subfield.displayField.label"
            :additional-message="field.description"
            :required="subfield.displayField.isMandatory"
            :character-limit="subfield.displayField.maxLength"
          />
        </template>

        <!-- Default - input -->
        <UiInput
          v-else
          v-model="value![subfield.key] as string"
          :assistive="field.description"
          :label="subfield.displayField.label"
          :error-message="errors?.[subfield.key]?.message"
          :status="errors?.[subfield.key] && UiInputStatus.ERROR"
          :required="subfield?.displayField.isMandatory"
        />
      </fieldset>

      <!-- Edge cases -->
      <!-- Russia data transfer -->
      <template v-if="subfield.key === 'nationality' && formContext?.isRussianLawContext">
        <UiCheckbox
          id="russian-law-context"
          name="russian-law-context"
          :description="$t('form.russian_consent')"
          :v-model="value!.russianLawContext"
          :selected-value="true"
          :error-message="errors?.russianLawContext?.message"
        />
      </template>
    </div>
  </div>
</template>
