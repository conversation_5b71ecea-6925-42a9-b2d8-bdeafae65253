<script setup lang="ts">
import { FormBuilderData, FormBuilderSectionProps } from "./interface"
import FormBuilderRow from "./FormBuilderRow.vue"
import UiDetailSummary from "@sb-components/molecules/UiDetailSummary/UiDetailSummary.vue"
import { computed } from "vue"

const props = defineProps<FormBuilderSectionProps>()

const structure = computed(() => props.section.structure)

const value = defineModel<FormBuilderData>()
</script>

<template>
  <UiDetailSummary
    class="Form-builder-section"
    :title="structure.title"
    :subtitle="structure.subtitle"
    :start-expanded="structure.expanded"
  >
    <template #content>
      <div class="Form-builder-section__content">
        <FormBuilderRow
          v-for="field of structure.fields"
          :key="field.key"
          v-model="value"
          class="Form-builder-section__row"
          :field="field"
        />
      </div>
    </template>
  </UiDetailSummary>
</template>

<style lang="scss" scoped src="./FormBuilder.scss"></style>
