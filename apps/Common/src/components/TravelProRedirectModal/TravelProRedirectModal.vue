<script setup lang="ts">
import { ModalSize, Transition } from "@sb-components/molecules/UiModal/enums"
import { computed, inject } from "vue"
import { useModal, useSearch } from "../../composables"
import { BrandConfiguration } from "@shared/types"
import { DateMoment } from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import { DividerDirection } from "@sb-components/atoms/UiDivider/enums"
import { TravelProRedirectionFixedParams } from "./interface"
import UiDivider from "@sb-components/atoms/UiDivider/UiDivider.vue"
import UiImage from "@sb-components/atoms/UiImage/UiImage.vue"
import UiLink from "@sb-components/atoms/UiLink/UiLink.vue"
import UiModal from "@sb-components/molecules/UiModal/UiModal.vue"
import { formatISO } from "date-fns"
import { useI18n } from "vue-i18n"

const { locale } = useI18n()
const config = inject<BrandConfiguration>("brandConfig")!

const { isModalOpen, closeModal } = useModal("travel-pro")
const { dates, iataCode, rooms, lengthOfStay, hotel } = useSearch()

const dateIn = computed(() => formatISO(dates.value?.[DateMoment.START], { representation: "date" }))
const monthIn = computed(() => ((dates.value?.[DateMoment.START]?.getMonth() || 0) + 1).toString())
const yearIn = computed(() => dates.value?.[DateMoment.START]?.getFullYear().toString())
const dateOut = computed(() => formatISO(dates.value?.[DateMoment.END], { representation: "date" }))
const monthOut = computed(() => ((dates.value?.[DateMoment.END]?.getMonth() || 0) + 1).toString())
const yearOut = computed(() => dates.value?.[DateMoment.START]?.getFullYear().toString())

const params = computed(() => {
  const body = {
    dateIn: dateIn.value,
    dateOut: dateOut.value,
    "identification.reserverId": iataCode.value,
    nightNb: lengthOfStay.value.toString(),
    origin: config.siteCode,
    partner_id: config.siteCode,
    roomNumber: rooms.value.length.toString(),
    "search.dayIn": dates.value[DateMoment.START]?.getDate().toString(),
    "search.dayOut": dates.value[DateMoment.END]?.getDate().toString(),
    "search.destination": hotel.value?.id,
    "search.destination.code": hotel.value?.id,
    "search.destination.userlang": hotel.value?.name,
    "search.geozone.geoZoneCode": hotel.value?.id,
    "search.monthIn": monthIn.value,
    "search.monthOut": monthOut.value,
    "search.yearIn": yearIn.value,
    "search.yearOut": yearOut.value,

    //TODO: add cookie persistency (Not included in CONVERT-173)
    ...TravelProRedirectionFixedParams
  }

  for (const [index, room] of Object.entries(rooms.value)) {
    const roomBody = {
      [`search.roomCriteria[${index}].adultNumber`]: room.adults,
      [`search.roomCriteria[${index}].childrenNumber`]: room.children
    }

    for (const [subIndex, childAge] of Object.entries(room.childrenAges)) {
      roomBody[`search.roomCriteria[${index}].children[${subIndex}].age`] = childAge
    }

    Object.assign(body, roomBody)
  }

  return body
})

const allLink = computed(() => {
  const urlSearchParams = new URLSearchParams(params.value as Record<string, string>)

  return `${import.meta.env.VITE_APP_TRAVEL_PRO_REDIRECTION_URL}index.${locale.value}.shtml?${urlSearchParams.toString()}`
})
</script>

<template>
  <UiModal
    with-overlay
    class="Travel-pro-redirect-modal"
    :is-open="isModalOpen"
    :transition="Transition.FADE"
    :modal-size="ModalSize.SMALL"
    mobile-min-height
    @ui-modal::close="closeModal"
  >
    <div class="Travel-pro-redirect-modal__container">
      <div class="Travel-pro-redirect-modal__logos">
        <UiImage
          class="Travel-pro-redirect-modal__brand"
          src="/booking/brandLogoBlack.svg"
          :alt="$t('components.travel_pro_redirect_modal.brand_logo', { brand: config.name })"
        />
        <UiImage
          class="Travel-pro-redirect-modal__travel"
          src="/booking/travelProLogo.svg"
          :alt="$t('components.travel_pro_redirect_modal.travel_pro_logo')"
        />
        <UiImage
          class="Travel-pro-redirect-modal__all"
          src="/booking/allLogo.svg"
          :alt="$t('components.travel_pro_redirect_modal.all_logo')"
        />
        <!--TODO: found a way to use relative link for all and travel pro  brands-->
      </div>
      <UiDivider :direction="DividerDirection.HORIZONTAL" max-length="80rem" />
      <p class="Travel-pro-redirect-modal__infos">{{ $t("components.travel_pro_redirect_modal.infos") }}</p>
      <div class="Travel-pro-redirect-modal__actions">
        <UiLink
          class="Travel-pro-redirect-modal__link"
          :text="$t('components.travel_pro_redirect_modal.redirect')"
          :href="allLink"
          uppercase
          external
        />
      </div>
    </div>
  </UiModal>
</template>

<style lang="scss" scoped src="./TravelProRedirectModal.scss"></style>
