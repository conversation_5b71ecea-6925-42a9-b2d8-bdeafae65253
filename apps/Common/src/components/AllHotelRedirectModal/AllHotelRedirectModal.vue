<script setup lang="ts">
import { ModalSize, Transition } from "@sb-components/molecules/UiModal/enums"
import { SpecialRates, SpecialRatesCodes } from "@sb-components/organisms/UiSpecialRates/enums"
import { computed, inject } from "vue"
import { useModal, useSearch } from "../../composables"
import { AllHotelRedirectionFixedParams } from "./interface"
import { BrandConfiguration } from "@shared/types"
import { DividerDirection } from "@sb-components/atoms/UiDivider/enums"
import UiDivider from "@sb-components/atoms/UiDivider/UiDivider.vue"
import UiImage from "@sb-components/atoms/UiImage/UiImage.vue"
import UiLink from "@sb-components/atoms/UiLink/UiLink.vue"
import UiModal from "@sb-components/molecules/UiModal/UiModal.vue"
import { useI18n } from "vue-i18n"
import { useUserPOS } from "../../composables/useUserPOS"

const { dateIn, hotel, lengthOfStay, rooms, specialRate, promoCode } = useSearch()
const { locale } = useI18n()
const { userPOS } = useUserPOS()
const config = inject<BrandConfiguration>("brandConfig")!

const monthIn = computed(() => ((dateIn.value?.getMonth() || 0) + 1).toString())
const yearIn = computed(() => dateIn.value?.getFullYear().toString())

const params = computed<Record<string, string | number | undefined>>(() => {
  const body = {
    code_langue: locale.value,
    currency: userPOS.currency,
    dayIn: dateIn.value?.getDate().toString(),
    destination: hotel.value?.id,
    monthIn: monthIn.value,
    nightNb: lengthOfStay.value.toString(),
    origin: config.siteCode,
    partner_id: config.siteCode,
    preferredCode: undefined as string | undefined,
    roomNumber: rooms.value.length.toString(),
    yearIn: yearIn.value,

    //TODO: add cookie persistency (Not included in CONVERT-174)

    ...AllHotelRedirectionFixedParams
  }

  for (const [index, room] of Object.entries(rooms.value)) {
    const roomBody = {
      [`room[${index}].adultNumber`]: room.adults,
      [`room[${index}].childrenNumber`]: room.children
    }

    for (const [subIndex, childAge] of Object.entries(room.childrenAges)) {
      roomBody[`room[${index}].childrenAge[${subIndex}]`] = childAge
    }

    Object.assign(body, roomBody)
  }

  switch (specialRate.value) {
    case SpecialRates.PROMO_CODE:
      if (promoCode.value && promoCode.value.split(",").length > 1) {
        delete body.preferredCode
      } else {
        body.preferredCode = promoCode.value
      }
      break
    case SpecialRates.AAA_CCA_MEMBER:
      body.preferredCode = SpecialRatesCodes.AAA_CCA_MEMBER
      break
    case SpecialRates.GOVERNMENT: // We do not handle multiple code in the URL for all.com redirection
      delete body.preferredCode
      break
    case SpecialRates.MILITARY_VETERAN:
      body.preferredCode = SpecialRatesCodes.MILITARY_VETERAN
      break
    case SpecialRates.SENIOR_DISCOUNT:
      body.preferredCode = SpecialRatesCodes.SENIOR_DISCOUNT
      break

    default:
      delete body.preferredCode
      break
  }

  return body
})

const allLink = computed(() => {
  const urlSearchParams = new URLSearchParams(params.value as Record<string, string>)

  return `${import.meta.env.VITE_APP_ALL_HOTEL_REDIRECTION_URL}${urlSearchParams.toString()}`
})

const { isModalOpen, closeModal } = useModal("hotel")
</script>

<template>
  <UiModal
    with-overlay
    class="All-hotel-redirect-modal"
    :is-open="isModalOpen"
    :transition="Transition.FADE"
    :modal-size="ModalSize.SMALL"
    mobile-min-height
    @ui-modal::close="closeModal"
  >
    <div class="All-hotel-redirect-modal__container">
      <div class="All-hotel-redirect-modal__logos">
        <UiImage
          class="All-hotel-redirect-modal__brand"
          src="/booking/brandLogoBlack.svg"
          :alt="$t('components.all_hotel_redirect_modal.brand_logo', { brand: config.name })"
        />
        <UiImage
          class="All-hotel-redirect-modal__all"
          src="/booking/allLogo.svg"
          :alt="$t('components.all_hotel_redirect_modal.all_logo')"
        />
        <!--TODO: found a way to use relative link for all brands-->
      </div>
      <UiDivider :direction="DividerDirection.HORIZONTAL" max-length="800px" />
      <p class="All-hotel-redirect-modal__infos">{{ $t("components.all_hotel_redirect_modal.infos") }}</p>
      <div class="All-hotel-redirect-modal__actions">
        <UiLink
          class="All-hotel-redirect-modal__link"
          :text="$t('components.all_hotel_redirect_modal.redirect')"
          :href="allLink"
          uppercase
          external
        />
      </div>
    </div>
  </UiModal>
</template>

<style lang="scss" scoped src="./AllHotelRedirectModal.scss"></style>
