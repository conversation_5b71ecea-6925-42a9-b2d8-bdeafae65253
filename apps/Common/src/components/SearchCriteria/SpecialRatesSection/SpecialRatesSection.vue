<script setup lang="ts">
import { SpecialRates } from "@sb-components/organisms/UiSpecialRates/enums"
import { SpecialRatesSectionProps } from "./interface"
import { UiInputStatus } from "@sb-components/atoms/UiInput/enums"
import UiSection from "@sb-components/molecules/UiSection/UiSection.vue"
import UiSpecialRates from "@sb-components/organisms/UiSpecialRates/UiSpecialRates.vue"

withDefaults(defineProps<SpecialRatesSectionProps>(), {
  isValid: true
})

defineEmits(["SpecialRatesSection::validate"])

const specialRate = defineModel<SpecialRates>("special-rate")
const promoCode = defineModel<string>("promo-code")
const iataCode = defineModel<string>("iata-code")
</script>

<template>
  <div>
    <UiSection :title="$t('components.search_criteria.special_rates.section_title')">
      <template #content>
        <UiSpecialRates
          v-model:iata-code="iataCode"
          v-model:rate-selected="specialRate as SpecialRates"
          v-model:promo-code="promoCode"
          :iata-error-message="iataErrorMessage"
          :iata-code-status="!!iataErrorMessage ? UiInputStatus.ERROR : undefined"
          :promo-error-message="promoErrorMessage"
          :promo-code-status="!!promoErrorMessage ? UiInputStatus.ERROR : undefined"
          @special-rates::iata-blur="$emit('SpecialRatesSection::validate')"
        />
      </template>
    </UiSection>
  </div>
</template>
