<script setup lang="ts">
import { computed, ref } from "vue"
import { ActionAlignment } from "@sb-components/molecules/UiModal/enums"
import { DefaultRoomDetails } from "@sb-components/organisms/UiRooms/constants"
import { RoomType } from "@sb-components/organisms/UiRoom/types"
import { RoomsAndGuestsModalProps } from "./interface"
import { RoomsDirection } from "@sb-components/organisms/UiRooms/enums"
import UiButton from "@sb-components/atoms/UiButton/UiButton.vue"
import UiModal from "@sb-components/molecules/UiModal/UiModal.vue"
import UiModalHeader from "@sb-components/molecules/UiModalHeader/UiModalHeader.vue"
import { UiRoomSelectDefault } from "@sb-components/organisms/UiRoom/constants"
import UiRooms from "@sb-components/organisms/UiRooms/UiRooms.vue"
import UiSearchCriteriaButton from "@sb-components/atoms/UiSearchCriteriaButton/UiSearchCriteriaButton.vue"
import { UiSearchCriteriaButtonVariant } from "@sb-components/atoms/UiSearchCriteriaButton/enums"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"

defineProps<RoomsAndGuestsModalProps>()

defineEmits(["RoomsAndGuestsModal::validate-children-age"])

const { t } = useI18n()
const route = useRoute()

const rooms = defineModel<RoomType[]>("rooms")
const roomsAndGuestsInError = defineModel<number[]>("roomsAndGuestsInError")

const temporaryRooms = ref<RoomType[]>(
  rooms.value || [
    {
      ...DefaultRoomDetails,
      id: 0
    }
  ]
)
const isOpen = defineModel<boolean>("is-open")

const buttonText = computed(() => {
  return route.name === "search" ? t("global.continue") : t("global.apply")
})

const criteriaValue = computed(() => {
  const roomsNumber = rooms.value?.length
  const guestsNumbers = rooms.value?.reduce((acc: number, room: RoomType) => acc + room.adults + room.children, 0)

  return `${t("components.search_criteria.rooms_and_guests.button_value_rooms", { count: roomsNumber })} - ${t("components.search_criteria.rooms_and_guests.button_value_guests", { count: guestsNumbers })}`
})

const closeModal = () => {
  isOpen.value = false
}

const openModal = () => {
  isOpen.value = true
  // Reset working value on modal open
  temporaryRooms.value = JSON.parse(JSON.stringify(rooms.value))
}

const addRoom = (id: number) => {
  temporaryRooms.value?.push({
    ...DefaultRoomDetails,
    id
  })
}

const singleDropdownValidation = (dropdown: { roomIndex: number; dropdownIndex: number }) => {
  if (temporaryRooms.value[dropdown.roomIndex].childrenAges[dropdown.dropdownIndex] === UiRoomSelectDefault) {
    if (temporaryRooms.value[dropdown.roomIndex].dropdownsInError) {
      temporaryRooms.value[dropdown.roomIndex]?.dropdownsInError?.push(dropdown.dropdownIndex)
    } else {
      temporaryRooms.value[dropdown.roomIndex].dropdownsInError = [dropdown.dropdownIndex]
    }
  }

  // if dropdown and not the default value, we have to filter the index out of the array
  else {
    const filteredErrors = temporaryRooms.value[dropdown.roomIndex].dropdownsInError?.filter(
      (item) => item !== dropdown.dropdownIndex
    )
    temporaryRooms.value[dropdown.roomIndex].dropdownsInError = filteredErrors

    // if there's no more dropdowns in error, we also filter out the room index from the room errors
    if (!temporaryRooms.value[dropdown.roomIndex].dropdownsInError?.length) {
      const filteredRoomsInError = roomsAndGuestsInError.value?.filter((roomIndex) => roomIndex !== dropdown.roomIndex)
      roomsAndGuestsInError.value = filteredRoomsInError
    }
  }
}

const handleContinue = () => {
  rooms.value = temporaryRooms.value

  closeModal()
}

defineExpose({
  openModal
})
</script>

<template>
  <div class="Rooms-and-guests-modal">
    <UiSearchCriteriaButton
      :criteria-name="$t('components.search_criteria.rooms_and_guests.dropdown.name')"
      :criteria-value="criteriaValue"
      :error="!isValid"
      has-arrow
      :variant="UiSearchCriteriaButtonVariant.BORDERED"
      @ui-search-criteria-button::click="openModal"
    />
    <UiModal :is-open="isOpen" :action-alignment="ActionAlignment.FULL">
      <template #header>
        <UiModalHeader
          has-border-bottom
          :title="$t('components.search_criteria.rooms_and_guests.section_title')"
          @ui-modal-header::click="closeModal"
        />
      </template>

      <UiRooms
        :direction="RoomsDirection.VERTICAL"
        :rooms="temporaryRooms as RoomType[]"
        :rooms-and-guests-in-error="roomsAndGuestsInError"
        :error-message="t('errors.child_age')"
        @ui-rooms::update="temporaryRooms = $event"
        @ui-rooms::add="addRoom"
        @ui-rooms::validate-children-age="singleDropdownValidation($event)"
      />

      <template #actions>
        <UiButton :text="buttonText" @click="handleContinue" />
      </template>
    </UiModal>
  </div>
</template>
