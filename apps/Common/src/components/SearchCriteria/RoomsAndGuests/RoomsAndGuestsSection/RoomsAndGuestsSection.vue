<script setup lang="ts">
import { DefaultRoomDetails } from "@sb-components/organisms/UiRooms/constants"
import { RoomsAndGuestsSectionProps } from "./interface"
import UiRooms from "@sb-components/organisms/UiRooms/UiRooms.vue"
import UiSection from "@sb-components/molecules/UiSection/UiSection.vue"
import { UnwrapRef } from "vue"
import { useI18n } from "vue-i18n"
import { useSearch } from "../../../../composables"

defineProps<RoomsAndGuestsSectionProps>()

defineEmits(["RoomsAndGuestsSection::validate-children-age"])

const { rooms } = useSearch()

const { t } = useI18n()

const updateRooms = (newRooms: UnwrapRef<typeof rooms>) => {
  rooms.value = newRooms
}

const addRoom = (id: number) => {
  rooms.value = [
    ...rooms.value,
    {
      id,
      ...DefaultRoomDetails
    }
  ]
}
</script>

<template>
  <UiSection :title="$t('components.search_criteria.rooms_and_guests.section_title')">
    <template #content>
      <UiRooms
        :rooms="rooms"
        :max-rooms="maxRooms"
        :max-pax="maxPax"
        :max-adult="maxAdult"
        :max-child="maxChild"
        :max-child-age="maxChildAge"
        :error-message="t('errors.child_age')"
        :rooms-and-guests-in-error="roomsAndGuestsInError"
        @ui-rooms::update="updateRooms"
        @ui-rooms::add="addRoom"
        @ui-rooms::validate-children-age="$emit('RoomsAndGuestsSection::validate-children-age', $event)"
      />
    </template>
  </UiSection>
</template>
