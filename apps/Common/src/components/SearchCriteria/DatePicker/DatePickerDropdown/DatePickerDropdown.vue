<script setup lang="ts">
import { DateMoment, DatePickerVariant } from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import { DatePickerDropdownProps } from "./interface"
import UiDatePickerDesktop from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/UiDatePickerDesktop.vue"
import { ref } from "vue"

withDefaults(defineProps<DatePickerDropdownProps>(), {
  variant: DatePickerVariant.DEFAULT
})

defineEmits(["DatePickerDropdown::change-start-date", "DatePickerDropdown::change-end-date"])

const dates = defineModel<Date[]>()
const uiDatePickerDesktop = ref()

const handleDatesChange = (newDates: Date[]) => {
  dates.value = newDates
}

const openDropdownModal = (moment: DateMoment) => {
  uiDatePickerDesktop.value.openDatePickerDropdown(moment)
}

defineExpose({ openDropdownModal })
</script>

<template>
  <div class="Date-picker-desktop">
    <UiDatePickerDesktop
      ref="uiDatePickerDesktop"
      :dates="dates"
      :variant="variant"
      :max-date="maxDate"
      :max-range="maxRange"
      :min-range="minRange"
      :error-message="errorMessage"
      :start-date-error="!inputsAreValid?.startDate"
      :end-date-error="!inputsAreValid?.endDate"
      @update:dates="handleDatesChange"
      @ui-date-picker-desktop::clear="dates = []"
      @ui-date-picker-desktop::change-start-date="$emit('DatePickerDropdown::change-start-date', $event)"
      @ui-date-picker-desktop::change-end-date="$emit('DatePickerDropdown::change-end-date', $event)"
    />
  </div>
</template>
