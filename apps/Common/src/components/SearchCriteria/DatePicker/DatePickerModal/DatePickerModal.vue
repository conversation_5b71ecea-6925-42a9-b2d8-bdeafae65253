<script setup lang="ts">
import { computed, ref, useTemplateRef } from "vue"
import { DateMoment } from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import { DatePickerModalProps } from "./interface"
import UiButton from "@sb-components/atoms/UiButton/UiButton.vue"
import { UiButtonVariation } from "@sb-components/atoms/UiButton/enums"
import UiDatePickerMobile from "@sb-components/organisms/UiDatePicker/UiDatePickerMobile/UiDatePickerMobile.vue"
import UiIcon from "@sb-components/atoms/UiIcon/UiIcon.vue"
import UiMessage from "@sb-components/molecules/UiMessage/UiMessage.vue"
import { UiMessageVariation } from "@sb-components/molecules/UiMessage/enums"
import UiModal from "@sb-components/molecules/UiModal/UiModal.vue"
import UiModalHeader from "@sb-components/molecules/UiModalHeader/UiModalHeader.vue"
import UiSearchCriteriaButton from "@sb-components/atoms/UiSearchCriteriaButton/UiSearchCriteriaButton.vue"
import { UiSearchCriteriaButtonVariant } from "@sb-components/atoms/UiSearchCriteriaButton/enums"
import { differenceInCalendarDays } from "date-fns"
import { useDatePicker } from "@sb-composables/index"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"

defineProps<DatePickerModalProps>()

const emits = defineEmits([
  "DatePickerModal:closeModal",
  "DatePickerModal:isValid",
  "DatePickerModal:change-start-date",
  "DatePickerModal:change-end-date"
])

const dates = defineModel<Date[]>("dates", {
  default: () => []
})
const currentDates = defineModel<Date[]>("current-dates")
const isOpen = defineModel<boolean>("is-open")

const { computedDate } = useDatePicker()
const { t } = useI18n()
const route = useRoute()

const datePicker = useTemplateRef("date-picker")
const defaultDateSelected = ref<DateMoment>(DateMoment.START)

const buttonText = computed(() => {
  return route.name === "search" ? t("global.continue") : t("global.apply")
})

const closeModal = (resetDates: boolean = true) => {
  defaultDateSelected.value = DateMoment.START
  isOpen.value = false
  emits("DatePickerModal:closeModal")

  if (resetDates) {
    currentDates.value = [...dates.value]
  }
}

const nightsCount = computed(() => {
  if (!currentDates.value?.[DateMoment.START] || !currentDates.value?.[DateMoment.END]) return 0

  return differenceInCalendarDays(currentDates.value?.[DateMoment.END], currentDates.value?.[DateMoment.START])
})

const clearDates = () => {
  currentDates.value = []
  datePicker.value?.clearDates()
}

const handleContinue = () => {
  if (currentDates?.value?.[DateMoment.START] && currentDates?.value[DateMoment.END]) {
    dates.value = [...currentDates.value]
  } else {
    dates.value = []
  }

  closeModal(false)
}

const startDateLabel = computed(() =>
  dates.value?.[DateMoment.START]
    ? computedDate(dates.value[DateMoment.START])
    : t("ui.organisms.ui_date_picker_desktop.example")
)
const endDateLabel = computed(() =>
  dates.value?.[DateMoment.END]
    ? computedDate(dates.value[DateMoment.END])
    : t("ui.organisms.ui_date_picker_desktop.example")
)
</script>

<template>
  <div class="Date-picker-modal">
    <UiSearchCriteriaButton
      :criteria-name="$t('components.search_criteria.date_picker.title')"
      has-arrow
      :is-criteria-value-empty="!dates || !dates.length"
      :variant="UiSearchCriteriaButtonVariant.BORDERED"
      :error="!isValid"
      @ui-search-criteria-button::click="isOpen = true"
    >
      <template #criteria-value>
        <UiButton
          class="Date-picker-modal__button"
          :aria-label="$t('components.search_criteria.date_picker.edit_start_date')"
          :variation="UiButtonVariation.PLAIN"
          :text="startDateLabel || ''"
          @click="defaultDateSelected = DateMoment.START"
        />
        <UiIcon class="Date-picker-modal__arrow" name="arrow" />
        <UiButton
          class="Date-picker-modal__button"
          :aria-label="$t('components.search_criteria.date_picker.edit_end_date')"
          :variation="UiButtonVariation.PLAIN"
          :text="endDateLabel || ''"
          @click="defaultDateSelected = DateMoment.END"
        />
      </template>
    </UiSearchCriteriaButton>
    <UiModal :is-open="isOpen">
      <template #header>
        <UiModalHeader
          :title="$t('components.search_criteria.date_picker.choose_your_dates')"
          @ui-modal-header::click="closeModal"
        />
      </template>

      <UiMessage
        v-if="errorMessage"
        :description="errorMessage"
        :variation="UiMessageVariation.DANGER"
        no-radius
        class="my-4"
      />

      <UiDatePickerMobile
        ref="date-picker"
        v-model="currentDates"
        :max-date="maxDate"
        :max-range="maxRange"
        :min-range="minRange"
        :default-date-selected="defaultDateSelected"
        @ui-date-picker-mobile:change-start-date="$emit('DatePickerModal:change-start-date')"
        @ui-date-picker-mobile:change-end-date="$emit('DatePickerModal:change-end-date')"
      />

      <template #actions>
        <div class="Date-picker-modal__action-row">
          <p v-if="nightsCount" class="Date-picker-modal__nights">
            {{ $t("ui.organisms.ui_date_picker_mobile.nights", { count: nightsCount }) }}
          </p>
          <button class="Date-picker-modal__clear" type="button" :disabled="nightsCount === 0" @click="clearDates">
            {{ $t("ui.organisms.ui_date_picker_mobile.clear") }}
          </button>
        </div>
        <UiButton type="button" :disabled="currentDates?.length !== 2" :text="buttonText" @click="handleContinue" />
      </template>
    </UiModal>
  </div>
</template>

<style lang="scss" scoped src="./DatePickerModal.scss"></style>
