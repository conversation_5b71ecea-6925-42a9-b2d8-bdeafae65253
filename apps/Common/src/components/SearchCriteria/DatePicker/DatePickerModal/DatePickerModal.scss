@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-utilities/mq";

.Date-picker-modal {
  :deep(.Search-criteria-button__value) {
    display: flex;
    gap: map.get(spaces.$sizes, "6");
  }

  &__arrow {
    font-size: 1.6rem;
    transform: rotateZ(180deg);
  }

  &__action-row {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-start;
  }

  &__button {
    :deep(.ads-button) {
      @include text.lbf-text("body-02");
      min-height: auto;
    }
  }
}
