import { DestinationVariant } from "@sb-components/organisms/UiDestination/UiDestinationDesktop/enums"
import { UiDestinationDesktopProps } from "@sb-components/organisms/UiDestination/UiDestinationDesktop/interface"

export interface DestinationDropdownProps extends UiDestinationDesktopProps {
  errorMessage?: string
  isLoading?: boolean
  isValid?: boolean
  searchValue?: string
  variant?: DestinationVariant
}
