<script setup lang="ts">
import { Destination } from "@sb-components/atoms/UiDestinationListItem/interface"
import type { DestinationDropdownProps } from "./interface"
import { DestinationVariant } from "@sb-components/organisms/UiDestination/UiDestinationDesktop/enums"
import UiDestinationDesktop from "@sb-components/organisms/UiDestination/UiDestinationDesktop/UiDestinationDesktop.vue"
import { ref } from "vue"

withDefaults(defineProps<DestinationDropdownProps>(), {
  isValid: true,
  variant: DestinationVariant.DEFAULT
})

const hotel = defineModel<Destination>("hotel")
const inputValue = defineModel<string | undefined>("input-filter")
const dropdownIsOpen = defineModel<boolean>("dropdown-is-open")

const destinationDropdown = ref()
</script>

<template>
  <div class="Destination-dropdown">
    <UiDestinationDesktop
      ref="destinationDropdown"
      v-model:destination="hotel"
      v-model:input-value="inputValue"
      v-model:is-open="dropdownIsOpen"
      :error-border="!isValid"
      :error-message="errorMessage"
      :is-loading="isLoading"
      :items="items"
      :variant="variant"
    />
  </div>
</template>
