<script setup lang="ts">
import { usePosCurrency<PERSON><PERSON><PERSON>, usePosLanguageHandler, useUserPOS } from "../../composables"
import CurrencyModal from "../CurrencyModal/CurrencyModal.vue"
import { HeaderProps } from "./interface"
import LanguageModal from "../LanguageModal/LanguageModal.vue"
import { LoyaltyAccountUriFragments } from "./constants"
import { OidcEnpoint } from "../../global/enums"
import UiDropdownAccount from "@sb-components/molecules/UiDropdownAccount/UiDropdownAccount.vue"
import UiLink from "@sb-components/atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "@sb-components/atoms/UiLink/enums"
import UiLoadingDots from "@sb-components/atoms/UiLoadingDots/UiLoadingDots.vue"
import { brandConfiguration } from "../../global/consts"
import { computed } from "vue"
import { replaceUriParams } from "../../helpers/uriHelper"
import { setOidcCookies } from "../../helpers/oidc"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"
import { useUserStore } from "@stores/user/user"

const {
  countriesGeographicalArea,
  countriesGeographicalAreaOptions,
  countryRegionLabel,
  countryRegionOptions,
  resetState
} = usePosLanguageHandler()
const {
  currencyCode,
  currencyCodeOptions,
  currenciesGeographicalArea,
  currenciesGeographicalAreaOptions,
  resetStateCurrency
} = usePosCurrencyHandler()
const route = useRoute()
const { userPOS } = useUserPOS()
const { loggedUser, clearUserStore } = useUserStore()
const { locale } = useI18n()

defineProps<HeaderProps>()

const myBookingsUrl = brandConfiguration.myBookingsUrl
  ? brandConfiguration.myBookingsUrl
      .replace("{legacyHostname}", brandConfiguration.legacy.hostname)
      .replace("{locale}", locale.value)
  : null

const uriInfos = computed(() => {
  return { locale: locale.value, origin: window.location.origin }
})

const signInUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_OIDC_SIGN_IN_URI, uriInfos.value.origin, uriInfos.value.locale)
})

const signUpUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_OIDC_SIGN_UP_URI, uriInfos.value.origin, uriInfos.value.locale)
})

const logOutUri = computed(() => {
  return OidcEnpoint.logout
})

const loyaltyAccountUri = computed(() => {
  return `${replaceUriParams(import.meta.env.VITE_LOYALTY_ACCOUNT_REDIRECTION_URI, uriInfos.value.origin, uriInfos.value.locale)}#${LoyaltyAccountUriFragments.LOYALTY_ACCOUNT_URI_FRAGMENT}`
})

const pointStatementUri = computed(() => {
  return `${replaceUriParams(import.meta.env.VITE_LOYALTY_ACCOUNT_REDIRECTION_URI, uriInfos.value.origin, uriInfos.value.locale)}#${LoyaltyAccountUriFragments.POINT_STATEMENT_URI_FRAGMENT}`
})

const reservationUri = computed(() => {
  return `${replaceUriParams(import.meta.env.VITE_LOYALTY_ACCOUNT_REDIRECTION_URI, uriInfos.value.origin, uriInfos.value.locale)}#${LoyaltyAccountUriFragments.RESERVATION_URI_FRAGMENT}`
})

function handleLogout() {
  setOidcCookies(route)
  clearUserStore()
  window.location.href = logOutUri.value
}

function handleSignInClicked() {
  setOidcCookies(route)
  window.location.href = signInUri.value
}

function handleSignUpClicked() {
  setOidcCookies(route)
  window.location.href = signUpUri.value
}
</script>
<template>
  <header class="Header container">
    <div class="grid">
      <div class="Header__brand">
        <UiLink :href="logoLink" :variant="UiLinkVariant.NEUTRAL" text="">
          <template #preprend-content>
            <span class="sr-only">{{ $t("global.go_to_homepage") }}</span>
            <img class="Header__brand-logo" :src="logo" :alt="$t('components.header.brand_logo')" />
          </template>
        </UiLink>
      </div>

      <div class="Header__right">
        <UiDropdownAccount
          :my-bookings-url="myBookingsUrl"
          :user-logged-infos="loggedUser"
          :your-loyalty-account-uri="loyaltyAccountUri"
          :your-point-statement-uri="pointStatementUri"
          :your-reservation-uri="reservationUri"
          @ui-dropdown-account::sign-in-clicked="handleSignInClicked"
          @ui-dropdown-account::sign-up-clicked="handleSignUpClicked"
          @ui-dropdown-account::log-out-clicked="handleLogout"
        />

        <UiLoadingDots v-if="!userPOS.countryMarket || !userPOS.currency" color="white" />
        <template v-else>
          <LanguageModal
            v-model:geographical-area="countriesGeographicalArea"
            v-model:country="countryRegionLabel"
            :geographical-options="countriesGeographicalAreaOptions"
            :country-options="countryRegionOptions"
            @close:language-modal="resetState"
          />

          <CurrencyModal
            v-model:geographical-area="currenciesGeographicalArea"
            v-model:currency="currencyCode"
            :geographical-options="currenciesGeographicalAreaOptions"
            :currency-options="currencyCodeOptions"
            @close:currency-modal="resetStateCurrency"
          />
        </template>
      </div>
    </div>
  </header>
</template>

<style lang="scss" scoped src="./Header.scss"></style>
