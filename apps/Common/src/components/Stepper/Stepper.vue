<script setup lang="ts">
import { useRoute, useRouter } from "vue-router"
import UiStepper from "@sb-components/molecules/UiStepper/UiStepper.vue"
import { computed } from "vue"
import { useI18n } from "vue-i18n"
import { useSearchQueryParams } from "../../composables"

const route = useRoute()
const router = useRouter()
const { locale, t } = useI18n()
const { queryParams } = useSearchQueryParams()

const stepRoutes = ["search", "stay", "enhance", "complete"]
const clearChoiceRoutes = ["search", "stay"]

const currentStepIndex = computed(() => stepRoutes.indexOf(route.name?.toString() || ""))
const searchCriteriaQueryParams = computed(() => {
  const qp: Record<string, string> = {}
  const queryParamsValue = queryParams.value

  for (const key in queryParamsValue) {
    if (key.includes("productCode") || key.includes("offerId")) continue

    qp[key] = queryParamsValue[key] as string
  }

  return qp
})

const stepItems = computed(() =>
  stepRoutes.map((routeName, index) => {
    const href = router.resolve({
      name: routeName,
      params: { locale: locale.value },
      query: clearChoiceRoutes.includes(routeName) ? searchCriteriaQueryParams.value : queryParams.value
    }).href

    return {
      href,
      isCompleted: index < currentStepIndex.value,
      isCurrent: index === currentStepIndex.value,
      step: index + 1,
      text: t(`routes.${routeName}`),
      totalStep: stepRoutes.length
    }
  })
)
</script>

<template>
  <UiStepper
    v-if="route.name && stepRoutes.includes(route.name as string)"
    :items="stepItems"
    :current-step-index="currentStepIndex"
    :total-step="stepItems.length"
  />
</template>
