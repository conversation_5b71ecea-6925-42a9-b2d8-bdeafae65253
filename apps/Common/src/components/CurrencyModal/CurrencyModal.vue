<script setup lang="ts">
import { UiButtonSize, UiButtonVariation } from "@sb-components/atoms/UiButton/enums"
import { useLoader, useModal, usePosCurrencyHandler } from "../../composables"
import { CurrencyModalProps } from "./interface"
import UiButton from "@sb-components/atoms/UiButton/UiButton.vue"
import UiHeaderModal from "@sb-components/molecules/UiHeaderModal/UiHeaderModal.vue"
import UiSelect from "@sb-components/atoms/UiSelect/UiSelect.vue"
import { updateCurrencyCookies } from "../../services/pos/pos.fetch"
import { usePosStore } from "@stores/pos/pos"
import { useTemplateRef } from "vue"
import { useUserPOS } from "../../composables/useUserPOS"

const { loading } = useLoader("currency")
const { isModalOpen, closeModal, toggleModal } = useModal("currency")
const { currentCurrencyLabel, resetStateCurrency } = usePosCurrencyHandler()
const { findCurrencyObjectByAccorCurrencyCode } = usePosStore()
const activator = useTemplateRef<HTMLButtonElement>("activator")
const { refreshPOS } = useUserPOS()

const emits = defineEmits(["close:currencyModal"])
defineProps<CurrencyModalProps>()

const geographicalArea = defineModel<string>("geographicalArea")
const currency = defineModel<string>("currency")

const closeCurrencyModal = () => {
  closeModal()
  emits("close:currencyModal")
}

async function handleSubmitCurrencyModal() {
  if (!currency.value) return

  loading.value = true

  const currencyObject = findCurrencyObjectByAccorCurrencyCode(currency.value)
  if (!currencyObject) return

  await updateCurrencyCookies(currencyObject)
  refreshPOS()
  resetStateCurrency()

  loading.value = false

  closeModal()
}
</script>

<template>
  <div ref="currency-modal" class="Currency-modal">
    <UiButton
      ref="activator"
      class="Currency-modal__button"
      :text="currentCurrencyLabel || ''"
      :size="UiButtonSize.SMALL"
      @click="toggleModal"
    />

    <UiHeaderModal :activator="activator" :is-open="isModalOpen" @ui-header-modal::close="closeCurrencyModal">
      <div class="Currency-modal__content">
        <h5 class="Currency-modal__title">{{ $t("components.currency_modal.select_currency") }}</h5>

        <form class="Currency-modal__form">
          <UiSelect
            v-model="geographicalArea"
            :label="$t('components.currency_modal.geographical_area')"
            :options="geographicalOptions"
            required
          />
          <UiSelect
            v-model="currency"
            :default-selected="currency"
            :label="$t('components.currency_modal.currency')"
            :options="currencyOptions"
            required
          />
        </form>

        <UiButton
          class="Currency-modal__confirm"
          type="button"
          :is-loading="loading"
          :text="$t('components.currency_modal.confirm')"
          :variation="UiButtonVariation.SECONDARY"
          @click="handleSubmitCurrencyModal"
        />
      </div>
    </UiHeaderModal>
  </div>
</template>

<style lang="scss" scoped src="./CurrencyModal.scss"></style>
