<script setup lang="ts">
import { computed, ref, watch } from "vue"
import { Icon } from "@sb-assets/icons"
import { InputPasswordError } from "./enum"
import { InputPasswordProps } from "./interface"
import UiIcon from "@sb-components/atoms/UiIcon/UiIcon.vue"
import UiInputPassword from "@sb-components/atoms/UiInputPassword/UiInputPassword.vue"
import { UiInputPasswordStatus } from "@sb-components/atoms/UiInputPassword/enum"
import { passwordSchema } from "@/helpers/zod"

defineProps<InputPasswordProps>()

const password = defineModel<string>({ default: "" })
const errors = ref<InputPasswordError[]>([InputPasswordError.NO_LOWERCASE])

const inputStatus = computed(() => {
  if (!password.value.length) return undefined

  return errors.value.length ? UiInputPasswordStatus.ERROR : UiInputPasswordStatus.SUCCESS
})

watch(password, () => {
  const validatedSchema = passwordSchema.safeParse(password.value)
  errors.value = []

  if (validatedSchema.error) {
    const error = validatedSchema.error.format()
    errors.value = error._errors as InputPasswordError[]
  }
})

const criteriaIcon = (criteria: InputPasswordError): Icon => {
  if (password.value.length === 0) return "checkDefault"

  return errors.value.includes(criteria) ? "warn" : "checkRounded"
}

const iconClass = (criteria: InputPasswordError) => {
  if (password.value.length === 0) return ""

  return errors.value.includes(criteria) ? "Input-password__criteria--error" : "Input-password__criteria--success"
}
</script>

<template>
  <UiInputPassword
    v-model="password"
    :label="label"
    :placeholder="placeholder"
    :assistive="assistiveText"
    :status="inputStatus"
    required
  >
    <template #rules>
      <div class="Input-password__criterias">
        <p class="Input-password__additional-title">{{ $t("components.input_password.password_criterias") }}</p>

        <ul class="Input-password__criterias-list">
          <li class="Input-password__criteria" :class="iconClass(InputPasswordError.TOO_SHORT_OR_LONG)">
            <UiIcon :name="criteriaIcon(InputPasswordError.TOO_SHORT_OR_LONG)" />
            <span>{{ $t("components.input_password.too_short_or_long") }}</span>
          </li>

          <li class="Input-password__criteria" :class="iconClass(InputPasswordError.NO_LOWERCASE)">
            <UiIcon :name="criteriaIcon(InputPasswordError.NO_LOWERCASE)" />
            <span>{{ $t("components.input_password.no_lowercase") }}</span>
          </li>

          <li class="Input-password__criteria" :class="iconClass(InputPasswordError.NO_UPPERCASE)">
            <UiIcon :name="criteriaIcon(InputPasswordError.NO_UPPERCASE)" />
            <span>{{ $t("components.input_password.no_uppercase") }}</span>
          </li>

          <li class="Input-password__criteria" :class="iconClass(InputPasswordError.NO_NUMBER_OR_SYMBOL)">
            <UiIcon :name="criteriaIcon(InputPasswordError.NO_NUMBER_OR_SYMBOL)" />
            <span>{{ $t("components.input_password.no_number_or_symbol") }}</span>
          </li>
        </ul>
      </div>
    </template>
  </UiInputPassword>
</template>

<style lang="scss" scoped src="./InputPassword.scss"></style>
