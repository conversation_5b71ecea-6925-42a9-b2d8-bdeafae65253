<script setup lang="ts">
import { computed, watch } from "vue"
import {
  mockStoreEnhanceStep,
  mockStoreSearchStep,
  mockStoreStayStep
} from "@sb-components/molecules/UiBookingSummary/mockData"
import {
  useAccessibilitiesFacilities,
  useCurrentHotel,
  useCurrentWindowSize,
  useLoader,
  useOffersAndAccommodations,
  usePricingCondition,
  useSearch,
  useSearchEngineSectionValidation
} from "../../composables"
import AccessibilityModal from "./AccessibilityModal/AccessibilityModal.vue"
import { BlocInteractionEnum } from "../../global/enums"
import { DateMoment } from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import { RoomType } from "@sb-components/organisms/UiRoom/types"
import UiBookingSummary from "@sb-components/molecules/UiBookingSummary/UiBookingSummary.vue"
import { UiBookingSummaryProps } from "@sb-components/molecules/UiBookingSummary/interface"
import { UiBookingSummaryStep } from "@sb-components/molecules/UiBookingSummary/enums"
import UiCancellationPolicy from "@sb-components/molecules/UiCancellationPolicy/UiCancellationPolicy.vue"
import { UiCancellationPolicyVariant } from "@sb-components/molecules/UiCancellationPolicy/enums"
import UiComplementaryInformation from "@sb-components/molecules/UiComplementaryInformation/UiComplementaryInformation.vue"
import UiPricingDetails from "@sb-components/molecules/UiPricingDetails/UiPricingDetails.vue"
import UiSidebarAccordion from "@sb-components/molecules/UiSidebarAccordion/UiSidebarAccordion.vue"
import { UiSidebarAccordionProps } from "@sb-components/molecules/UiSidebarAccordion/interface"
import { accommodationMapper } from "../../services/accommodation/accommodation.mapper"
import { pricingConditionMapper } from "../../services/offer/offer.mapper"
import { replaceUriParams } from "../../helpers/uriHelper"
import { useBasket } from "../../composables/useBasket"
import { useI18n } from "vue-i18n"
import { useRoute } from "vue-router"
import { useUserPOS } from "../../composables/useUserPOS"

const { accessibilities, loadAccessibilities } = useAccessibilitiesFacilities()
const { hotel: currentHotel } = useCurrentHotel()
const { dates, hotel, rooms, lengthOfStay } = useSearch()
const { isDesktop } = useCurrentWindowSize()
const { basket } = useBasket()
const { userPOS } = useUserPOS()
const { locale } = useI18n()
const { hasAccessibility, offers, sections, getOfferAccommodationById } = useOffersAndAccommodations()
const { loading } = useLoader("search")
const { cancellationPolicies, getPricingConditionById } = usePricingCondition()
const route = useRoute()

const { handleSearchSubmit } = useSearchEngineSectionValidation()

const displayPricingDetails = computed(
  () => route.name !== UiBookingSummaryStep.SEARCH && !!basket.value && pricingDetails.value
)

const pricingDetails = computed(() => {
  if (!basket.value) return null

  //TODO replace hard coded value
  return {
    hotelCurrencyPrices: {
      currency: basket.value.hotelCurrency,
      hotelAmount: "$4071",
      onlineAmount: "$0",
      roomPrice: "$1767",
      taxAndFeesPrice: "$170",
      totalPrice: basket.value.pricing.formattedTotalAmountHotelCurrency
    },
    numberOfNight: lengthOfStay.value,
    userCurrencyPrices:
      basket.value.currency !== basket.value.hotelCurrency
        ? {
            currency: basket.value.currency,
            hotelAmount: "4071",
            onlineAmount: "0",
            totalPrice: basket.value.pricing.formattedTotalAmount
          }
        : undefined
  }
})

const shouldDisplayAccessibilityComponent = computed(() => {
  return route.name !== UiBookingSummaryStep.SEARCH && hasAccessibility.value && offers.value.length > 0
})

// TODO: Change mockStoreData with real data
const mockStoreData = computed<UiBookingSummaryProps>(() => {
  switch (route.name) {
    case UiBookingSummaryStep.ENHANCE:
      return mockStoreEnhanceStep
    case UiBookingSummaryStep.SEARCH:
      return mockStoreSearchStep
    case UiBookingSummaryStep.STAY:
      return mockStoreStayStep
    default:
      return mockStoreSearchStep
  }
})

const adultsCount = computed(() => {
  let count = 0
  rooms.value.forEach((room) => {
    count += room.adults
  })

  return count as number
})

const childrenCount = computed(() => {
  let count = 0
  rooms.value.forEach((room) => {
    count += room.children
  })

  return count as number
})

const formattedRooms = computed<RoomType[]>(() => [
  ...rooms.value.map((room) => {
    const productCode = room.productCode
    const rateId = room.rateId
    const pair = sections.value
      .flatMap((section) => section.pairs)
      .find((pair) => pair.offerMapped.roomCard.productCode === productCode)?.offerMapped

    return {
      ...room,
      offerDetails: {
        ...room.offerDetails,
        rateTitle: basket.value?.items.find((item) => item.offerId === rateId)?.detail.rateLabel as string,
        roomBedding: pair?.roomCard.bedding,
        roomMedias: pair?.roomCard.gallery.medias,
        roomTitle: pair?.roomCard.title
      }
    }
  })
])

const bookingSummaryData = computed<UiBookingSummaryProps & UiSidebarAccordionProps>(() => {
  return {
    adults: adultsCount.value,
    checkInHour: currentHotel.value?.checkInHour,
    checkOutHour: currentHotel.value?.checkOutHour,
    children: childrenCount.value,
    currency: userPOS.currency,
    currentRoomId: currentlySelectedRoomId.value,
    dateIn: dates.value[DateMoment.START],
    dateOut: dates.value[DateMoment.END],
    images: currentHotel.value?.medias,
    price: basket.value?.pricing.formattedTotalAmount || "",
    rooms: formattedRooms.value,
    step: mockStoreData.value.step,
    title: currentHotel.value?.name
  }
})

const moreNumbersUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_MORE_NUMBERS, "", locale.value)
})

const bestPriceGuarantee = computed(() => {
  return replaceUriParams(
    import.meta.env.VITE_APP_ALL_BEST_PRICE_GUARANTEE_CONDITIONS,
    window.location.origin,
    locale.value
  )
})

const help = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_ALL_HELP, window.location.origin, locale.value)
})

watch(
  () => hotel.value?.id,
  (hotelId) => {
    if (hotelId) {
      loadAccessibilities(hotelId)
    }
  }
)

const currentlySelectedRoomId = computed(() => rooms.value.findIndex((room) => !room.productCode || !room.rateCode))

const loadRateModalDetails = async (roomId: number) => {
  const roomIndex = rooms.value.findIndex((room) => room.id === roomId)
  const room = rooms.value[roomIndex]
  if (!room?.productCode || !room.rateId) return

  const pricingDetails = await getPricingConditionById(room.rateId)

  if (!pricingDetails) return

  rooms.value[roomIndex].offerDetails = {
    ...rooms.value[roomIndex].offerDetails,
    rateModalContent: pricingConditionMapper(pricingDetails)
  }
}

const loadRoomModalDetails = async (roomId: number) => {
  const roomIndex = rooms.value.findIndex((room) => room.id === roomId)
  const room = rooms.value[roomIndex]
  if (!room?.productCode || !room.rateId) return

  const offer = offers.value.find((offer) => offer.product.id === room.productCode)
  if (!offer) return

  const accommodation = await getOfferAccommodationById(offer)

  rooms.value[roomIndex].offerDetails = {
    ...rooms.value[roomIndex].offerDetails,
    roomModalContent: accommodationMapper(offer.product, accommodation)
  }
}
</script>

<template>
  <aside class="Sidebar">
    <UiSidebarAccordion
      v-if="!isDesktop && route.name !== UiBookingSummaryStep.SEARCH && basket?.id"
      :currency="bookingSummaryData.currency"
      :price="bookingSummaryData.price"
    >
      <UiBookingSummary
        v-if="hotel?.id && rooms.length > 0 && currentHotel?.medias?.length"
        v-bind="bookingSummaryData"
        :loading="loading"
        @ui-booking-summary::load-rate-modal="loadRateModalDetails"
        @ui-booking-summary::load-room-modal="loadRoomModalDetails"
      />
      <UiPricingDetails v-if="displayPricingDetails && pricingDetails" v-bind="pricingDetails" />

      <UiCancellationPolicy
        v-if="cancellationPolicies && route.name !== UiBookingSummaryStep.SEARCH"
        v-bind="cancellationPolicies"
        :variant="UiCancellationPolicyVariant.SIDE_BAR"
      />
    </UiSidebarAccordion>

    <template v-if="isDesktop">
      <UiBookingSummary
        v-if="
          hotel?.id &&
          rooms.length > 0 &&
          bookingSummaryData.dateIn &&
          bookingSummaryData.dateOut &&
          currentHotel?.medias?.length
        "
        v-bind="bookingSummaryData"
        :loading="loading"
        @ui-booking-summary::load-rate-modal="loadRateModalDetails"
        @ui-booking-summary::load-room-modal="loadRoomModalDetails"
        @ui-booking-summary::validate="handleSearchSubmit(BlocInteractionEnum.RIGHT)"
      />

      <UiPricingDetails
        v-if="displayPricingDetails && displayPricingDetails && pricingDetails"
        v-bind="pricingDetails"
      />

      <UiCancellationPolicy
        v-if="cancellationPolicies && route.name !== UiBookingSummaryStep.SEARCH"
        v-bind="cancellationPolicies"
      />
    </template>

    <UiComplementaryInformation
      v-if="shouldDisplayAccessibilityComponent"
      :title="$t('components.sidebar.accessible_rooms_title')"
    >
      <template #content>
        <p class="body-02">{{ $t("components.sidebar.accessible_rooms_text") }}</p>
        <AccessibilityModal v-bind="accessibilities" />
      </template>
    </UiComplementaryInformation>

    <UiComplementaryInformation
      :title="$t('components.sidebar.best_price_title')"
      :top-link-text="$t('components.sidebar.best_price_top_link_text')"
      :top-link-href="bestPriceGuarantee"
      top-link-target="_blank"
    >
      <template #content>
        <p class="body-02">{{ $t("components.sidebar.best_price_text") }}</p>
      </template>
    </UiComplementaryInformation>

    <UiComplementaryInformation
      :title="$t('components.sidebar.need_help_title')"
      :top-link-text="$t('components.sidebar.need_help_top_link_text')"
      :top-link-href="moreNumbersUri"
      top-link-target="_blank"
      :bottom-link-text="$t('components.sidebar.need_help_bottom_link_text')"
      :bottom-link-href="help"
      bottom-link-target="_blank"
    >
      <template #content>
        <p class="body-02">{{ $t("components.sidebar.need_help_text_1") }}</p>
        <p class="body-02">{{ $t("components.sidebar.need_help_text_2") }}</p>
      </template>
    </UiComplementaryInformation>
  </aside>
</template>
<style lang="scss" scoped src="./Sidebar.scss"></style>
