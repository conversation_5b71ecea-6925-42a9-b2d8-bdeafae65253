<script setup lang="ts">
import { ActionAlignment } from "@sb-components/molecules/UiModal/enums"
import UiAccessibilityModalContent from "@sb-components/molecules/UiAccessibilityModalContent/UiAccessibilityModalContent.vue"
import { UiAccessibilityModalContentProps } from "@sb-components/molecules/UiAccessibilityModalContent/interface"
import UiLink from "@sb-components/atoms/UiLink/UiLink.vue"
import { UiLinkVariant } from "@sb-components/atoms/UiLink/enums"
import UiModal from "@sb-components/molecules/UiModal/UiModal.vue"
import { ref } from "vue"

const accessibilityModalProps = defineProps<UiAccessibilityModalContentProps>()

const isOpen = ref<boolean>(false)
</script>
<template>
  <div class="Accessibility-modal">
    <UiLink
      type="button"
      :text="$t('components.sidebar.accessible_rooms_button_text')"
      class="caption-01-underline"
      :variant="UiLinkVariant.NEUTRAL"
      @click="isOpen = true"
    />
    <UiModal
      v-if="isOpen"
      :action-alignment="ActionAlignment.FULL"
      no-header-shadow
      with-overlay
      @ui-modal::close="isOpen = false"
    >
      <UiAccessibilityModalContent v-bind="accessibilityModalProps" />
    </UiModal>
  </div>
</template>
