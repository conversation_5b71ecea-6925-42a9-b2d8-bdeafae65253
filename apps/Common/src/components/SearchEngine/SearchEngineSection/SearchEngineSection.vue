<script setup lang="ts">
import { onMounted, ref } from "vue"
import { useSearch, useSearchEngineSectionValidation } from "../../../composables"
import { BlocInteractionEnum } from "../../../global/enums"
import DatePickerDropdown from "../../SearchCriteria/DatePicker/DatePickerDropdown/DatePickerDropdown.vue"
import { DatePickerVariant } from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import DestinationDropdown from "../../SearchCriteria/Destination/DestinationDropdown/DestinationDropdown.vue"
import { DestinationVariant } from "@sb-components/organisms/UiDestination/UiDestinationDesktop/enums"
import RoomsAndGuestsSection from "../../SearchCriteria/RoomsAndGuests/RoomsAndGuestsSection/RoomsAndGuestsSection.vue"
import { SearchEngineSectionProps } from "./interface"
import SpecialRatesSection from "../../SearchCriteria/SpecialRatesSection/SpecialRatesSection.vue"
import UiButton from "@sb-components/atoms/UiButton/UiButton.vue"
import UiSection from "@sb-components/molecules/UiSection/UiSection.vue"
import { watchDebounced } from "@vueuse/core"

defineProps<SearchEngineSectionProps>()

const {
  datePickerDropdownReference,
  datePickerErrorMessage,
  datePickerErrorMessageValidation,
  datePickerInputsAreValid,
  destinationDropdownIsOpen,
  destinationDropdownIsValid,
  destinationErrorMessage,
  destinationErrorMessageValidation,
  destinationInput,
  filteredDestinationsFallback,
  handleSearchSubmit,
  iataCodeErrorMessage,
  promoCodeErrorMessage,
  roomsAndGuestsInError,
  roomsAndGuestsSingleDropdownValidation,
  validateSpecialRatesSection
} = useSearchEngineSectionValidation()

const { dates, iataCode, hotel, specialRate, promoCode } = useSearch()

const datePickerDropdown = ref()

onMounted(() => {
  datePickerDropdownReference.value = datePickerDropdown.value
})

watchDebounced(
  destinationInput,
  () => {
    destinationErrorMessageValidation(true)
  },
  { debounce: 300, maxWait: 300 }
)
</script>

<template>
  <form ref="searchEngineSection" class="Search-engine-section">
    <UiSection :title="$t('components.search_criteria.your_trip')">
      <template #content>
        <div class="Search-engine-section__content">
          <DestinationDropdown
            id="Destination-dropdown"
            v-model:hotel="hotel"
            v-model:input-filter="destinationInput"
            v-model:dropdown-is-open="destinationDropdownIsOpen"
            :error-message="destinationErrorMessage"
            :is-loading="isLoading"
            :is-valid="destinationDropdownIsValid"
            :items="filteredDestinationsFallback"
            :variant="DestinationVariant.BORDERED"
          />

          <DatePickerDropdown
            id="Date-picker-dropdown"
            ref="datePickerDropdown"
            v-model="dates"
            :error-message="datePickerErrorMessage"
            :inputs-are-valid="datePickerInputsAreValid"
            :max-date="maxDate"
            :max-range="maxRange"
            :min-range="minRange"
            :variant="DatePickerVariant.BORDERED"
            @date-picker-dropdown::change-start-date="datePickerErrorMessageValidation($event, true)"
            @date-picker-dropdown::change-end-date="datePickerErrorMessageValidation($event, true)"
          />
        </div>
      </template>
    </UiSection>

    <div id="Rooms-and-guests-section">
      <RoomsAndGuestsSection
        :max-room="maxRoom"
        :max-pax="maxPax"
        :max-adult="maxAdult"
        :max-child="maxChild"
        :max-child-age="maxChildAge"
        :rooms-and-guests-in-error="roomsAndGuestsInError"
        @rooms-and-guests-section::validate-children-age="roomsAndGuestsSingleDropdownValidation($event)"
      />
    </div>

    <div id="Special-rates-section">
      <SpecialRatesSection
        v-model:iata-code="iataCode"
        v-model:special-rate="specialRate"
        v-model:promo-code="promoCode"
        :iata-error-message="iataCodeErrorMessage"
        :promo-error-message="promoCodeErrorMessage"
        @special-rates-section::validate="validateSpecialRatesSection"
      />
    </div>

    <UiButton
      :text="$t('components.search_engine.check_rates')"
      class="Search-engine-section__button"
      :is-loading="loading"
      @click="handleSearchSubmit(BlocInteractionEnum.BOTTOM)"
    />
  </form>
</template>

<style lang="scss" scoped src="./SearchEngineSection.scss"></style>
