@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Search-engine-desktop {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: map.get(spaces.$sizes, "4");
  padding-block: map.get(spaces.$sizes, "6");
  box-shadow: map.get(boxes.$shadows, "bottom");

  &__update {
    :deep(.ads-button) {
      @include text.lbf-text("body-01-uppercase");
      padding-left: 2.8rem; // custom padding because of added random 4px on the figma
      padding-right: 2.8rem; // custom padding because of added random 4px on the figma
    }
  }

  // Destination modal alignment, specific to this version of the search engine
  :deep(.Dropdown-modal__content) {
    top: calc(100% + (3rem));
  }
}
