<script setup lang="ts">
import { ref, watch } from "vue"
import { useLoader, useSearch, useSearchConstraints } from "../../../composables"
import { ActionAlignment } from "@sb-components/molecules/UiModal/enums"
import SearchEngineMobile from "../SearchEngineMobile/SearchEngineMobile.vue"
import { SearchEngineModalProps } from "./interface"
import UiButton from "@sb-components/atoms/UiButton/UiButton.vue"
import UiMobileSearchResume from "@sb-components/molecules/UiMobileSearchResume/UiMobileSearchResume.vue"
import UiModal from "@sb-components/molecules/UiModal/UiModal.vue"

defineProps<SearchEngineModalProps>()
const emits = defineEmits(["SearchEngineModal::check"])

const { loading } = useLoader("search")
const { dateIn, dateOut, hotel, rooms, specialRate, dates, iataCode, promoCode } = useSearch()
const { calendarConstraints, occupancyConstraints } = useSearchConstraints(hotel.value?.id)

const isOpen = ref(false)
const localDates = ref(dates.value)
const localHotel = ref(hotel.value)
const localRooms = ref(rooms.value)
const localIataCode = ref(iataCode.value)
const localSpecialRate = ref(specialRate.value)
const localPromoCode = ref(promoCode.value)
const searchEngineMobile = ref()

const onSubmit = () => {
  loading.value = true
  hotel.value = localHotel.value
  dates.value = localDates.value
  rooms.value = localRooms.value
  specialRate.value = localSpecialRate.value
  promoCode.value = localPromoCode.value
  iataCode.value = localIataCode.value

  searchEngineMobile.value.handleSubmit()
}

const handleCheck = () => {
  emits("SearchEngineModal::check")
}

watch(loading, (newLoading) => {
  if (!newLoading) {
    isOpen.value = false
  }
})
</script>

<template>
  <div class="Search-engine-modal">
    <UiMobileSearchResume
      :date-in="dateIn"
      :date-out="dateOut"
      :hotel="hotel"
      :rooms="rooms"
      :special-rate="specialRate"
      :iata-code="localIataCode"
      @ui-mobile-search-resume::edit="isOpen = true"
    />

    <UiModal :is-open="isOpen" :action-alignment="ActionAlignment.FULL" @ui-modal::close="isOpen = false">
      <template #title>
        <h5 class="Search-engine-modal__title">{{ $t("components.search_engine.modify_your_search") }}</h5>
      </template>

      <SearchEngineMobile
        ref="searchEngineMobile"
        v-model:hotel="localHotel"
        v-model:dates="localDates"
        v-model:rooms="localRooms"
        v-model:iata-code="localIataCode"
        v-model:special-rate="localSpecialRate"
        v-model:promo-code="localPromoCode"
        :has-check-rates-button="false"
        :max-adult="occupancyConstraints?.maxAdult"
        :max-child="occupancyConstraints?.maxChild"
        :max-child-age="occupancyConstraints?.maxChildAge"
        :max-pax="occupancyConstraints?.maxPax"
        :max-room="occupancyConstraints?.maxRoom"
        :max-date="calendarConstraints?.maxDate"
        :max-range="calendarConstraints?.maxLengthOfStay"
        :min-range="calendarConstraints?.minLengthOfStay"
        @search-engine-mobile::check="handleCheck"
      />

      <template #actions>
        <UiButton :text="$t('components.search_engine.update_search')" :is-loading="loading" @click="onSubmit" />
      </template>
    </UiModal>
  </div>
</template>

<style lang="scss" scoped src="./SearchEngineModal.scss"></style>
