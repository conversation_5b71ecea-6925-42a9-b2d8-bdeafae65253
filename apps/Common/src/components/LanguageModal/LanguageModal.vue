<script setup lang="ts">
import { UiButtonSize, UiButtonVariation } from "@sb-components/atoms/UiButton/enums"
import { useLoader, useModal, usePosCurrencyHandler, usePosLanguageHandler } from "../../composables"
import { LanguageModalProps } from "./interface"
import UiButton from "@sb-components/atoms/UiButton/UiButton.vue"
import UiHeaderModal from "@sb-components/molecules/UiHeaderModal/UiHeaderModal.vue"
import UiSelect from "@sb-components/atoms/UiSelect/UiSelect.vue"
import { updateLanguageCookies } from "../../services/pos/pos.fetch"
import { useI18n } from "vue-i18n"
import { usePosStore } from "@stores/pos/pos"
import { useTemplateRef } from "vue"
import { useUserPOS } from "../../composables/useUserPOS"

const { locale } = useI18n()
const { loading } = useLoader("language")
const { isModalOpen, closeModal, toggleModal } = useModal("language")
const { resetStateCurrency } = usePosCurrencyHandler()
const { countryRegionLabel, handleLanguageChange, resetState } = usePosLanguageHandler()
const { findCountryObjectByAccorCountryCode } = usePosStore()
const activator = useTemplateRef<HTMLButtonElement>("activator")
const { refreshPOS } = useUserPOS()

const emits = defineEmits(["close:languageModal"])
defineProps<LanguageModalProps>()

const geographicalArea = defineModel<string>("geographicalArea")
const country = defineModel<string>("country")

const closeLanguageModal = () => {
  closeModal()
  emits("close:languageModal")
}

async function handleSubmitLanguageModal() {
  loading.value = true

  const country = findCountryObjectByAccorCountryCode(countryRegionLabel.value)

  if (!country) return

  await updateLanguageCookies(country)
  refreshPOS()
  resetState()
  await handleLanguageChange()
  resetStateCurrency()

  loading.value = false

  closeModal()
}
</script>

<template>
  <div class="Language-modal">
    <UiButton
      ref="activator"
      class="Language-modal__button"
      :text="locale"
      :size="UiButtonSize.SMALL"
      @click="toggleModal"
    />

    <UiHeaderModal :activator="activator" :is-open="isModalOpen" @ui-header-modal::close="closeLanguageModal">
      <div class="Language-modal__content">
        <h5 class="Language-modal__title">{{ $t("components.language_modal.select_language") }}</h5>

        <form class="Language-modal__form">
          <UiSelect
            v-model="geographicalArea"
            :default-selected="geographicalArea"
            :label="$t('components.language_modal.geographical_area')"
            :options="geographicalOptions"
            required
          />

          <UiSelect
            v-model="country"
            :default-selected="country"
            :label="$t('components.language_modal.language')"
            :options="countryOptions"
            required
          />
        </form>

        <UiButton
          class="Language-modal__confirm"
          type="button"
          :is-loading="loading"
          :text="$t('components.language_modal.confirm')"
          :variation="UiButtonVariation.SECONDARY"
          @click="handleSubmitLanguageModal"
        />
      </div>
    </UiHeaderModal>
  </div>
</template>

<style lang="scss" scoped src="./LanguageModal.scss"></style>
