<script setup lang="ts">
import { computed, inject } from "vue"
import { BrandConfiguration } from "@shared/types"
import UiFooter from "@sb-components/organisms/UiFooter/UiFooter.vue"
import type { UiFooterProps } from "@sb-components/organisms/UiFooter/interface"
import { brandConfiguration } from "../../global/consts"
import { replaceUriParams } from "../../helpers/uriHelper"
import { useI18n } from "vue-i18n"

const config = inject<BrandConfiguration>("brandConfig")!
const { locale, t } = useI18n()

const moreNumbersUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_MORE_NUMBERS, "", locale.value)
})

const termsAndConditionsUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_TERMS_AND_CONDITIONS, "", locale.value)
})

const privacyPolicyUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_ALL_PRIVACY_POLICY, brandConfiguration.allHostname, locale.value)
})

const helpContactUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_ALL_HELP_CONTACT, brandConfiguration.legacy.hostname, locale.value)
})

const sitemapUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_SITEMAP, "", locale.value)
})

const accessibilityUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_WEB_ACCESSIBILITY, "", locale.value)
})

const homepageUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_HOMEPAGE, "", locale.value)
})

const footerProps = computed<UiFooterProps>(() => ({
  copyright: t("components.footer.copyright"),
  currentYear: new Date().getFullYear(),
  helpSection: {
    link: {
      href: moreNumbersUri.value,
      target: "_blank",
      text: t("components.footer.help_section.link")
    },
    mainContent: t("components.footer.help_section.main_content"),
    secondaryContent: t("components.footer.help_section.secondary_content"),
    title: t("components.footer.help_section.title")
  },
  links: [
    {
      href: termsAndConditionsUri.value,
      target: "_blank",
      text: t("components.footer.links.terms_conditions")
    },
    {
      href: privacyPolicyUri.value,
      target: "_blank",
      text: t("components.footer.links.privacy_policy")
    },
    {
      href: helpContactUri.value,
      target: "_blank",
      text: t("components.footer.links.do_not_sell")
    },
    {
      onClick: (e: Event) => {
        e.preventDefault()
        window.Optanon.ToggleInfoDisplay()
      },
      text: t("components.footer.links.cookies_preferences")
    },
    {
      href: sitemapUri.value,
      target: "_blank",
      text: t("components.footer.links.sitemap")
    },
    {
      href: accessibilityUri.value,
      target: "_blank",
      text: t("components.footer.links.accessibility")
    }
  ],
  logo: config.logoInvert,
  logoLink: homepageUri.value
}))
</script>

<template>
  <UiFooter v-bind="footerProps" />
</template>
