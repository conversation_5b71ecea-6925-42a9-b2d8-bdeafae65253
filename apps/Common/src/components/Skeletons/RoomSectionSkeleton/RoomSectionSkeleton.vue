<script setup lang="ts">
import RoomSectionTitleSkeleton from "../RoomSectionTitleSkeleton/RoomSectionTitleSkeleton.vue"
import UiRoomCardSkeleton from "@sb-components/skeletons/UiRoomCardSkeleton/UiRoomCardSkeleton.vue"
</script>
<template>
  <div class="Room-section-skeleton">
    <RoomSectionTitleSkeleton />

    <UiRoomCardSkeleton v-for="index in 3" :key="index" />
  </div>
</template>

<style lang="scss" scoped src="./RoomSectionSkeleton.scss"></style>
