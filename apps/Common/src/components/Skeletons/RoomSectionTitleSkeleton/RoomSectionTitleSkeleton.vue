<script setup lang="ts">
import UiIcon from "@sb-components/atoms/UiIcon/UiIcon.vue"
import UiSkeleton from "@sb-components/atoms/UiSkeleton/UiSkeleton.vue"
</script>
<template>
  <div class="Room-section-title-skeleton">
    <UiSkeleton width="100%" height="2.4rem" border-radius=".3rem" class="Room-section-title-skeleton__main-title" />
    <UiIcon class="Room-section-title-skeleton__ellipsis" name="ellipsis" />
    <UiSkeleton width="100%" height="1.6rem" border-radius=".3rem" class="Room-section-title-skeleton__sub-title" />
  </div>
</template>

<style lang="scss" scoped src="./RoomSectionTitleSkeleton.scss"></style>
