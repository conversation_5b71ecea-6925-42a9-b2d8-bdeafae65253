@use "sass:map";
@use "@sb-base/helpers";
@use "@sb-config/colors";
@use "@sb-config/spaces";
@use "@sb-config/text";
@use "@sb-config/fonts";
@use "@sb-config/boxes";
@use "@sb-utilities/mq";

.Room-section-title-skeleton {
  z-index: 1;
  position: sticky;
  top: 0;
  display: flex;
  flex-direction: column;
  margin: 0 -1.6rem;
  padding: map.get(spaces.$sizes, "5") 1.8rem;
  gap: map.get(spaces.$sizes, "4");
  background-color: map.get(colors.$pearlGrey, "200");

  @include mq.media(">=small") {
    flex-direction: row;
    position: relative;
    z-index: initial;
    gap: map.get(spaces.$sizes, "5");
    margin: initial;
    padding: 0;
    background-color: unset;
    align-items: center;
  }

  &__main-title {
    @include mq.media(">=small") {
      width: 17.5rem;

      :deep(.ads-skeleton) {
        height: 3rem;
      }
    }
  }

  &__sub-title {
    @include mq.media(">=small") {
      width: 11.5rem;
    }
  }

  &__ellipsis {
    display: none;
    color: map.get(colors.$caviarBlack, "500");

    @include mq.media(">=small") {
      display: initial;
      font-size: 0.4rem;
      color: map.get(colors.$caviarBlack, "300");
    }
  }
}
