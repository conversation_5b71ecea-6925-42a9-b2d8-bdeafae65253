<script setup lang="ts">
import { PageTitleProps } from "./interface"
import { useCurrentWindowSize } from "../../composables"

defineProps<PageTitleProps>()

const { isDesktop } = useCurrentWindowSize()
</script>

<template>
  <h1 class="page-title">
    <span :class="!isDesktop ? 'exp-heading-04-alt' : 'exp-heading-03-alt'">
      {{ left }}
    </span>
    <span :class="!isDesktop ? 'exp-heading-04' : 'exp-heading-03'">
      {{ middle }}
    </span>
    <span :class="!isDesktop ? 'exp-heading-04-alt' : 'exp-heading-03-alt'">
      {{ right }}
    </span>
  </h1>
</template>
