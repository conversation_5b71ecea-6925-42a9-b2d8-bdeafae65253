<script setup lang="ts">
import { computed } from "vue"

const legalMail = computed(() => import.meta.env.VITE_APP_LEGAL_MAIL)
</script>

<template>
  <div class="Legal-text caption-01 color-customs-text-light">
    <i18n-t keypath="components.legal_text.legal.first_paragraph" tag="p">
      <template #link>
        <a :href="`mailto:${legalMail}`">{{ legalMail }}</a>
      </template>
    </i18n-t>

    <p class="Legal-text__first-paragraph">{{ $t("components.legal_text.legal.second_paragraph") }}</p>
    <p>{{ $t("components.legal_text.legal.third_paragraph") }}</p>
  </div>
</template>

<style lang="scss" scoped src="./LegalText.scss"></style>
