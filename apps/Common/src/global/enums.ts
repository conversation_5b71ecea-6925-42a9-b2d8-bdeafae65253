export enum OidcEnpoint {
  logout = "/booking/oidc/logout",
  refresh = "/booking/oidc/refresh"
}

//TODO update with other currency
export enum Currency {
  AED = "AED",
  AUD = "AUD",
  AZN = "AZN",
  CAD = "CAD",
  CHF = "CHF",
  CNY = "CNY",
  EUR = "EUR",
  GBP = "GBP",
  IDR = "IDR",
  INR = "INR",
  MAD = "MAD",
  QAR = "QAR",
  SGD = "SGD",
  USD = "USD"
}

export enum EcommerceEventNameEnum {
  begin_checkout = "begin_checkout",
  add_payment_info = "add_payment_info",
  purchase = "purchase"
}

export enum EventNameEnum {
  bloc_interact = "bloc_interact",
  bloc_view = "bloc_view",
  cta_authentication = "cta_authentication",
  funnel_complete = "funnel_complete",
  funnel_error = "funnel_error",
  funnel_start = "funnel_start",
  funnel_step = "funnel_step",
  payment_submit = "payment_submit"
}

export enum Region {
  AFRICA = "Africa",
  ASIA = "Asia",
  EUROPE = "Europe",
  NORTH_AMERICA = "North America",
  SOUTH_AMERICA = "South America"
}

export enum RegionPriority {
  africa = 4,
  asia = 5,
  europe = 3,
  northAmerica = 1,
  southAmerica = 2
}

export enum BlocInteractionEnum {
  RIGHT = "right",
  BOTTOM = "bottom"
}
