export const brandConfiguration = {
  allHostname: import.meta.env.VITE_APP_ALL_HOSTNAME,
  apiHeaders: {
    clientId: import.meta.env.VITE_APP_CLIENT_ID
  },
  apiUrl: import.meta.env.VITE_APP_API_BASE_URL,
  brandCode: import.meta.env.VITE_APP_BRAND_CODE,
  legacy: {
    endpoints: {
      moreNumbers: import.meta.env.VITE_APP_LEGACY_MORE_NUMBERS,
      sitemap: import.meta.env.VITE_APP_LEGACY_SITEMAP,
      termsAndConditions: import.meta.env.VITE_APP_LEGACY_TERMS_AND_CONDITIONS
    },
    hostname: import.meta.env.VITE_APP_LEGACY_HOSTNAME
  },
  myBookingsUrl: import.meta.env.VITE_APP_MY_BOOKINGS_URL,
  name: import.meta.env.VITE_APP_BRAND_NAME,
  paymentModuleApiKey: import.meta.env.VITE_APP_PAYMENT_API_KEY,
  paymentModuleUrl: import.meta.env.VITE_APP_PAYMENT_IFRAME_MODULE,
  siteCode: import.meta.env.VITE_APP_SITE_CODE,
  step3Permalink: {
    client: import.meta.env.VITE_APP_STEP_3_CLIENT,
    host: import.meta.env.VITE_APP_STEP_3_PERMALINK_HOST,
    path: import.meta.env.VITE_APP_STEP_3_PERMALINK_PATH
  }
}

export const AccorApiEndpoints = {
  accommodations: "/catalog/v1/hotels/{hotelId}/products/accommodations",
  commerceTracking: "/referential/v1/best-visit/cookie-attribution",
  contactMe: "/customer/v1/contacts/me",
  countries: "/referentials/v1/countries",
  createBasket: "/basket/webdirectchannels/v1/baskets",
  facilities: (hotelId: string) => `/catalog/v1/hotels/${hotelId}/facilities/global`,
  getBasket: (basketId: string) => `/basket/webdirectchannels/v1/baskets/${basketId}`,
  hotel: (hotelId: string) => `/catalog/v1/hotels/${hotelId}`,
  hotels: "/catalog/v1/hotels",
  identification: "/availability/v3/identification",
  medias: (hotelId: string) => `/catalog/v1/hotels/${hotelId}/medias`,
  offers: "/businessoffers/v1/hotels/{hotelId}/accommodations/best-offers",
  posCountriesGroupedByContinent: (siteCode: string) => `/referential/v1/pos/countries?siteCode=${siteCode}`,
  posCountryLanguageAuto: (siteCode: string, isoCountryCode: string, languageCode: string) =>
    `/referential/v1/pos/countryLanguageAuto?usecase=externalLink&siteCode=${siteCode}&isoCountryCode=${isoCountryCode}&languageCode=${languageCode}`,
  posCountryLanguageManual: (siteCode: string, countryCode: string, languageCode: string) =>
    `/referential/v1/pos/countryLanguageManual?siteCode=${siteCode}&accorCountryCode=${countryCode}&languageCode=${languageCode}`,
  posCurrenciesGroupedByContinent: "/referential/v1/pos/currencies?groupByContinent=true",
  posCurrencyManual: (currencyCode: string) => `/referential/v1/pos/currencyManual?currencyCode=${currencyCode}`,
  pricing: "/businessoffers/v1/offers/pricing-conditions",
  productOffer: (hotelId: string) => `/businessoffers/v1/hotels/${hotelId}/products/offers`,
  referentialList: "/referentials/v1/list",
  refreshToken: import.meta.env.VITE_OIDC_REFRESH_TOKEN_URI,
  updateBasket: (basketId: string) => `/basket/webdirectchannels/v1/baskets/${basketId}/items`
}

export const AllHotels: string[] = ["1988", "A5A5", "B773"]
