import { EcommerceEventNameEnum } from "./enums"

declare global {
  interface Window {
    dataLayer?: object[]
    Optanon: OptanonInterface
  }
}

export interface OptanonInterface {
  ToggleInfoDisplay: () => void
}

export interface CoordinateType {
  lng: string
  lat: string
}

export interface MediaItem {
  "1024x768"?: string
  "346x260": string
  "120x90": string
  "2048x1536"?: string
  "3000x2250"?: string
}

// --- GTM types (probably need a dedicated file as this part will growth in future)
// TODO change type of each property if needed when implement tracking plan

export interface BlocInteractDataType {
  pagename?: string
  bloc_name?: string
  bloc_interaction?: string
}

export interface CtaAuthenticationDataType {
  pagename?: string
  cta_name?: string
}

export interface EcommerceType {
  ecommerce: {
    coupon?: string
    currency?: string
    items?: Array<{
      index?: number
      item_id?: string
      item_name?: string
      item_category?: string
      item_brand?: string
      item_variant?: string
      price?: string
      quantity?: string
      discount?: string
      location_id?: string
      night_nb?: string
    }>
    tax?: string
    transaction_id?: string
    value?: string
  }
  event?: EcommerceEventNameEnum
}

export interface FunnelStartDataType {
  error_type?: string
  results_nb?: string
  room_number_available?: string
  room_type_available?: string
  step_name?: string
}

export interface EventType {
  eventName?: string
  event_data?: BlocInteractDataType | CtaAuthenticationDataType | FunnelStartDataType // TODO add here any else possible type for dedicated type of event (e.g. OptionsInteractDataType)
}

export interface PageDataType {
  adults_nb?: string
  arrival_date?: string
  children_nb?: string
  departure_date?: string
  dynamic_data_ready?: string
  error_type?: string
  funnel_type?: string
  hotel_city_name?: string
  hotel_continent?: string
  hotel_country_name?: string
  hotel_name?: string
  hotel_region?: string
  hotel_rid_code?: string
  lead_time?: string
  member_rates?: boolean
  merchant_id?: string
  night_nb?: string
  offer_code?: string
  pagename?: string
  page_category?: string
  page_language?: string
  page_sub_category_level1?: string
  page_sub_category_level2?: string
  page_sub_category_level3?: string
  room_nb?: string
  source_id?: string
  partner_id?: string
}

export interface UserDataType {
  country_market?: string
  uiemailsha256?: string
  user_all_member?: boolean | string
  user_city?: string
  user_country?: string
  user_id?: string
  user_is_logged?: boolean | string
  user_loyalty_card_type?: string
}

// ---
