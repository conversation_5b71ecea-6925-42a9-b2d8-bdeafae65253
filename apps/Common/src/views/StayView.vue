<script setup lang="ts">
import { EcommerceEventNameEnum, EventNameEnum } from "../global/enums"
import { computed, nextTick, onMounted, reactive, watch } from "vue"
import {
  useBasket,
  useCommerceTracking,
  useEcommerceEvent,
  useGA4Event,
  useHTTPErrors,
  useIdentificationPlainText,
  useLoader,
  usePageViewEvent,
  useSearch,
  useSearchQueryParams
} from "../composables"
import { useRoute, useRouter } from "vue-router"
import { AvailabilityStatus } from "../services/offer/offer.enums"
import { DividerDirection } from "@sb-components/atoms/UiDivider/enums"
import { OfferCategoryCode } from "../services/hotels/hotels.enums"
import PageTitle from "../components/PageTitle/PageTitle.vue"
import RoomSection from "../components/StayView/RoomSection/RoomSection.vue"
import { SpecialRates } from "@sb-components/organisms/UiSpecialRates/enums"
import UiBanner from "@sb-components/molecules/UiBanner/UiBanner.vue"
import UiDivider from "@sb-components/atoms/UiDivider/UiDivider.vue"
import UiMessage from "@sb-components/molecules/UiMessage/UiMessage.vue"
import { UiMessageVariation } from "@sb-components/molecules/UiMessage/enums"
import UiRoomSectionSkeleton from "../components/Skeletons/RoomSectionSkeleton/RoomSectionSkeleton.vue"
import { replaceUriParams } from "../helpers/uriHelper"
import { setOidcCookies } from "../helpers/oidc"
import { useI18n } from "vue-i18n"
import { useOffersAndAccommodations } from "../composables/useOffersAndAccommodations"
import { useUserPOS } from "../composables/useUserPOS"
import { useUserStore } from "../stores/user/user"

const router = useRouter()
const {
  availabilityStatus,
  offers,
  fetchOffers,
  sections,
  hasAccessibility,
  higherDeductionPercentageInAllOffers,
  isLoadingOffers
} = useOffersAndAccommodations()
const { specialRate, rooms, rateIds, hotel, lengthOfStay } = useSearch()
const { queryParams, enhancePermalink } = useSearchQueryParams()
const { userPOS } = useUserPOS()
const { pushEcommerceEvent } = useEcommerceEvent()
const { pushPageViewEvent } = usePageViewEvent()
const { commerceTracking } = useCommerceTracking()
const { pushGA4Event } = useGA4Event()
const { identification } = useIdentificationPlainText()
const { isLogged } = useUserStore()
const { errors: httpErrors } = useHTTPErrors()

const route = useRoute()
const { locale } = useI18n()

const homepageUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_HOMEPAGE, "", locale.value)
})

const showAccessibilityWarning = computed(() => {
  return (
    hasAccessibility.value && availabilityStatus.value === AvailabilityStatus.AVAILABLE && offers.value.length === 0
  )
})

const showNoResultsBanner = computed(() => {
  return availabilityStatus.value === AvailabilityStatus.UNAVAILABLE
})

function handlePageViewEvent() {
  pushPageViewEvent(
    "step2",
    "select a room",
    !showNoResultsBanner.value
      ? {
          merchant_id: commerceTracking.value?.outputBestVisit?.merchantId ?? "",
          source_id: commerceTracking.value?.outputBestVisit?.sourceId ?? ""
        }
      : {
          error_type: "no room available",
          merchant_id: commerceTracking.value?.outputBestVisit?.merchantId ?? "",
          source_id: commerceTracking.value?.outputBestVisit?.sourceId ?? ""
        }
  )
}

function handleOtherGtmEvents() {
  const roomTypes = sections.value.map((section) => section.title).join(", ")

  const eventData = !showNoResultsBanner.value
    ? {
        error_type: "",
        results_nb: sections.value.length.toString(),
        room_number_available: offers.value.length.toString(),
        room_type_available: roomTypes,
        step_name: "step2"
      }
    : {
        error_type: "no room available",
        results_nb: "0",
        room_type_available: roomTypes,
        step_name: "step2::error"
      }

  pushGA4Event("step2", "select a room", {
    eventName: EventNameEnum.funnel_start,
    event_data: eventData
  })

  pushEcommerceEvent(
    {
      items: [
        {
          item_brand: import.meta.env.VITE_APP_SITE_CODE,
          item_category: "room",
          item_id: hotel.value?.id.toLowerCase(),
          item_name: hotel.value?.name.toLowerCase(),
          night_nb: lengthOfStay.value.toString()
        }
      ]
    },
    EcommerceEventNameEnum.begin_checkout
  )
}

const hasSpecialRate = computed(() => specialRate.value !== SpecialRates.NONE)

const missingSpecialOffers = computed(() => {
  if (!hasSpecialRate.value) return false
  const hasPreferredOffers = offers.value.some((offer) =>
    offer.categories.some((category) => category.code === OfferCategoryCode.PREFERRED)
  )
  return !hasPreferredOffers
})

const currentRoom = computed({
  get() {
    const currentIndex = rooms.value.findIndex((room) => !room.productCode || !room.rateCode)
    return rooms.value[currentIndex]
  },
  set(newValue) {
    const currentIndex = rooms.value.findIndex((room) => !room.productCode || !room.rateCode)
    rooms.value[currentIndex] = newValue
  }
})

const error = computed(() => {
  return httpErrors.basket || httpErrors.basketSummary || httpErrors.identification || httpErrors.offers
})

const expandedState = reactive<{ [key: string]: boolean }>({})

const reactiveSections = computed(() =>
  (sections.value || []).map((section) => ({
    ...section,
    expanded: expandedState[section.title] || false
  }))
)

const handleBecomeMember = () => {
  setOidcCookies(route)

  window.location.href = replaceUriParams(import.meta.env.VITE_OIDC_SIGN_UP_URI, window.location.origin, locale.value)
}

watch(isLoadingOffers, async (newValue) => {
  if (!newValue) {
    handlePageViewEvent()

    await nextTick()

    handleOtherGtmEvents()
  }
})

onMounted(async () => {
  await fetchOffers()
})

watch(userPOS, async () => {
  await fetchOffers()
})

const { loading } = useLoader("rate")
const { upsertBasket } = useBasket({ forceRefetch: true })
const handleOfferChosen = ({
  classCode,
  rateCode,
  rateId,
  productCode
}: {
  classCode?: string
  rateId: string
  rateCode: string
  productCode: string
}) => {
  loading.value = true
  const isLastRoom = rooms.value.indexOf(currentRoom.value) === rooms.value.length - 1
  currentRoom.value = { ...currentRoom.value, classCode, productCode, rateCode, rateId }

  nextTick(async () => {
    try {
      if (rooms.value.length === 1 && enhancePermalink.value) {
        window.location.href = enhancePermalink.value.href

        return
      }

      const result = await upsertBasket(rateIds.value)

      if (!result) return

      if (!isLastRoom) {
        router.push({
          query: {
            ...queryParams.value,
            basketId: result.id
          }
        })
      } else if ((import.meta.env.DEV && router.currentRoute.value.query.skipRedirection) || !enhancePermalink.value) {
        window.location.href = router.resolve({
          force: true,
          name: !isLastRoom ? "stay" : "complete",
          query: {
            ...queryParams.value,
            basketId: result.id
          }
        }).href
      } else {
        window.location.href = enhancePermalink.value.href
      }
    } finally {
      loading.value = false
    }
  })
}
</script>

<template>
  <div class="Stay-view">
    <PageTitle
      :left="$t('page.stay_view.title.left')"
      :middle="$t('page.stay_view.title.middle')"
      :right="$t('page.stay_view.title.right')"
    />

    <div class="Stay-view__content">
      <UiDivider
        v-if="availabilityStatus === AvailabilityStatus.AVAILABLE"
        :direction="DividerDirection.HORIZONTAL"
        class="Stay-view__divider"
      />

      <UiBanner
        v-if="!identification.lcahmember && !isLogged && higherDeductionPercentageInAllOffers > 0"
        class="Stay-view__banner"
        :percentage="higherDeductionPercentageInAllOffers"
        @become-member:banner="handleBecomeMember"
      />

      <UiMessage
        v-if="error"
        class="SearchView__error"
        :title="$t('global.errors.title')"
        :description="$t('global.errors.description')"
        :variation="UiMessageVariation.DANGER"
        :links="[
          {
            text: $t('global.errors.cta'),
            href: homepageUri
          }
        ]"
      />

      <div
        v-if="!isLoadingOffers && (missingSpecialOffers || showAccessibilityWarning || showNoResultsBanner)"
        class="Stay-view__message"
      >
        <UiMessage
          v-if="missingSpecialOffers && !showNoResultsBanner"
          :description="$t('errors.stay.no_special_offers')"
          :variation="UiMessageVariation.WARNING"
        />

        <UiMessage
          v-if="showAccessibilityWarning && !showNoResultsBanner"
          :description="$t('errors.stay.no_accessibility_room')"
          :variation="UiMessageVariation.WARNING"
        />

        <UiMessage
          v-if="showNoResultsBanner"
          :description="$t('components.stay_view.no_results')"
          :variation="UiMessageVariation.DANGER"
        />
      </div>

      <div class="Stay-view__offer-container" aria-live="polite" :aria-busy="isLoadingOffers">
        <template v-if="!isLoadingOffers">
          <ul v-for="(section, index) in reactiveSections" :key="section.title">
            <li>
              <RoomSection
                v-model:expanded="expandedState[section.title]"
                :title="section.title"
                :pairs="section.pairs"
                :has-divider="index !== reactiveSections.length - 1"
                @choose-offer="handleOfferChosen"
              />
            </li>
          </ul>
        </template>
        <template v-else>
          <UiRoomSectionSkeleton />
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use "sass:map";
@use "@sb-config/spaces";
@use "@sb-utilities/spaces" as spacesUtils;
@use "@sb-utilities/mq";

.Stay-view {
  &__divider {
    display: none;

    @include mq.media(">=small") {
      display: unset;
      padding-bottom: map.get(spaces.$sizes, "10");
    }
  }

  &__banner {
    margin-top: map.get(spaces.$sizes, "10");
    margin-bottom: map.get(spaces.$sizes, "7");

    @include mq.media(">=small") {
      margin-bottom: map.get(spaces.$sizes, "8");
    }

    @include mq.media(">=medium") {
      margin-bottom: map.get(spaces.$sizes, "10");
    }
  }

  &__offer-container {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "8");

    @include mq.media(">=medium") {
      gap: map.get(spaces.$sizes, "10");
    }
  }

  &__offer-item {
    display: flex;
    flex-direction: column;
    gap: map.get(spaces.$sizes, "8");

    @include mq.media(">=medium") {
      gap: map.get(spaces.$sizes, "10");
    }
  }
}

.Stay-view__message {
  display: flex;
  flex-direction: column;
  gap: map.get(spaces.$sizes, "6");
  margin-bottom: map.get(spaces.$sizes, "10");
}
</style>
