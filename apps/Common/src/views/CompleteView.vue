<script setup lang="ts">
import { EventNameEnum } from "../global/enums"
import FormBuilder from "../components/FormBuilder/FormBuilder.vue"
import LegalText from "../components/LegalText/LegalText.vue"
import PageTitle from "../components/PageTitle/PageTitle.vue"
import { onMounted } from "vue"
import { useBasket } from "../composables/useBasket"
import { useGA4Event } from "../composables"
import { useUserPOS } from "../composables/useUserPOS"

const { basket } = useBasket()
const { userPOS } = useUserPOS()
const { pushGA4Event } = useGA4Event()

onMounted(() => {
  pushGA4Event("step4", "complete details", {
    eventName: EventNameEnum.funnel_step,
    event_data: {
      // TODO: Need to handle when error occur for the second event https://docs.google.com/spreadsheets/d/1WN3lRicWYF8fahuO2Kssr6zn2iqRUkkT/edit?gid=577679097#gid=577679097&range=N31:O31
      error_type: "",
      step_name: "step4"
    }
  })
})
</script>

<template>
  <PageTitle
    :left="$t('page.complete_view.title.left')"
    :middle="$t('page.complete_view.title.middle')"
    :right="$t('page.complete_view.title.right')"
  />

  <FormBuilder
    v-if="basket?.id"
    :basket-id="basket.id"
    :country-market="userPOS.countryMarket"
    :currency="userPOS.currency"
  />

  <LegalText />
</template>
