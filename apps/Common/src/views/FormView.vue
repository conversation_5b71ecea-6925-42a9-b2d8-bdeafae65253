<script setup lang="ts">
import FormBuilder from "../components/FormBuilder/FormBuilder.vue"
import { ref } from "vue"
import { useRouteQuery } from "@vueuse/router"

const basketId = useRouteQuery<string>("basketId", "5057cb6b-dc23-4eb9-aa6e-278759978f4f", {
  mode: "replace"
})
const countryMarket = ref("CA")
const currency = ref("EUR")
</script>

<template>
  <FormBuilder v-if="basketId" :basket-id="basketId" :country-market="countryMarket" :currency="currency" />
</template>
