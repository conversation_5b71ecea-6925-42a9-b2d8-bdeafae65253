<script setup lang="ts">
import { computed, onMounted } from "vue"
import {
  useCommerceTracking,
  useCurrentWindowSize,
  useHTTPErrors,
  useLoader,
  usePageViewEvent,
  useResetProcess,
  useSearch,
  useSearchConstraints,
  useSearchEngineSectionValidation
} from "../composables"
import { BlocInteractionEnum } from "../global/enums"
import PageTitle from "../components/PageTitle/PageTitle.vue"
import SearchEngineMobile from "../components/SearchEngine/SearchEngineMobile/SearchEngineMobile.vue"
import SearchEngineSection from "../components/SearchEngine/SearchEngineSection/SearchEngineSection.vue"
import UiMessage from "@sb-components/molecules/UiMessage/UiMessage.vue"
import { UiMessageVariation } from "@sb-components/molecules/UiMessage/enums"
import { replaceUriParams } from "../helpers/uriHelper"
import { storeToRefs } from "pinia"
import { useHotelsStore } from "@stores/hotels/hotels"
import { useI18n } from "vue-i18n"

const { handleSearchEventLoading } = useSearchEngineSectionValidation()
const { isDesktop } = useCurrentWindowSize()

const { dates, hotel, iataCode, rooms, specialRate, promoCode } = useSearch()
const { loading } = useLoader("search")
const useHotelStore = useHotelsStore()
const { isLoading } = storeToRefs(useHotelStore)
const { errors: httpErrors } = useHTTPErrors()

const error = computed(() => {
  return (
    httpErrors.referentials ||
    httpErrors.hotels ||
    httpErrors.constraints ||
    httpErrors["pos-currencies"] ||
    httpErrors["pos-countries"] ||
    httpErrors.identification
  )
})

const hotelId = computed(() => hotel.value?.id)
const { calendarConstraints, occupancyConstraints } = useSearchConstraints(hotelId)
const { pushPageViewEvent } = usePageViewEvent()
const { commerceTracking } = useCommerceTracking()
const { resetProcess } = useResetProcess()

onMounted(async () => {
  pushPageViewEvent("step1", "check availability", {
    merchant_id: commerceTracking.value?.outputBestVisit?.merchantId ?? "",
    source_id: commerceTracking.value?.outputBestVisit?.sourceId ?? ""
  })

  resetProcess()
})

const handleSearchMobile = async () => {
  await handleSearchEventLoading(BlocInteractionEnum.BOTTOM)
}

const { locale } = useI18n()
const homepageUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_HOMEPAGE, "", locale.value)
})
</script>

<template>
  <PageTitle
    :left="$t('page.search_view.title.left')"
    :middle="$t('page.search_view.title.middle')"
    :right="$t('page.search_view.title.right')"
  />

  <UiMessage
    v-if="error"
    class="SearchView__error"
    :title="$t('global.errors.title')"
    :description="$t('global.errors.description')"
    :variation="UiMessageVariation.DANGER"
    :links="[
      {
        text: $t('global.errors.cta'),
        href: homepageUri
      }
    ]"
  />

  <SearchEngineMobile
    v-if="!isDesktop"
    v-model:dates="dates"
    v-model:hotel="hotel"
    v-model:rooms="rooms"
    v-model:iata-code="iataCode"
    v-model:promo-code="promoCode"
    v-model:special-rate="specialRate"
    :is-loading="isLoading"
    :max-date="calendarConstraints?.maxDate"
    :max-range="calendarConstraints?.maxLengthOfStay"
    :min-range="calendarConstraints?.minLengthOfStay"
    :max-room="occupancyConstraints?.maxRoom"
    :max-pax="occupancyConstraints?.maxPax"
    :max-adult="occupancyConstraints?.maxAdult"
    :max-child="occupancyConstraints?.maxChild"
    :max-child-age="occupancyConstraints?.maxChildAge"
    :loading="loading"
    @search-engine-mobile::check="handleSearchMobile"
  />

  <SearchEngineSection
    v-else
    :is-loading="isLoading"
    :max-date="calendarConstraints?.maxDate"
    :max-range="calendarConstraints?.maxLengthOfStay"
    :min-range="calendarConstraints?.minLengthOfStay"
    :max-room="occupancyConstraints?.maxRoom"
    :max-pax="occupancyConstraints?.maxPax"
    :max-adult="occupancyConstraints?.maxAdult"
    :max-child="occupancyConstraints?.maxChild"
    :max-child-age="occupancyConstraints?.maxChildAge"
    :loading="loading"
  />
</template>

<style lang="scss" scoped>
@use "sass:map";
@use "@sb-config/spaces";

.SearchView__error {
  margin-top: map.get(spaces.$sizes, "11");
  margin-bottom: map.get(spaces.$sizes, "11");
}
</style>
