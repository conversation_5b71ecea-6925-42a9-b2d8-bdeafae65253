import { LocaleMessages, VueMessageType, createI18n } from "vue-i18n"
import {
  storybookDeMessages,
  storybookEnMessages,
  storybookEsMessages,
  storybookFrMessages,
  storybookJaMessages,
  storybookZhMessages
} from "../../../../storybook/src/i18n"
import commonDeMessages from "./locales/de.json"
import commonEnMessages from "./locales/en.json"
import commonEsMessages from "./locales/es.json"
import commonFrMessages from "./locales/fr.json"
import commonJaMessages from "./locales/ja.json"
import commonZhMessages from "./locales/zh.json"
import deepmerge from "deepmerge"

let defaultMessages = {
  de: deepmerge.all([storybookDeMessages, commonDeMessages]) as LocaleMessages<VueMessageType>,
  en: deepmerge.all([storybookEnMessages, commonEnMessages]) as LocaleMessages<VueMessageType>,
  es: deepmerge.all([storybookEsMessages, commonEsMessages]) as LocaleMessages<VueMessageType>,
  fr: deepmerge.all([storybookFrMessages, commonFrMessages]) as LocaleMessages<VueMessageType>,
  ja: deepmerge.all([storybookJaMessages, commonJaMessages]) as LocaleMessages<VueMessageType>,
  zh: deepmerge.all([storybookZhMessages, commonZhMessages]) as LocaleMessages<VueMessageType>
}

const brandName = import.meta.env.VITE_APP_BRAND_NAME

if (brandName) {
  function getLocalesPath(locale: string) {
    return `../../../${brandName}/src/i18n/locales/${locale}.json`
  }

  const allLocalesMessages = import.meta.glob<LocaleMessages<VueMessageType>>("../../../*/src/i18n/locales/*.json", {
    eager: true
  })

  defaultMessages = {
    de: deepmerge.all([defaultMessages.de, allLocalesMessages[getLocalesPath("de")]]) as LocaleMessages<VueMessageType>,
    en: deepmerge.all([defaultMessages.en, allLocalesMessages[getLocalesPath("en")]]) as LocaleMessages<VueMessageType>,
    es: deepmerge.all([defaultMessages.es, allLocalesMessages[getLocalesPath("es")]]) as LocaleMessages<VueMessageType>,
    fr: deepmerge.all([defaultMessages.fr, allLocalesMessages[getLocalesPath("fr")]]) as LocaleMessages<VueMessageType>,
    ja: deepmerge.all([defaultMessages.ja, allLocalesMessages[getLocalesPath("ja")]]) as LocaleMessages<VueMessageType>,
    zh: deepmerge.all([defaultMessages.zh, allLocalesMessages[getLocalesPath("zh")]]) as LocaleMessages<VueMessageType>
  }
}

export const i18n = createI18n({
  fallbackLocale: "en",
  globalInjection: true,
  legacy: false,
  locale: "en",
  messages: defaultMessages,
  pluralRules: {}
})

export type AvailableLocales = (typeof i18n.global.availableLocales)[number]
