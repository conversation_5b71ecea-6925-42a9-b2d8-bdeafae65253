import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios"
import { AccorApiEndpoints } from "../../global/consts"
import { OidcEnpoint } from "../../global/enums"
import { i18n } from "../../i18n"
import qs from "qs"
import { useApiSemaphore } from "../../composables/useApiSemaphore"
import { useIdentificationPlainText } from "../../composables/useIdentificationPlainText"

export interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
}

export function mapApiResponse<T, K>(response: ApiResponse<T>, extractor: (data: T) => K): K {
  return extractor(response.data)
}

const { waitUntilIdentification, waitUntilUserDetails } = useApiSemaphore()

class ApiManager {
  private readonly apiClient: AxiosInstance

  constructor(baseURL: string, headers: Record<string, string> = {}) {
    this.apiClient = axios.create({
      baseURL,
      headers,
      paramsSerializer: (params) => qs.stringify(params, { arrayFormat: "repeat" }),
      withCredentials: true
    })

    this.apiClient.interceptors.request.use(async function (config) {
      const { identification } = useIdentificationPlainText()
      const identificationId = identification.identificationId

      const whitelistURLs = [
        `${AccorApiEndpoints.contactMe}`,
        identificationId
          ? `${AccorApiEndpoints.identification}/${identificationId}`
          : `${AccorApiEndpoints.identification}/plaintext`
      ]

      if (config.url && whitelistURLs.includes(config.url)) return config

      await waitUntilUserDetails()
      await waitUntilIdentification()

      return config
    })
  }

  async get<T>(url: string | URL, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const { identification } = useIdentificationPlainText()

    const headers = {
      "Accept-Language": i18n.global.locale.value,
      ...config?.headers,
      "identification-token": identification.identificationToken || undefined
    }

    return await this.apiClient.get<T>(url.toString(), { ...config, headers })
  }

  async post<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const { identification } = useIdentificationPlainText()

    const headers = {
      "Accept-Language": i18n.global.locale.value,
      ...config?.headers,
      "identification-token": identification.identificationToken || undefined
    }

    return await this.apiClient.post<T>(url, data, { ...config, headers })
  }

  async put<T>(url: string, data: unknown, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const { identification } = useIdentificationPlainText()

    const headers = {
      "Accept-Language": i18n.global.locale.value,
      ...config?.headers,
      "identification-token": identification.identificationToken || undefined
    }

    return await this.apiClient.put<T>(url, data, { ...config, headers })
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const { identification } = useIdentificationPlainText()

    const headers = {
      "Accept-Language": i18n.global.locale.value,
      ...config?.headers,
      "identification-token": identification.identificationToken || undefined
    }

    return await this.apiClient.delete<T>(url, { ...config, headers })
  }

  async getWithAccessToken<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.apiClient.get<T>(url, config)
      return response.data
    } catch (error) {
      const axiosError = error as AxiosError

      if (axiosError.response?.status === 401) {
        // Try to refresh expired access token
        await axios.get(OidcEnpoint.refresh)

        const retriedResponse = await this.apiClient.get<T>(url, config)

        return retriedResponse.data
      }

      throw axiosError
    }
  }
}

export default ApiManager
