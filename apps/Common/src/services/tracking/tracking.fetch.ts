import { AccorApiEndpoints, brandConfiguration } from "../../global/consts"
import { ApiCommerceTracking } from "./tracking.types"
import { useApiManager } from "../../composables/useApiManager"

export async function getCommerceTracking() {
  const { apiManager } = useApiManager()

  const body = {
    siteCode: brandConfiguration.siteCode
  }

  const { data: response } = await apiManager.value.post<ApiCommerceTracking>(AccorApiEndpoints.commerceTracking, {
    body
  })

  return response
}
