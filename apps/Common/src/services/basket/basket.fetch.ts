import { AccorApiEndpoints } from "../../global/consts"
import { PostBasketApiResponse } from "./basket.types"
import { useApiManager } from "../../composables/useApiManager"

export async function createBasket(
  offersId: string[],
  currency: string,
  countryMarket: string
): Promise<PostBasketApiResponse> {
  const { apiManager } = useApiManager()

  const params = {
    countryMarket: countryMarket,
    currency: currency
  }

  //TODO validate fixed params
  const body = {
    context: {
      bookingFunnelName: "myAppName",
      bookingFunnelVersion: "myAppVersion",
      leverCode: "myLeverName",
      leverLevel: "myLeverLevel",
      merchantId: "MS-MULTI-FAIRMONT",
      referer: "myReferer",
      sessionLeverCode: "mySessionLeverLevel",
      sessionLeverName: "mySessionLeverName",
      sessionMerchantId: "mySessionMerchantId",
      sessionSourceId: "mySessionSourceId",
      sourceId: "Direct_Access"
    },
    ipAddress: "*******",
    items: offersId.map((offerId) => {
      return {
        offerId: offerId
      }
    }),
    userAgent: "myUserAgent"
  }

  const { data: response } = await apiManager.value.post<PostBasketApiResponse>(AccorApiEndpoints.createBasket, body, {
    params
  })

  return response
}

export async function updateBasketItems(basketId: string, offerIds: string[], currency: string, countryMarket: string) {
  const { apiManager } = useApiManager()

  const params = {
    countryMarket: countryMarket,
    currency: currency
  }

  //TODO validate fixed params
  const body = {
    context: {
      bookingFunnelName: "myAppName",
      bookingFunnelVersion: "myAppVersion",
      leverCode: "myLeverName",
      leverLevel: "myLeverLevel",
      merchantId: "MS-MULTI-FAIRMONT",
      referer: "myReferer",
      sessionLeverCode: "mySessionLeverLevel",
      sessionLeverName: "mySessionLeverName",
      sessionMerchantId: "mySessionMerchantId",
      sessionSourceId: "mySessionSourceId",
      sourceId: "Direct_Access"
    },
    ipAddress: "*******",
    items: offerIds.map((offerId) => ({ offerId })),
    userAgent: "myUserAgent"
  }

  const { data: response } = await apiManager.value.put<PostBasketApiResponse>(
    AccorApiEndpoints.updateBasket(basketId),
    body,
    {
      params
    }
  )

  return response
}

export async function getBasket(
  hotelId: string,
  currency: string,
  countryMarket: string
): Promise<PostBasketApiResponse> {
  const { apiManager } = useApiManager()

  const params = {
    countryMarket: countryMarket,
    currency: currency
  }

  const getBasketEndpoint = AccorApiEndpoints.getBasket(hotelId)

  const { data: response } = await apiManager.value.get<PostBasketApiResponse>(getBasketEndpoint, { params })

  return response
}
