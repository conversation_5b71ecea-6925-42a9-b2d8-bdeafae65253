export enum HotelStatus {
  OPEN = "OPEN",
  CLOSED = "CLOSED"
}

export enum AvailabilityStatus {
  AVAILABLE = "AVAILABLE",
  UNAVAILABLE = "UNAVAILABLE"
}

export enum LengthOfStayUnit {
  DAY = "DAY",
  NIGHT = "NIGHT"
}

export enum AggregationPricingType {
  AVERAGE_PER_NIGHT = "AVERAGE_PER_NIGHT",
  TOTAL_STAY = "TOTAL_STAY"
}

export enum Deduction {
  MEMBER_RATE = "MEMBER_RATE",
  PREFERRED = "PREFERRED",
  CORPORATE = "CORPORATE"
}

export enum PricingCategory {
  AGENCY = "AGENCY",
  CORPORATE_NEGOTIATED_RATE = "CORPORATE_NEGOTIATED_RATE",
  FAMILY = "FAMILY",
  LOYALTY_ALL_PLUS_IBIS = "LOYALTY_ALL_PLUS_IBIS",
  LOYALTY_RESIDENTIAL_OWNERS = "LOYALTY_RESIDENTIAL_OWNERS",
  MEMBER_RATE = "MEMBER_RATE",
  PREFERRED = "PREFERRED",
  PREMIUM_AVAILABILITY = "PREMIUM_AVAILABILITY",
  STANDARD = "STANDARD"
}

export enum TaxType {
  ALL_TAXES_AND_FEES_INCLUDED = "ALL_TAXES_AND_FEES_INCLUDED",
  TAXES_EXCLUDED = "TAXES_EXCLUDED"
}

export enum MealPlan {
  EUROPEAN_PLAN = "EUROPEAN_PLAN",
  FULL_BOARD = "FULL_BOARD"
}

export enum OfferCategoryCode {
  MEMBER_RATE = "MEMBER_RATE",
  LOYALTY_RESIDENTIAL_OWNERS = "LOYALTY_RESIDENTIAL_OWNERS",
  LOYALTY_ALL_PRIVILEGED = "LOYALTY_ALL_PRIVILEGED",
  PREFERRED = "PREFERRED"
}

export enum OfferType {
  ROOM = "ROOM",
  PACKAGE = "PACKAGE"
}

export enum CancellationPolicy {
  FREE_CANCELLATION = "FREE_CANCELLATION"
}

export enum PolicyGuarantee {
  NO_PREPAY = "NO_PREPAY"
}

export enum Lodging {
  APT = "APT",
  HTL = "HTL",
  RST = "RST"
}

export enum SpokenLanguages {
  AR = "AR",
  DA = "DA",
  DE = "DE",
  EN = "EN",
  ES = "ES",
  FR = "FR",
  ID = "ID",
  IT = "IT",
  JA = "JA",
  KO = "KO",
  NL = "NL",
  PL = "PL",
  PT = "PT",
  RU = "RU",
  SV = "SV",
  TH = "TH",
  VI = "VI",
  ZH = "ZH"
}

export enum HotelScale {
  FI = "FI",
  LX = "LX",
  TO = "TO",
  UP = "UP"
}

export enum HotelType {
  AC = "AC",
  FR = "FR",
  MG = "MG",
  MP = "MP"
}

export enum HotelLabel {
  FASTCOM = "FASTCOM",
  HAOKE = "HAOKE",
  ASFST = "ASFST",
  ALL_SAFE = "ALL_SAFE",
  SUITE_NIGHT_UPGRADE = "SUITE_NIGHT_UPGRADE",
  EXTENDED_STAY = "EXTENDED_STAY",
  CHEQUE_VACANCES_ACCEPTED = "CHEQUE_VACANCES_ACCEPTED",
  DINING_SPA_WITHOUT_STAY = "DINING_SPA_WITHOUT_STAY",
  DINING_SPA_WITH_STAY = "DINING_SPA_WITH_STAY",
  HIDDEN_MEMBER_RATES = "HIDDEN_MEMBER_RATES",
  BEST_PRICE_GUARANTEE = "BEST_PRICE_GUARANTEE",
  BUREAU_VERITAS_CERTIFIED = "BUREAU_VERITAS_CERTIFIED",
  SPA = "SPA",
  THALASSO = "THALASSO",
  TRIPADVISOR_CERTIFIED = "TRIPADVISOR_CERTIFIED",
  ISO_9001 = "ISO_9001",
  RESORT = "RESORT",
  MEMORABLE_MOMENT = "MEMORABLE_MOMENT",
  FLY_AND_STAY = "FLY_AND_STAY",
  DINING_OFFER = "DINING_OFFER",
  PROGRAM_OPTOUT = "PROGRAM_OPTOUT",
  PROGRAM_OPTIN_CFS = "PROGRAM_OPTIN_CFS",
  PROGRAM_OPTIN_NFS2 = "PROGRAM_OPTIN_NFS2",
  PROGRAM_OPTIN_NSD = "PROGRAM_OPTIN_NSD",
  PROGRAM_PREFERENCES = "PROGRAM_PREFERENCES",
  BOOKING_LEISURE_ON_LINE = "BOOKING_LEISURE_ON_LINE",
  WEDDING = "WEDDING",
  METROPOLITAN = "METROPOLITAN",
  FAMILY_FRIENDLY = "FAMILY_FRIENDLY",
  COMPLIMENTARY_BREAKFAST = "COMPLIMENTARY_BREAKFAST",
  HUAZHU = "HUAZHU",
  IDHAB = "IDHAB",
  CORPORATE_BENEFITS = "CORPORATE_BENEFITS",
  AVAILABLE_FOR_CONTRACTING = "AVAILABLE_FOR_CONTRACTING"
}

export enum LoyaltyProgramStatus {
  NON_PARTICIPATING = "NON_PARTICIPATING",
  PARTICIPATING_HOTEL = "PARTICIPATING_HOTEL"
}

export enum Amenity {
  AIR_CONDITIONING = "air_conditioning",
  BABYSITTING = "baby_sitting",
  BAR = "bar",
  BUSINESS_CENTER = "business_center",
  CHILD_FACILITIES = "child_facilities",
  COFFEE_MACHINE = "coffee_machine",
  CONVENTION_CENTER = "convention_center",
  FITNESS = "fitness",
  GOLF = "golf",
  HAMMAM = "hammam",
  IRON = "iron",
  JACUZZI = "jacuzzi",
  LAUNDRY = "laundry",
  MEETING_ROOMS = "meeting_rooms",
  NON_SMOKING = "non_smoking",
  POOL = "pool",
  PRIVATIVE_BATHROOM = "privative_bathroom",
  RESTAURANT = "restaurant",
  ROOM_SERVICE = "room_service",
  SAUNA = "sauna",
  TENNIS = "tennis",
  THALASSO = "thalasso",
  WHEELCHAIR_ACCESS = "wheelchair_access",
  WIFI = "wifi"
}

export enum ApiMediaCategory {
  ACCESS = "ACCESS",
  ANECDOTE = "ANECDOTE",
  BANNER = "BANNER",
  BAR = "BAR",
  BEDROOM = "BEDROOM",
  BREAKFAST = "BREAKFAST",
  BUSINESS_CENTER = "BUSINESS_CENTER",
  CHEF = "CHEF",
  CUSTOMER_MEDIA = "CUSTOMER_MEDIA",
  DESTINATION = "DESTINATION",
  EXECUTIVE_FLOOR = "EXECUTIVE_FLOOR",
  FAMILY = "FAMILY",
  FITNESS = "FITNESS",
  GOLF = "GOLF",
  HOTEL = "HOTEL",
  HOTEL_ADVANTAGE = "HOTEL_ADVANTAGE",
  INSTITUTE = "INSTITUTE",
  LEGAL = "LEGAL",
  MEETING_ROOM = "MEETING_ROOM",
  OPTION = "OPTION",
  PACKAGE = "PACKAGE",
  POOL = "POOL",
  RESTAURANT = "RESTAURANT",
  SERVICE = "SERVICE",
  SPA = "SPA",
  SUITE = "SUITE",
  SUSTAINABLE_DEVELOPMENT = "SUSTAINABLE_DEVELOPMENT",
  THALASSO = "THALASSO",
  WEDDING = "WEDDING",
  WELCOME = "WELCOME"
}

export enum ApiMediaType {
  IMAGE = "IMAGE",
  TEXT = "TEXT",
  VIDEO = "VIDEO"
}

export enum ApiPaymentMeanProgram {
  MOBILE = "MOBILE"
}

export enum ApiPaymentMeanItemCode {
  VISA = "VI"
}

export enum ApiPaymentMeanItemFamily {
  CREDIT_CARD = "CC"
}

export enum ApiPaymentMeanItemType {
  GUARANTEE = "GUARANTEE",
  PREPAYMENT = "PREPAYMENT"
}
