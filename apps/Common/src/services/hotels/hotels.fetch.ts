import { AccorApiEndpoints, brandConfiguration } from "../../global/consts"
import { ApiHotel, ApiHotelMedia, HotelsApiResponse } from "./hotels.types"
import { Region } from "../../global/enums"
import { cachedApi } from "../cache"
import { useApiManager } from "../../composables/useApiManager"

export async function fetchHotelsByRegion(region: Region): Promise<HotelsApiResponse> {
  return cachedApi(
    async () => {
      const { apiManager } = useApiManager()

      const params = {
        brand: brandConfiguration.brandCode,
        enlarge: false,
        q: region
      }

      const { data: response } = await apiManager.value.get<HotelsApiResponse>(AccorApiEndpoints.hotels, {
        params
      })

      return response
    },
    AccorApiEndpoints.hotel(region),
    { keyParts: [brandConfiguration.brandCode, region] }
  )
}

export async function fetchHotel(hotelId: string): Promise<ApiHotel> {
  const { apiManager } = useApiManager()

  const { data: response } = await apiManager.value.get<ApiHotel>(`/catalog/v1/hotels/${hotelId}`)

  return response
}

export async function getMedias(hotelId: string): Promise<ApiHotelMedia[]> {
  const { apiManager } = useApiManager()

  const params = {
    category: "HOTEL",
    type: "IMAGE"
  }

  const mediasEndpoint = AccorApiEndpoints.medias(hotelId)

  const { data: response } = await apiManager.value.get<ApiHotelMedia[]>(mediasEndpoint, { params })

  return response
}
