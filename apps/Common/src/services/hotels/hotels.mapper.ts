import { ApiHotel } from "./hotels.types"
import { HotelsList } from "@stores/hotels/interface"
import { RegionPriority } from "../../global/enums"
import { UiDestinationListDropdownItem } from "@sb-components/molecules/UiDestinationListDropdown/types"
import { UiDestinationListItemType } from "@sb-components/molecules/UiDestinationList/types"
import { UiDestinationListRegionProps } from "@sb-components/molecules/UiDestinationListRegion/interface"
import { getStateName } from "../../services/referentials/referentials.mapper"
import { i18n } from "../../i18n"

export function mapApiHotelsToDestinationMobile(hotels: HotelsList): UiDestinationListDropdownItem[] {
  return Object.keys(hotels)
    .sort((a, b) => {
      return RegionPriority[a as keyof typeof RegionPriority] < RegionPriority[b as keyof typeof RegionPriority]
        ? -1
        : 1
    })
    .map((region, index) => {
      return {
        id: index.toString(),
        items: mapApiHotelsToDestinationListRegion(hotels[region as keyof typeof hotels]),
        name: i18n.global.t(`regions.${region}`)
      }
    })
}

export function mapApiHotelsToDestinationDesktop(hotels: HotelsList): UiDestinationListItemType[] {
  return Object.keys(hotels)
    .sort((a, b) => {
      return RegionPriority[a as keyof typeof RegionPriority] < RegionPriority[b as keyof typeof RegionPriority]
        ? -1
        : 1
    })
    .map((region, index) => {
      return {
        id: index.toString(),
        items: hotels[region as keyof typeof hotels]
          .map((hotel) => mapApiHotelToUiDestinationListItemProps(hotel))
          .sort((a, b) => {
            if (a.city === b.city) {
              return a.name < b.name ? -1 : 1
            }

            return a.city < b.city ? -1 : 1
          }),
        name: i18n.global.t(`regions.${region}`)
      }
    })
}

function mapApiHotelsToDestinationListRegion(hotels: ApiHotel[]) {
  return hotels
    .reduce((acc: UiDestinationListRegionProps[], hotel) => {
      const country = hotel.localization.address.country

      let index = acc.findIndex((element) => element.id === country)
      if (index === -1) {
        acc.push({
          id: country,
          items: [],
          name: country
        })

        index = acc.length - 1
      }

      acc[index].items?.push(mapApiHotelToUiDestinationListItemProps(hotel))

      return acc
    }, [])
    .sort((a, b) => {
      return a.id < b.id ? -1 : 1
    })
    .map((element) => {
      return {
        ...element,
        items: element.items?.sort((a, b) => (a.name < b.name ? -1 : 1))
      }
    })
}

function mapApiHotelToUiDestinationListItemProps(hotel: ApiHotel) {
  return {
    city: hotel.localization.address.city,
    country: hotel.localization.address.country,
    expediaCompliant: hotel.label?.includes("HIDDEN_MEMBER_RATES") || false,
    id: hotel.id,
    name: hotel.name,
    state: getStateName(hotel.localization.address.countryCode, hotel.localization.address.stateCode)
  }
}
