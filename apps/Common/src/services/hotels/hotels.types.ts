import {
  Amenity,
  ApiMediaCategory,
  ApiMediaType,
  ApiPaymentMeanItemCode,
  ApiPaymentMeanItemFamily,
  ApiPaymentMeanItemType,
  ApiPaymentMeanProgram,
  HotelScale,
  HotelStatus,
  HotelType,
  Lodging,
  LoyaltyProgramStatus,
  SpokenLanguages
} from "./hotels.enums"
import { CoordinateType, MediaItem } from "../../global/types"
import { Currency } from "../../global/enums"

export interface HotelsApiResponse {
  links: {
    href: string
    method: string
    rel: string
  }[]
  place: {
    enlargement: boolean
    searchBox: {
      bottomLeft: CoordinateType
      boxCenter: CoordinateType
      topRight: CoordinateType
    }
  }
  results: {
    distance: number
    hotel: ApiHotel
    score: number
  }[]
  statistics: StatisticsHotel
}

interface StatisticsHotel {
  amenity?: {
    [key: string]: number
  }
  brand?: {
    [key: string]: number
  }
  label?: {
    [key: string]: number
  }
  lodging?: {
    [key: string]: number
  }
  loyalty?: {
    advantageAllowed: number
    burnAllowed: number
    earnAllowed: number
    huazhuRewards: number
    memberRate: number
    status: {
      non_participating: number
      participating_hotel: number
      partner_hotel: number
      partner_hotel_huazhu: number
    }
  }
  rating?: {
    star: {
      [key: number]: number
    }
    tripadvisor: {
      [key: number]: number
    }
    total: number
  }
}

export interface ApiHotel {
  advantages?: string[]
  amenity?: {
    free: Amenity[]
    paying?: Amenity[]
  }
  brand: string
  chain: string
  checkin: {
    available: boolean
  }
  checkInHour: string
  checkInHourTo?: string
  checkOutHour: string
  codes: {
    id: string
    source: string
  }[]
  contact: {
    fax?: string
    faxPrefix?: string
    generalManager?: {
      firstName: string
      lastName: string
    }
    mail: string
    phone: string
    phonePrefix: string
  }
  croCode: string
  crs: string
  currencyCode: Currency
  customerRequestAccepted?: boolean
  description: string
  destinationDescription?: string
  enhancedDescription?: string
  factsheetUrl: string
  flashInfo?: string
  id: string
  lastRenovationDate: string // format YYYY-MM-DD
  label?: string[]
  localization: {
    address: {
      city: string
      country: string
      countryCode: string
      stateCode?: string
      street: string
      zipCode: string
    }
    environmentCode?: string
    gps: CoordinateType
  }
  lodging: Lodging
  loyaltyProgram: {
    advantageAllowed: boolean
    burnAllowed: boolean
    earnAllowed: boolean
    huazhuRewards: boolean
    memberRate: boolean
    status: LoyaltyProgramStatus
  }
  media: {
    count: number
    medias: MediaItem[]
  }
  name: string
  openingDate?: string
  paymentMeans: PaymentMean[]
  pms: {
    code: string
    label: string
  }
  rating: {
    star?: {
      score: number
    }
    tripadvisor?: {
      nbReviews: number
      score: number
    }
    trustyou?: {
      nbReviews: number
      score: number
    }
  }
  roomOccupancy: {
    maxAdult: number
    maxChild: number
    maxChildAge: number
    maxPax: number
    maxRoom: number
  }
  scale: HotelScale
  seo: {
    description?: string
    metaDescription?: string
    title?: string
  }
  seoDescription?: string
  spokenLanguages?: SpokenLanguages[]
  status: HotelStatus
  type: HotelType
}

export interface AccommodationWrapper {
  accommodations: []
  description: string
}

export interface Accommodation {
  amenities: Amenities[]
  b2b: {
    category: {
      code: string
      label: string
    }
  }
  classification: {
    group: string
    standard: string
    type: string
  }
  configuration: [
    {
      assets: string[]
      beddings: {
        details: [
          {
            quantity: number
            type: string
          }
        ]
        doubleBed: number
        extra: {
          adultBed: boolean
          childBed: boolean
          crib: boolean
        }
        singleBed: number
      }
      maxOccupancy: {
        adult: number
        children: number
        maxChildAge: number
        pax: number
      }
      situation: {
        accessibility: boolean
        availableFloors: [
          {
            code: string
            label: string
          }
        ]
        views: string[]
      }
    }
  ]
  description: string
  extendedStay: boolean
  features: string
  floorSpace: {
    unit: string
    value: number
  }
  id: string
  inventoryCount: number
  label: string
  medias: {
    photos: MediaItem[]
    total: number
  }
  roomClass: {
    code: string
  }
  shortLabel: string
  smokingRoom: boolean
  surface: {
    squareFeet: number
    squareMeter: number
  }
  title: string
  topAmenities: string[]
  upsells: string[]
}

export interface Amenities {
  amenities: [
    {
      code: string
      value: string
    }
  ]
  facilities: Facilities[]
  label: string
  name: string
  setup: [
    {
      code: string
      details: [
        {
          code: string
          label: string
        }
      ]
      label: string
    }
  ]
}

export interface Facilities {
  items: FacilitiesItems[]
  label: string
  name: string
}

export interface FacilitiesItems {
  code: string
  name: string
  paying: boolean
}

export interface PaymentMean {
  items: PaymentMeanItem[]
  program: ApiPaymentMeanProgram
}

export interface PaymentMeanItem {
  code: ApiPaymentMeanItemCode
  rank: number
  name: string
  family: ApiPaymentMeanItemFamily
  types: ApiPaymentMeanItemType[]
  installments: unknown[]
}

export interface ApiHotelMedia {
  category: ApiMediaCategory
  productCode: string
  type: ApiMediaType
  formats: ApiMediaFormat[]
}

export interface ApiMediaFormat {
  format: string
  generic: boolean
  path: string
}
