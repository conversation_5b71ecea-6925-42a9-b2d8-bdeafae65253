import { KeyFeaturesCode, KeyFeaturesOrder } from "../../services/utils/keyFeatures/keyFeatures.enums"
import { Accommodation } from "../hotels/hotels.types"
import { Offer } from "../offer/offer.types"
import { UiRoomDetailsModalContentProps } from "@sb-components/molecules/UiRoomDetailsModalContent/interface"
import { mapAmenities } from "../utils/amenities/amenities.mapper"
import { roomDetailsModalKeyFeaturesMapper } from "../utils/keyFeatures/keyFeatures.mapper"
import { roomDetailsModalMediaMapper } from "../utils/media/media.mapper"
import { truncateApiTitle } from "@shared/utils"

export function accommodationMapper(
  offer: Offer["product"],
  accommodation?: Accommodation
): UiRoomDetailsModalContentProps {
  const keyFeaturesMappedAndSorted = roomDetailsModalKeyFeaturesMapper(offer.bedding, offer.keyFeatures)
    .filter((feature) => KeyFeaturesOrder.includes(feature.code as KeyFeaturesCode))
    .sort(
      (a, b) =>
        KeyFeaturesOrder.indexOf(a.code as KeyFeaturesCode) - KeyFeaturesOrder.indexOf(b.code as KeyFeaturesCode)
    )

  return {
    amenities: mapAmenities(accommodation?.amenities),
    classCode: accommodation?.roomClass?.code as string,
    description: accommodation?.description,
    images: roomDetailsModalMediaMapper(offer.label, accommodation?.medias),
    keyFeatures: keyFeaturesMappedAndSorted,
    title: truncateApiTitle(offer.label)
  }
}
