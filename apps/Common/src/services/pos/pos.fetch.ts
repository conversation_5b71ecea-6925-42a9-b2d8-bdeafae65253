import { AccorApiEndpoints, brandConfiguration } from "../../global/consts"
import {
  Country,
  CountryInfos,
  Currency,
  PosCountriesGroupedByContinent,
  PosCountryLanguageAuto,
  PosCurrenciesGroupedByContinent
} from "./pos.type"
import { cachedApi } from "../cache"
import { useApiManager } from "../../composables/useApiManager"

export async function fetchPosCountriesGroupedByContinent(): Promise<PosCountriesGroupedByContinent[]> {
  return cachedApi(async () => {
    const { apiManager } = useApiManager()

    const { data: response } = await apiManager.value.get<PosCountriesGroupedByContinent[]>(
      AccorApiEndpoints.posCountriesGroupedByContinent(brandConfiguration.siteCode)
    )

    return response
  }, AccorApiEndpoints.posCountriesGroupedByContinent(brandConfiguration.siteCode))
}

export async function fetchPosCurrenciesGroupedByContinent(): Promise<PosCurrenciesGroupedByContinent[]> {
  return cachedApi(async () => {
    const { apiManager } = useApiManager()

    const { data: response } = await apiManager.value.get<PosCurrenciesGroupedByContinent[]>(
      AccorApiEndpoints.posCurrenciesGroupedByContinent
    )

    return response
  }, AccorApiEndpoints.posCurrenciesGroupedByContinent)
}

export async function fetchPosCountryLanguageAuto(
  isoCountryCode: string,
  languageCode: string
): Promise<PosCountryLanguageAuto> {
  const { apiManager } = useApiManager()

  const { data: response } = await apiManager.value.get<PosCountryLanguageAuto>(
    AccorApiEndpoints.posCountryLanguageAuto(brandConfiguration.siteCode, isoCountryCode, languageCode)
  )

  return response
}

export async function updateCurrencyCookies(currency: Currency): Promise<void> {
  const { apiManager } = useApiManager()

  const endPoint = AccorApiEndpoints.posCurrencyManual(currency.currencyCode)

  await apiManager.value.get<Currency>(endPoint)
}

export async function updateLanguageCookies(country: Country): Promise<void> {
  const { apiManager } = useApiManager()

  const endPoint = AccorApiEndpoints.posCountryLanguageManual(
    brandConfiguration.siteCode,
    country.accorCountryCode,
    country.languageCode
  )

  await apiManager.value.get<CountryInfos>(endPoint)
}
