import { Accommodation, ApiHotelMedia, ApiMediaFormat } from "../../hotels/hotels.types"
import { FormattedRoomDetailModalMedia, sortMediasByResolution } from "../../../helpers/medias"
import { MediaFormat, OfferMedia } from "../../../services/offer/offer.types"
import { GalleryType } from "@sb-components/organisms/UiRoomCard/interface"
import { MediaItem } from "../../../global/types"
import { UiImageMediaUnit } from "@sb-components/atoms/UiImage/types"
import { UiImageProps } from "@sb-components/atoms/UiImage/interface"
import { i18n } from "../../../i18n"

const { global } = i18n

export function roomCardmediaMapper(media: OfferMedia["medias"], label: string): UiImageProps[] {
  if (media.length === 0) {
    return getDefaultUiImageProps(label)
  }

  return media.map((mediaItem) => {
    const formats = sortMediasByResolution(mediaItem.formats) as MediaFormat[]
    const mainFormat = formats.find((format) => format.generic) || mediaItem.formats[0]

    const srcSet = {
      medias: formats.map((format) => ({
        [format.format]: format.url
      }))
    }
    return {
      alt: global.t("components.stay_view.room_card.media.alt", { label }),
      src: mainFormat.url,
      srcSet
    }
  })
}

export function roomDetailsModalMediaMapper(label: string, media?: Accommodation["medias"]): UiImageProps[] {
  if (!media || media.photos.length === 0) {
    return getDefaultUiImageProps(label)
  }

  return media.photos.map((photo) => {
    const formats = sortMediasByResolution(
      Object.keys(photo).map((format) => ({ format, path: photo[format as keyof MediaItem] as string }))
    ) as FormattedRoomDetailModalMedia[]

    const mainSrc = formats.find((resolution) => resolution.path)?.path || ""

    const srcSet = {
      medias: formats.map((format) => ({
        [format.format]: format.path
      }))
    }

    return {
      alt: global.t("components.stay_view.room_card.media.alt", { label }),
      src: mainSrc,
      srcSet
    }
  })
}

export function hotelMediaMapper(medias: ApiHotelMedia[], label: string): UiImageProps[] {
  if (medias.length === 0) {
    return getDefaultUiImageProps(label)
  }

  return medias.map((media) => {
    const formats = sortMediasByResolution(media.formats) as ApiMediaFormat[]
    const mainSrc = media.formats.find((format) => format.path)?.path || ""

    const srcSetObject: UiImageMediaUnit = formats.reduce<UiImageMediaUnit>((acc, curr) => {
      acc[curr.format] = curr.path
      return acc
    }, {})

    const srcSet: UiImageProps["srcSet"] = {
      count: formats.length,
      medias: [srcSetObject]
    }

    return {
      alt: global.t("components.sidebar.media_alt", { label }),
      src: mainSrc,
      srcSet
    }
  })
}

export function getDefaultUiImageProps(label: string): UiImageProps[] {
  return [
    {
      alt: global.t("components.stay_view.room_card.media.no_image", { label }),
      src: "",
      srcSet: {
        medias: []
      }
    }
  ]
}

export function galleryMapper(count: number, medias: UiImageProps[]): GalleryType {
  return {
    count: count,
    medias: medias
  }
}
