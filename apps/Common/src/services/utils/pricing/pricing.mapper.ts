import { Categories } from "@sb-components/molecules/UiPrincingSection/enums"
import { CountryMarket } from "../../../services/pos/pos.enums"
import { Offer } from "../../offer/offer.types"
import { PricingCategory } from "../../../services/offer/offer.enums"
import { PricingType } from "@sb-components/molecules/UiPrincingSection/types"
import { UiPricingSectionProps } from "@sb-components/molecules/UiPrincingSection/interface"
import { i18n } from "../../../i18n"
import { roundedPriceHelper } from "../../../helpers/roundedPriceHelper"
import { useUserPOS } from "../../../composables/useUserPOS"
const { global } = i18n

function mapCategories(categories: string[]): Categories[] {
  return categories.map((categoryStr) => Categories[categoryStr as keyof typeof Categories] || Categories.STANDARD)
}

function determineAggregationType(stayLength: number, countryMarket: string): string {
  const isAveragePerNightApplicable = stayLength !== 2 && countryMarket !== CountryMarket.US

  return isAveragePerNightApplicable
    ? global.t("components.stay_view.room_card.price_section.per_night")
    : global.t("components.stay_view.room_card.price_section.per_stay")
}

function calculateFormattedAmounts(
  pricing: Offer["pricing"],
  isAveragePerNight: boolean
): {
  main: { amount: number; formatted: string; currency: string }
  alternative: { amount: number; formatted: string; currency: string }
} {
  const mainFormattedAmount = isAveragePerNight ? pricing.main.average : pricing.main
  const alternativeAmount = isAveragePerNight ? pricing.alternative?.average : pricing.alternative

  return {
    alternative: {
      amount: alternativeAmount?.amount || 0,
      currency: pricing.currency,
      formatted: alternativeAmount?.formattedAmount || "0"
    },
    main: {
      amount: mainFormattedAmount.amount,
      currency: pricing.currency,
      formatted: mainFormattedAmount.formattedAmount
    }
  }
}

export function offerPricingMapper(
  pricing: Offer["pricing"],
  stayLength: number,
  countryMarket: string,
  isLogged: boolean,
  expediaCompliant: boolean
): UiPricingSectionProps {
  const { userPOS } = useUserPOS()

  const formattedAggregationType = determineAggregationType(stayLength, countryMarket)

  const { main: mainFormattedAmount, alternative: alternativeAmount } = calculateFormattedAmounts(
    pricing,
    stayLength !== 2 && countryMarket !== CountryMarket.US
  )

  const hasMemberRate = pricing.main.categories.includes(PricingCategory.MEMBER_RATE)

  const showAlternativePrice = !isLogged && expediaCompliant && hasMemberRate

  const displayedMainAmount = showAlternativePrice && pricing.alternative ? alternativeAmount : mainFormattedAmount

  const mainPricing: PricingType = {
    amount: pricing.main.amount,
    categories: mapCategories(pricing.main.categories),
    formattedAmount: roundedPriceHelper(displayedMainAmount.amount, userPOS.fullLocale, displayedMainAmount.currency)
  }

  const alternativePricing: PricingType = pricing.alternative && {
    amount: pricing.alternative.amount,
    categories: mapCategories(pricing.alternative.categories),
    formattedAmount: roundedPriceHelper(alternativeAmount.amount, userPOS.fullLocale, alternativeAmount.currency)
  }

  return {
    alternativePricing,
    currency: pricing.currency,
    formattedAggregationType,
    formattedTaxType: pricing.formattedTaxType,
    mainPricing
  }
}
