import { ApiBasketSummary } from "./form.types"
import { brandConfiguration } from "../../global/consts"
import { useApiManager } from "../../composables"

export async function getBasketSummary(basketId: string, currency: string, countryMarket: string) {
  const { apiManager } = useApiManager()

  const url = new URL(
    `${brandConfiguration.apiUrl}basket/webdirectchannels/v1/baskets/${basketId}/summary`,
    window.location.origin
  )
  url.searchParams.set("currency", currency)
  url.searchParams.set("countryMarket", countryMarket)

  const { data: response } = await apiManager.value.get<ApiBasketSummary>(url.toString())

  return response
}
