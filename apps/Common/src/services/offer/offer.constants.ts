import { RoomClassCode } from "./offer.enums"
import { i18n } from "../../i18n"

const { global } = i18n

export const ROOM_CLASS_TITLES: Record<RoomClassCode, string> = {
  [RoomClassCode.FMT]: global.t("components.stay_view.room_section.rooms_label.fairmont"),
  [RoomClassCode.DLX]: global.t("components.stay_view.room_section.rooms_label.deluxe"),
  [RoomClassCode.OTHER]: global.t("components.stay_view.room_section.rooms_label.other")
}
