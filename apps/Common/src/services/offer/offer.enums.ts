export enum AvailabilityStatus {
  AVAILABLE = "AVAILABLE",
  UNAVAILABLE = "UNAVAIL<PERSON>LE"
}

export enum AggregationPricingType {
  AVERAGE_PER_NIGHT = "AVERAGE_PER_NIGHT",
  TOTAL_STAY = "TOTAL_STAY"
}

export enum CancellationPolicy {
  FREE_CANCELLATION = "FREE_CANCELLATION",
  NO_CANCELLATION = "NO_CANCELLATION",
  RESTRICTED_CANCELLATION = "RESTRICTED_CANCELLATION"
}

export enum Deduction {
  MEMBER_RATE = "MEMBER_RATE",
  PREFERRED = "PREFERRED",
  CORPORATE = "CORPORATE"
}

export enum LengthOfStayUnit {
  DAY = "DAY",
  NIGHT = "NIGHT"
}

export enum MealPlan {
  EUROPEAN_PLAN = "EUROPEAN_PLAN",
  FULL_BOARD = "FULL_BOARD"
}

export enum OfferCategoryCode {
  MEMBER_RATE = "MEMBER_RATE",
  LOYALTY_RESIDENTIAL_OWNERS = "LOYALTY_RESIDENTIAL_OWNERS",
  LOYALTY_ALL_PRIVILEGED = "LOYALTY_ALL_PRIVILEGED",
  PREFERRED = "PREFERRED"
}

export enum OfferType {
  ROOM = "ROOM",
  PACKAGE = "PACKAGE"
}

export enum PolicyGuarantee {
  NO_PREPAY = "NO_PREPAY"
}

export enum PricingCategory {
  CORPORATE_NEGOTIATED_RATE = "CORPORATE_NEGOTIATED_RATE",
  LOYALTY_ALL_PLUS_IBIS = "LOYALTY_ALL_PLUS_IBIS",
  LOYALTY_RESIDENTIAL_OWNERS = "LOYALTY_RESIDENTIAL_OWNERS",
  AGENCY = "AGENCY",
  PREMIUM_AVAILABILITY = "PREMIUM_AVAILABILITY",
  FAMILY = "FAMILY",
  MEMBER_RATE = "MEMBER_RATE"
}

export enum TaxType {
  ALL_TAXES_AND_FEES_INCLUDED = "ALL_TAXES_AND_FEES_INCLUDED",
  TAXES_EXCLUDED = "TAXES_EXCLUDED"
}

export enum RoomClassCode {
  FMT = "FMT",
  DLX = "DLX",
  OTHER = "NA"
}
