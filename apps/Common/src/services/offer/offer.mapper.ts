import {
  ApiBestOffers,
  ApiPricingCondition,
  Offer,
  OfferMappedType,
  PerUnit,
  Policy,
  TaxesAndFees
} from "./offer.types"
import { KeyFeaturesCode, KeyFeaturesOrder } from "../../services/utils/keyFeatures/keyFeatures.enums"
import { PricingCategory, RoomClassCode } from "./offer.enums"
import { RateCode, RateIcons } from "../utils/amenities/amenities.enums"
import {
  RoomCancellationPolicy,
  UiCancellationPolicyProps
} from "@sb-components/molecules/UiCancellationPolicy/interface"
import { galleryMapper, getDefaultUiImageProps, roomCardmediaMapper } from "../utils/media/media.mapper"
import { AmenitiesColor } from "@sb-components/atoms/UiAmenity/enums"
import { Icon } from "@sb-assets/icons"
import { MatchedPair } from "./offer.interface"
import { PoliciesType } from "@sb-components/molecules/UiPricingDetailsModalContent/interface"
import { ROOM_CLASS_TITLES } from "./offer.constants"
import { UiAmenityProps } from "@sb-components/atoms/UiAmenity/interface"
import { UiRateCardProps } from "@sb-components/molecules/UiRateCard/interface"
import { i18n } from "../../i18n"
import { offerPricingMapper } from "../utils/pricing/pricing.mapper"
import { rateCardPriceDisplay } from "@sb-components/molecules/UiRateCard/constants"
import { roomCardKeyFeaturesMapper } from "../utils/keyFeatures/keyFeatures.mapper"
import { roundedPriceHelper } from "../../helpers/roundedPriceHelper"
import { truncateApiTitle } from "@shared/utils"
import { useUserPOS } from "../../composables/useUserPOS"
const { global } = i18n

export function offerMapper(
  offer: Offer,
  stayLength: number,
  countryMarket: string,
  isLogged: boolean,
  expediaCompliant: boolean
): OfferMappedType {
  const roomImages = offer.product.media ? roomCardmediaMapper(offer.product.media.medias, offer.product.label) : []

  const amenitiesMappedAndSorted = roomCardKeyFeaturesMapper(offer.product.bedding, offer.product.keyFeatures)
    .filter((feature) => KeyFeaturesOrder.includes(feature.code as KeyFeaturesCode))
    .sort(
      (a, b) =>
        KeyFeaturesOrder.indexOf(a.code as KeyFeaturesCode) - KeyFeaturesOrder.indexOf(b.code as KeyFeaturesCode)
    )

  return {
    id: offer.id,
    rateId: offer.rate.id,
    roomCard: {
      amenities: amenitiesMappedAndSorted,
      bedding: offer.product.bedding?.details?.[0]?.label || "",
      gallery: galleryMapper(offer.product.media?.count || 1, roomImages),
      image: roomImages.length ? roomImages[0] : getDefaultUiImageProps("")[0],
      isOpen: false,
      pricing: offerPricingMapper(offer.pricing, stayLength, countryMarket, isLogged, expediaCompliant),
      productCode: offer.product.id,
      title: truncateApiTitle(offer.product.label)
    }
  }
}

export function pricingConditionMapper(pricingCondition: ApiPricingCondition) {
  return {
    dailyRates: dailyRatesMapper(pricingCondition.perUnit, pricingCondition.taxesAndFees),
    policies: policiesMapper(pricingCondition.policies),
    rate: {
      description: pricingCondition.offer.rate.description,
      title: pricingCondition.offer.rate.label
    },
    taxLabel: pricingCondition.offer.pricing.formattedTaxType,
    totalRate: pricingCondition.offer.pricing.main.formattedAmount
  }
}

export function rateCardMapper(
  offers: Offer[],
  isExpediaCompliant: boolean,
  isUserLoggedIn: boolean,
  isMultiNights: boolean,
  isUSLocation: boolean
): UiRateCardProps[] {
  return offers.map((offer) => {
    const rateCardAmounts = getRateCardPricingInfos(
      offer.pricing,
      isUserLoggedIn,
      isMultiNights,
      isExpediaCompliant,
      isUSLocation
    )

    return {
      ...rateCardAmounts,
      amenities: rateCardAmenitiesMapper(
        offer.pricing.deduction,
        offer.pricing.main.simplifiedPolicies,
        offer.mealPlan,
        offer.pricing.main,
        isExpediaCompliant,
        isUserLoggedIn
      ),
      formattedTaxType: offer.pricing.formattedTaxType,
      rateCardId: offer.id,
      title: offer.rate.label
    }
  })
}

export function policiesMapper(policies: Policy[]): PoliciesType[] {
  return policies.map((policy) => ({
    label: policy.label,
    text: policy.description
  }))
}

export function dailyRatesMapper(perUnit: PerUnit[], taxesAndFees: TaxesAndFees) {
  const fee = taxesAndFees.included[0] || { label: "" }

  return perUnit.map((unit) => ({
    date: unit.from,
    fee,
    price: unit.formattedAmount
  }))
}

export function getRateCardPricingInfos(
  pricing: Offer["pricing"],
  isUserLoggedIn: boolean,
  isMultiNights: boolean,
  isExpediaCompliant: boolean,
  isUSLocation: boolean
): {
  aggregationLabel: string
  currency: string
  formattedTaxType?: string
  prominentMemberPrice?: string
  publicPrice: string
  secondaryMemberPrice?: string
  secondaryPublicPrice?: string
  publicPriceDisplay?: rateCardPriceDisplay
  memberPriceDisplay?: rateCardPriceDisplay
} {
  const { userPOS } = useUserPOS()
  const { main, alternative } = pricing
  const totalForYourStayLabel = global.t("components.stay_view.rate_card.price_section.total_for_your_stay")
  const averagePerNightLabel = global.t("components.stay_view.rate_card.price_section.average_per_night")

  let publicPrice,
    secondaryPublicPrice,
    prominentMemberPrice,
    secondaryMemberPrice,
    aggregationLabel,
    publicPriceDisplay,
    memberPriceDisplay

  if (main.categories.includes(PricingCategory.MEMBER_RATE)) {
    if (isMultiNights) {
      const priceInfo = isUSLocation
        ? { amount: alternative.amount, formatted: alternative.formattedAmount, label: totalForYourStayLabel }
        : {
            amount: alternative.average.amount,
            formatted: alternative.average.formattedAmount,
            label: averagePerNightLabel
          }

      publicPrice = roundedPriceHelper(priceInfo.amount, userPOS.fullLocale, pricing.currency)
      aggregationLabel = priceInfo.label

      if (!isUserLoggedIn) {
        const secondaryInfo = isUSLocation
          ? { amount: alternative.average.amount, formatted: alternative.average.formattedAmount }
          : { amount: alternative.amount, formatted: alternative.formattedAmount }

        secondaryPublicPrice = roundedPriceHelper(secondaryInfo.amount, userPOS.fullLocale, pricing.currency)
      }
    } else {
      publicPrice = roundedPriceHelper(alternative.amount, userPOS.fullLocale, pricing.currency)
      aggregationLabel = totalForYourStayLabel
    }

    if (isUserLoggedIn) {
      const memberPriceInfo = isUSLocation
        ? { amount: main.amount, formatted: main.formattedAmount }
        : { amount: main.average.amount, formatted: main.average.formattedAmount }

      prominentMemberPrice = roundedPriceHelper(memberPriceInfo.amount, userPOS.fullLocale, pricing.currency)

      if (!isMultiNights) {
        const secondaryMemberInfo = isUSLocation
          ? { amount: main.average.amount, formatted: main.average.formattedAmount }
          : { amount: main.amount, formatted: main.formattedAmount }

        secondaryMemberPrice = roundedPriceHelper(secondaryMemberInfo.amount, userPOS.fullLocale, pricing.currency)
      }
    }

    publicPriceDisplay = isUserLoggedIn ? rateCardPriceDisplay.STRIKETHROUGH : rateCardPriceDisplay.REGULAR_WITH_LABEL

    if (!isUserLoggedIn && isExpediaCompliant) {
      memberPriceDisplay = rateCardPriceDisplay.REGULAR_WITH_LABEL
    }
  } else {
    const priceInfo =
      !isMultiNights || isUSLocation
        ? { amount: main.amount, formatted: main.formattedAmount, label: totalForYourStayLabel }
        : { amount: main.average.amount, formatted: main.average.formattedAmount, label: averagePerNightLabel }

    publicPrice = roundedPriceHelper(priceInfo.amount, userPOS.fullLocale, pricing.currency)
    aggregationLabel = priceInfo.label
  }

  return {
    aggregationLabel,
    currency: pricing.currency,
    formattedTaxType: pricing.formattedTaxType,
    memberPriceDisplay,
    prominentMemberPrice,
    publicPrice,
    publicPriceDisplay,
    secondaryMemberPrice,
    secondaryPublicPrice
  }
}

export function cancellationPoliciesMapper(
  simplifiedPoliciesArray: Offer["pricing"]["main"]["simplifiedPolicies"][]
): UiCancellationPolicyProps | null {
  if (!simplifiedPoliciesArray?.length) return null

  const roomsCancellationPolicy = simplifiedPoliciesArray
    .map((simplifiedPolicies, index) => {
      if (!simplifiedPolicies) return null

      const cancellationIcon = mapAmenitiesIcons(simplifiedPolicies.cancellation?.code)
      if (!cancellationIcon) return null

      const color =
        simplifiedPolicies.cancellation.code.toString() === RateCode.FREE_CANCELLATION
          ? AmenitiesColor.GREEN_500
          : AmenitiesColor.CAVIAR_BLACK_700

      return {
        cancellationPolicy: {
          accessibilityIconLabel: cancellationIcon.accessibilityIconLabel,
          iconColor: color,
          iconName: cancellationIcon.iconName,
          label: simplifiedPolicies.cancellation.label,
          labelColor: color
        },
        description: simplifiedPolicies.guarantee.label,
        id: (index + 1).toString()
      }
    })
    .filter(Boolean) as RoomCancellationPolicy[]

  if (!roomsCancellationPolicy.length) return null

  return { roomsCancellationPolicy }
}

function createAmenityDetail(
  icon: { accessibilityIconLabel: string; iconName: Icon } | null,
  label: string,
  color: AmenitiesColor = AmenitiesColor.CAVIAR_BLACK_700
): UiAmenityProps | null {
  if (!icon) return null

  return {
    accessibilityIconLabel: icon.accessibilityIconLabel,
    iconColor: color,
    iconName: icon.iconName,
    label,
    labelColor: color
  }
}

function getGuaranteeAmenity(
  simplifiedPolicies: Offer["pricing"]["main"]["simplifiedPolicies"]
): UiAmenityProps | null {
  const guaranteeIcon = mapAmenitiesIcons(simplifiedPolicies.guarantee?.code)
  return createAmenityDetail(guaranteeIcon, simplifiedPolicies.guarantee?.label)
}

function getCancellationAmenity(
  simplifiedPolicies: Offer["pricing"]["main"]["simplifiedPolicies"]
): UiAmenityProps | null {
  const cancellationIcon = mapAmenitiesIcons(simplifiedPolicies.cancellation?.code)
  if (!cancellationIcon) return null

  const color =
    simplifiedPolicies.cancellation.code.toString() === RateCode.FREE_CANCELLATION
      ? AmenitiesColor.GREEN_500
      : AmenitiesColor.CAVIAR_BLACK_700

  return createAmenityDetail(cancellationIcon, simplifiedPolicies.cancellation.label, color)
}

function getMealPlanAmenity(mealPlan: Offer["mealPlan"]): UiAmenityProps | null {
  const mealPlanIcon = mapAmenitiesIcons(mealPlan?.code)
  return mealPlanIcon && mealPlan?.label ? createAmenityDetail(mealPlanIcon, mealPlan.label) : null
}

function getMemberRateAmenity(
  mainPrice: Offer["pricing"]["main"],
  deduction: Offer["pricing"]["deduction"],
  isUserLogged: boolean,
  isExpediaCompliant: boolean
): UiAmenityProps | null {
  const isMainPriceMemberRate = mainPrice.categories.includes(PricingCategory.MEMBER_RATE)
  const allIcon = mapAmenitiesIcons(RateCode.ALL_ICON)

  if (!isMainPriceMemberRate || !allIcon) return null

  let label
  if (isUserLogged) {
    label = global.t("components.stay_view.rate_card.amenities.member_rate_deduction_logged")
  } else if (isExpediaCompliant) {
    label = global.t("components.stay_view.rate_card.amenities.member_rate_deduction_compliance", {
      amount: deduction[0].formattedAmount
    })
  } else {
    label = global.t("components.stay_view.rate_card.amenities.member_rate_deduction_unlogged")
  }

  return createAmenityDetail(allIcon, label, AmenitiesColor.STRATOS_BLUE_800)
}

export function rateCardAmenitiesMapper(
  deduction: Offer["pricing"]["deduction"],
  simplifiedPolicies: Offer["pricing"]["main"]["simplifiedPolicies"],
  mealPlan: Offer["mealPlan"],
  mainPrice: Offer["pricing"]["main"],
  isExpediaCompliant: boolean,
  isUserLogged: boolean
): UiAmenityProps[] {
  const amenities = [
    getGuaranteeAmenity(simplifiedPolicies),
    getCancellationAmenity(simplifiedPolicies),
    getMealPlanAmenity(mealPlan),
    getMemberRateAmenity(mainPrice, deduction, isUserLogged, isExpediaCompliant)
  ]

  return amenities.filter((amenity): amenity is UiAmenityProps => amenity !== null)
}

function mapAmenitiesIcons(code: string): { accessibilityIconLabel: string; iconName: Icon } | null {
  switch (code) {
    case RateCode.NO_PREPAY:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.guarantee"),
        iconName: RateIcons.CREDIT_CARD
      }
    case RateCode.PREPAID:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.guarantee"),
        iconName: RateIcons.CHECK
      }
    case RateCode.RESTRICTED_CANCELLATION:
      return {
        accessibilityIconLabel: global.t(
          "components.stay_view.rate_card.amenities.accessibility_icon_label.cancellation"
        ),
        iconName: RateIcons.TIME
      }
    case RateCode.FREE_CANCELLATION:
      return {
        accessibilityIconLabel: global.t(
          "components.stay_view.rate_card.amenities.accessibility_icon_label.cancellation"
        ),
        iconName: RateIcons.TIME
      }
    case RateCode.NO_CANCELLATION:
      return {
        accessibilityIconLabel: global.t(
          "components.stay_view.rate_card.amenities.accessibility_icon_label.cancellation"
        ),
        iconName: RateIcons.NOT_REFUNDABLE
      }
    case RateCode.FULL_BOARD:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.meal_plan"),
        iconName: RateIcons.RESTAURANT
      }
    case RateCode.HALF_BOARD:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.meal_plan"),
        iconName: RateIcons.RESTAURANT
      }
    case RateCode.ALL_INCLUSIVE:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.meal_plan"),
        iconName: RateIcons.RESTAURANT
      }
    case RateCode.BED_AND_BREAKFAST:
      return {
        accessibilityIconLabel: global.t("components.stay_view.rate_card.amenities.accessibility_icon_label.meal_plan"),
        iconName: RateIcons.BREAKFAST
      }
    case RateCode.ALL_ICON:
      return {
        accessibilityIconLabel: global.t(
          "components.stay_view.rate_card.amenities.accessibility_icon_label.loyalty_discount_description"
        ),
        iconName: RateIcons.ALL_ICON
      }
    default:
      return null
  }
}

export function normalizeRoomClassCode(code?: RoomClassCode): RoomClassCode {
  return code && code in RoomClassCode ? code : RoomClassCode.OTHER
}

export function buildMatchedPairs(
  offers: Offer[],
  countryMarket: string,
  nbNights: number,
  isLogged: boolean,
  expediaCompliant: boolean
): MatchedPair[] {
  return offers.map((offer) => {
    const offerMapped = offerMapper(offer, nbNights, countryMarket, isLogged, expediaCompliant)

    return { offerMapped }
  })
}

export function mapAndGroupByRoomClass(
  offers: Offer[],
  countryMarket: string,
  nbNights: number,
  isLogged: boolean,
  expediaCompliant: boolean,
  groupBy: ApiBestOffers["groupBy"]
) {
  const pairs = buildMatchedPairs(offers, countryMarket, nbNights, isLogged, expediaCompliant)

  const groupedRoomClasses = groupBy.roomClasses.reduce(
    (acc, roomClass) => {
      const code = normalizeRoomClassCode(roomClass.code)
      if (!acc[code]) {
        acc[code] = {
          bestOfferIds: [],
          code,
          title: ROOM_CLASS_TITLES[code]
        }
      }
      acc[code].bestOfferIds.push(...roomClass.bestOfferIds)
      return acc
    },
    {} as Record<RoomClassCode, { code: RoomClassCode; bestOfferIds: string[]; title: string }>
  )

  return Object.values(groupedRoomClasses).map(({ code, bestOfferIds, title }) => {
    const sectionPairs = pairs.filter((p) => bestOfferIds.includes(p.offerMapped.id))
    return { code, pairs: sectionPairs, title }
  })
}
