import { AggregationPricingType, LengthOfStayUnit } from "./offer.enums"
import { ApiBestOffers, ApiPricingCondition, RoomRateApiResponse } from "./offer.types"
import { AccommodationWrapper } from "../hotels/hotels.types"
import { AccorApiEndpoints } from "../../global/consts"
import { ApiResponse } from "../api/ApiManager"
import { DateMoment } from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import { UiRateCardProps } from "@sb-components/molecules/UiRateCard/interface"
import { calculateNights } from "@shared/utils"
import { rateCardMapper } from "./offer.mapper"
import { useApiManager } from "../../composables/useApiManager"
import { useSearch } from "../../composables"
import { useUserPOS } from "../../composables/useUserPOS"
import { useUserStore } from "../../stores/user/user"

export async function fetchOffers(
  hotelId: string,
  dateInValue: string,
  nbNights: number,
  nbAdults: number,
  childrenAges: number[],
  currency: string,
  hasAccessibility: boolean,
  country: string
): Promise<Pick<ApiBestOffers, "availability" | "bestOffers" | "groupBy">> {
  const { apiManager } = useApiManager()

  const offersBody = {
    adults: nbAdults,
    childrenAges,
    countryMarket: country,
    currency,
    dateIn: dateInValue,
    filter: hasAccessibility ? "PMR" : undefined,
    groupBy: "ROOM_CLASS",
    includeAverageAmount: true,
    "lengthOfStay.unit": LengthOfStayUnit.NIGHT,
    "lengthOfStay.value": nbNights,
    pricingContext: AggregationPricingType.TOTAL_STAY
  }
  const hotelEndpoint = AccorApiEndpoints.offers.replace("{hotelId}", hotelId)
  const { data: response } = await apiManager.value.get<ApiBestOffers>(hotelEndpoint, {
    params: offersBody
  })

  return { availability: response.availability, bestOffers: response.bestOffers, groupBy: response.groupBy }
}

export async function getSelectedRoomRateCards(
  productCode: string,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selection?: { rateCode: string; productCode: string }[]
): Promise<UiRateCardProps[]> {
  const { apiManager } = useApiManager()
  const { dates, rooms, hotel } = useSearch()
  const { isLogged } = useUserStore()
  const { userPOS } = useUserPOS()
  const dateIn = dates.value[DateMoment.START]
  const dateOut = dates.value[DateMoment.END]

  try {
    if (!hotel.value || !dateOut || !dateIn || !rooms.value.length) {
      throw new Error("Missing required parameters for fetching room offers")
    }

    const nbNights = calculateNights(dateIn, dateOut)
    const adults = rooms.value[0].adults
    const childrenAges = rooms.value[0].childrenAges

    const data = {
      adults: adults,
      childrenAges: childrenAges,
      countryMarket: userPOS.countryMarket,
      currency: userPOS.currency,
      dateIn: dateIn.toISOString().split("T")[0],
      includeAverageAmount: true,
      lengthOfStay: {
        unit: LengthOfStayUnit.NIGHT,
        value: nbNights
      },
      pricingContext: AggregationPricingType.TOTAL_STAY,
      productCodes: [productCode]
      // selection TODO FIX
    }

    const productOffersEndpoint = AccorApiEndpoints.productOffer(hotel.value.id)

    const { data: response } = await apiManager.value.post<RoomRateApiResponse>(productOffersEndpoint, data)

    return rateCardMapper(
      response.offers,
      hotel.value?.expediaCompliant,
      isLogged,
      nbNights > 1,
      userPOS.countryMarket === "US"
    )
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error fetching user details:", error)
    return []
  }
}

export async function getOfferDetails(offerId: string): Promise<object[]> {
  const { apiManager } = useApiManager()
  const { userPOS } = useUserPOS()

  const params = {
    countryMarket: userPOS.countryMarket,
    offerId: offerId
  }

  const { data: response } = await apiManager.value.get<ApiResponse<object[]>>(AccorApiEndpoints.pricing, {
    params
  })

  return response.data
}

export async function getPricingCondition(offerId: string): Promise<ApiPricingCondition> {
  const { apiManager } = useApiManager()
  const { userPOS } = useUserPOS()

  const params = {
    countryMarket: userPOS.countryMarket,
    currency: userPOS.currency,
    offerId: offerId
  }

  const { data: response } = await apiManager.value.get<ApiPricingCondition>(AccorApiEndpoints.pricing, {
    params
  })

  return response
}

export async function getAccommodations(hotelId: string, offersId: string): Promise<AccommodationWrapper> {
  const { apiManager } = useApiManager()

  const params = {
    id: offersId
  }

  const accommodationsEndpoint = AccorApiEndpoints.accommodations.replace("{hotelId}", hotelId)

  const { data: response } = await apiManager.value.get<AccommodationWrapper>(accommodationsEndpoint, { params })

  return response
}
