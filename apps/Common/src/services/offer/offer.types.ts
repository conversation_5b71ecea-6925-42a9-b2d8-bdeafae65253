import {
  AggregationPricingType,
  AvailabilityStatus,
  CancellationPolicy,
  Deduction,
  LengthOfStayUnit,
  MealPlan,
  OfferCategoryCode,
  OfferType,
  PolicyGuarantee,
  PricingCategory,
  RoomClassCode,
  TaxType
} from "./offer.enums"
import { UiRoomCardProps } from "@sb-components/organisms/UiRoomCard/interface"

export interface ApiBestOffers {
  hotel: {
    id: string
    name: string
    brand: string
    localRating: number
  }
  availability: {
    status: AvailabilityStatus
    reasons: {
      label: string
      code: string
    }
  }
  bestOffers: Offer[]
  countryMarket: string
  groupBy: {
    roomClasses: RoomClass[]
  }
}

export interface RoomRateApiResponse {
  selection: {
    step: number
    input: string[]
  }
  offers: Offer[]
}

export interface Offer {
  burnable?: boolean
  categories: OfferCategory[]
  compliances: {
    code: string
    description: string
  }[]
  dateIn: string
  id: string
  lengthOfStay: {
    value: number
    unit: LengthOfStayUnit
  }
  matches: string[]
  mealPlan: {
    code: MealPlan
    label?: string
  }
  occupancy: {
    adults: number
    childrenAges: number[]
    childrenAgesAsAdult: number[]
    babies: number
  }
  offerCategory?: string
  pricing: OfferPricing
  product: OfferProduct
  rate: {
    description: string
    id: string
    label: string
    productBestOffer?: boolean
  }
  remaining?: number
  type: OfferType
}

export interface OfferProduct {
  accessible: boolean
  bedding: {
    details: BeddingFormat[]
    extra: object
  }
  classification: {
    type: {
      code: string
      label: string
    }
    standard: {
      code: string
      label: string
    }
  }
  id: string
  keyFeatures: {
    code: string
    label: string
  }[]
  label: string
  media?: OfferMedia
  quantity: number
}

export interface OfferCategory {
  code: OfferCategoryCode
  tag: string
  description: string
  label?: string
  additionalTag?: string
}

export interface OfferPricing {
  currency: string
  aggregationType: AggregationPricingType
  formattedAggregationType: string
  taxType: TaxType
  formattedTaxType: string
  deduction: {
    percent: number
    amount: number
    formattedAmount: string
    type: Deduction
  }[]
  alternative: {
    amount: number
    average: {
      amount: number
      formattedAmount: string
    }
    formattedAmount: string
    categories: PricingCategory[]
    label: string
    reference: {
      amount: number
      formattedAmount: string
    }
    bookable: boolean
  }
  main: {
    amount: number
    average: {
      amount: number
      formattedAmount: string
    }
    formattedAmount: string
    categories: PricingCategory[]
    label: string
    reference: {
      amount: number
      formattedAmount: string
      categories: PricingCategory[]
    }
    feesTotalAmount: {
      included: {
        label: string
        breakdown: [string, string]
      }
      excluded: {
        label: string
        breakdown: [string, string]
      }
    }
    simplifiedPolicies: {
      cancellation: {
        code: CancellationPolicy
        label: string
      }
      guarantee: {
        code: PolicyGuarantee
        label: string
      }
    }
  }
}

export interface OfferMedia {
  category: string
  count: number
  formats: MediaFormat[]
  medias: {
    category: string
    formats: MediaFormat[]
    type: string
  }[]
}

export interface MediaFormat {
  format: string
  generic: boolean
  lastUpdate: string
  url: string
}

export interface BeddingFormat {
  label: string
  quantity: number
}

export interface ApiPricingCondition {
  offer: Offer
  policies: Policy[]
  perUnit: PerUnit[]
  taxesAndFees: TaxesAndFees
}

export interface PerUnit {
  from: string
  to: string
  formattedDate: string
  amount: number
  formattedAmount: string
  mealPlan: {
    code: MealPlan
  }
}

export interface TaxesAndFees {
  included: Fee[]
  excluded: Fee[]
}

export interface Fee {
  code: string
  label: string
}

export interface Policy {
  code: string
  label: string
  description: string
}

export interface OfferMappedType {
  rateId: string
  roomCard: UiRoomCardProps
  id: string
}

export interface RoomClass {
  bestOfferIds: string[]
  code?: RoomClassCode
}

export interface OffersWithGroupBy {
  offers: Offer[]
  groupBy: ApiBestOffers["groupBy"]
}
