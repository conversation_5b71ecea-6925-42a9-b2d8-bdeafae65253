import { ApiIdentification, ApiUserResponse } from "./users.types"
import { useApiManager, useSearch } from "../../composables"
import { AccorApiEndpoints } from "../../global/consts"
import { Identification } from "../../composables/useIdentificationPlainText"
import { SPECIAL_RATES_MAPPING } from "@sb-components/organisms/UiSpecialRates/constants"
import { SpecialRates } from "@sb-components/organisms/UiSpecialRates/enums"
import { User } from "@stores/user/interface"
import { useApiSemaphore } from "../../composables/useApiSemaphore"
import { useUserStore } from "@stores/user/user"

export async function getUserIdentification(currentIdentification: Identification): Promise<Identification> {
  const { apiManager } = useApiManager()
  const { specialRate, promoCode } = useSearch()

  const identificationData = {
    identification: currentIdentification
  }

  const identificationId = currentIdentification.identificationId

  let offers: { code: string; type: "PROMOTIONAL" }[] = []

  if (specialRate.value === SpecialRates.PROMO_CODE && promoCode.value) {
    offers.push({
      code: promoCode.value,
      type: "PROMOTIONAL"
    })
  } else {
    const value = SPECIAL_RATES_MAPPING[specialRate.value]
    if (value) {
      const values = Array.isArray(value) ? value : [value]
      offers = values.map((value) => ({
        code: value,
        type: "PROMOTIONAL"
      }))
    }
  }

  identificationData.identification.offerPreference = {
    offer: offers
  }

  if (identificationId !== null) {
    const updateIdentification = `${AccorApiEndpoints.identification}/${identificationId}`
    const { data: response, headers } = await apiManager.value.put<ApiIdentification<Identification>>(
      updateIdentification,
      identificationData
    )
    return { ...response.identification, identificationToken: headers["identification-token"] }
  } else {
    const { data: response, headers } = await apiManager.value.post<ApiIdentification<Identification>>(
      `${AccorApiEndpoints.identification}/plaintext`,
      identificationData
    )
    return { ...response.identification, identificationToken: headers["identification-token"] }
  }
}

export async function getUserDetails(): Promise<void> {
  const { setUserDetailsInProgress } = useApiSemaphore()
  const { apiManager } = useApiManager()
  const { updateInitialUser } = useUserStore()

  const endPoint = AccorApiEndpoints.contactMe
  const fields = [
    "id",
    "contactMediums.contactMedium.emailContact.email",
    "contactMediums.contactMedium.postalAddress.city",
    "contactMediums.contactMedium.postalAddress.countryCode",
    "individual.individualName.firstName",
    "individual.individualName.lastName",
    "individual.isLoyaltyMember",
    "loyalty.loyaltyCards.card.cardProduct.cardCodeTARS",
    "loyalty.loyaltyCards.card.cardProduct.productTier",
    "loyalty.loyaltyCards.card.isLastActiveCard",
    "loyalty.memberInfo.fullEnrolmentDate",
    "loyalty.balances.nbPoints",
    "loyalty.balances.pointsEarnedOnTierUpdate",
    "loyalty.balances.nightsSpentOnTierUpdate"
  ]

  const params = {
    fields: fields.join(",")
  }
  try {
    setUserDetailsInProgress(true)

    const response = await apiManager.value.getWithAccessToken<ApiUserResponse>(endPoint, { params })

    const lastActiveCard = response.loyalty.loyaltyCards.card.find((card) => card.isLastActiveCard)

    const primaryEmailContactMedium = response.contactMediums.contactMedium.find(
      (contact) => contact.emailContact?.isPrimary
    )

    const user: User = {
      city: response.contactMediums.contactMedium[0].postalAddress.city || "",
      countryCode: response.contactMediums.contactMedium[0].postalAddress.countryCode || "",
      email: primaryEmailContactMedium?.emailContact.email || "",
      firstName: response.individual.individualName.firstName,
      id: response.id,
      isLoyaltyMember: response.individual.isLoyaltyMember,
      lastName: response.individual.individualName.lastName,
      loyaltyCardType: lastActiveCard?.cardProduct.cardCodeTARS || "",
      nightValue: response.loyalty.balances.nightsSpentOnTierUpdate,
      rewardPointsValue: response.loyalty.balances.nbPoints,
      statusName: lastActiveCard?.cardProduct.productTier || "",
      statusPointsValue: response.loyalty.balances.pointsEarnedOnTierUpdate,
      statusSince: response.loyalty.memberInfo.fullEnrolmentDate
    }

    updateInitialUser(user)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error fetching user details:", error)
  } finally {
    setUserDetailsInProgress(false)
  }
}
