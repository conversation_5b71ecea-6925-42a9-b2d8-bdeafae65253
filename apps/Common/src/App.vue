<script setup lang="ts">
import { computed, inject, onMounted } from "vue"
import {
  useCommerceTracking,
  useCurrentWindowSize,
  useLoader,
  useModal,
  useResetProcess,
  useSearch
} from "./composables"
import { useRoute, useRouter } from "vue-router"
import AllHotelRedirectModal from "./components/AllHotelRedirectModal/AllHotelRedirectModal.vue"
import { AllHotels } from "./global/consts"
import { BrandConfiguration } from "@shared/types"
import Footer from "./components/Footer/Footer.vue"
import Header from "./components/Header/Header.vue"
import { Region } from "./global/enums"
import SearchEngineDesktop from "./components/SearchEngine/SearchEngineDesktop/SearchEngineDesktop.vue"
import SearchEngineModal from "./components/SearchEngine/SearchEngineModal/SearchEngineModal.vue"
import Sidebar from "./components/Sidebar/Sidebar.vue"
import Stepper from "./components/Stepper/Stepper.vue"
import TravelProRedirectModal from "./components/TravelProRedirectModal/TravelProRedirectModal.vue"
import { fetchCountries } from "./services/referentials/referentials.fetch"
import { fetchHotelsByRegion } from "./services/hotels/hotels.fetch"
import { getUserDetails } from "./services/users/users.fetch"
import { replaceUriParams } from "./helpers/uriHelper"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"
import { useHTTPError } from "./composables/useHTTPError"
import { useHotelsStore } from "@stores/hotels/hotels"
import { useI18n } from "vue-i18n"
import { useIdentificationPlainText } from "./composables/useIdentificationPlainText"
import { useIdentificationRefresher } from "./composables/useIdentificationRefresher"
import { useOffersAndAccommodations } from "./composables/useOffersAndAccommodations"
import { useReferentialsStore } from "@stores/referentials/referentials"
import { useSearchQueryParams } from "./composables/useSearchQueryParams"
import { useUserPOS } from "./composables/useUserPOS"
import { useUserStore } from "./stores/user/user"

const config = inject<BrandConfiguration>("brandConfig")!

const router = useRouter()
const { updateIsLoading, updateHotelsList } = useHotelsStore()
const { updateCountries } = useReferentialsStore()
const { loggedUser } = useUserStore()
const cookies = useCookies()
const { hotel, iataCode } = useSearch()
const { isDesktop } = useCurrentWindowSize()
const { refreshIdentification } = useIdentificationPlainText()
const { fetchCommerceTracking } = useCommerceTracking()
const { fetchOffers } = useOffersAndAccommodations()
const { resetProcess } = useResetProcess()
useIdentificationRefresher()
const { queryParams } = useSearchQueryParams()

useUserPOS()

const route = useRoute()
const layout = computed(() => route.meta.layout || "div")
const { locale } = useI18n()
const { error: hotelsError } = useHTTPError("hotels")
const { error: countriesError } = useHTTPError("countries")

const homepageUri = computed(() => {
  return replaceUriParams(import.meta.env.VITE_APP_LEGACY_HOMEPAGE, "", locale.value)
})

//functions
async function getHotels() {
  updateIsLoading(true)
  hotelsError.value = false

  const hotels = await Promise.allSettled([
    fetchHotelsByRegion(Region.AFRICA).catch(fetchHotelError),
    fetchHotelsByRegion(Region.ASIA).catch(fetchHotelError),
    fetchHotelsByRegion(Region.EUROPE).catch(fetchHotelError),
    fetchHotelsByRegion(Region.NORTH_AMERICA).catch(fetchHotelError),
    fetchHotelsByRegion(Region.SOUTH_AMERICA).catch(fetchHotelError)
  ])

  updateHotelsList({
    africa: hotels[0].status === "fulfilled" ? hotels[0].value.results.map((result) => result.hotel) : [],
    asia: hotels[1].status === "fulfilled" ? hotels[1].value.results.map((result) => result.hotel) : [],
    europe: hotels[2].status === "fulfilled" ? hotels[2].value.results.map((result) => result.hotel) : [],
    northAmerica: hotels[3].status === "fulfilled" ? hotels[3].value.results.map((result) => result.hotel) : [],
    southAmerica: hotels[4].status === "fulfilled" ? hotels[4].value.results.map((result) => result.hotel) : []
  })
  updateIsLoading(false)
}

function fetchHotelError() {
  hotelsError.value = true
  return { results: [] }
}

async function getCountries() {
  try {
    countriesError.value = false
    const countries = await fetchCountries()

    updateCountries(countries)
  } catch {
    countriesError.value = true
  }
}

const { openModal: openHotelModal } = useModal("hotel")
const isHotelInBlacklist = () => {
  if (hotel.value?.id && AllHotels.includes(hotel.value?.id)) {
    openHotelModal()
    return true
  }

  return false
}

const { openModal: openTravelProModal } = useModal("travel-pro")
const hasRedirectIataCode = () => {
  if (iataCode.value) {
    openTravelProModal()
    return true
  }
}

const { loading } = useLoader("search")
async function handleSearch() {
  if (hasRedirectIataCode() || isHotelInBlacklist()) return

  loading.value = true

  resetProcess()

  try {
    await router.push({
      ...route,
      query: {
        ...route.query,
        ...queryParams.value
      },
      replace: true
    })

    await fetchOffers()
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  const expires = new Date()
  expires.setTime(expires.getTime() + 60 * 1000 * 60 * 24 * 365) // 1 year
  const appDomain = config.appDomain

  cookies.set("nga", true, { domain: appDomain, path: "/" })
  cookies.set("app_domain", appDomain, { domain: appDomain, expires: expires, path: "/" })
  cookies.set("app_base_url", window.location.origin, { domain: appDomain, expires: expires, path: "/" })

  const oidcUserLogged = cookies.get("oidc_user_logged")

  if (oidcUserLogged && !loggedUser?.id) {
    await getUserDetails()
  }
  await refreshIdentification()

  fetchCommerceTracking()
  getHotels()
  getCountries()
})
</script>

<template>
  <component :is="layout">
    <template #header>
      <Header :logo="config.logo" :logo-link="homepageUri" />
    </template>

    <template #subheader>
      <div class="Subheader">
        <div class="Subheader__content">
          <template v-if="route.name === 'stay'">
            <SearchEngineDesktop v-if="isDesktop" :loading="loading" @search-engine-desktop::check="handleSearch" />
            <template v-else>
              <SearchEngineModal :loading="loading" @search-engine-modal::check="handleSearch" />
            </template>
          </template>
        </div>
      </div>
    </template>

    <template #main-content>
      <Stepper />
      <AllHotelRedirectModal />
      <TravelProRedirectModal />
      <RouterView />
    </template>

    <!-- Step 5 (summary) has no sidebar -->
    <template v-if="route.name !== 'summary'" #sidebar-content>
      <Sidebar />
    </template>

    <template #footer>
      <Footer />
    </template>
  </component>
</template>
