import "@sb-styles/main.scss"
import App from "./App.vue"
import { BrandConfiguration } from "@shared/types"
import brandFormConfig from "./brandForm.config"
import { createApp } from "vue"
import { createHead } from "@vueuse/head"
import { createPinia } from "pinia"
import { defineAdsTheme } from "@accor/ads-components"
import { i18n } from "./i18n"
import { initSplunk } from "./services/utils/tracking/tracking.splunk"
import router from "./router"

//manage page title
// eslint-disable-next-line @typescript-eslint/no-explicit-any
router.afterEach((to: any) => {
  document.title = to.meta.title || import.meta.env.VITE_APP_HEADER_TITLE
})

export const initializeApp = async (config: BrandConfiguration) => {
  const hostName = window.location.hostname
  const parts = hostName.split(".")

  // Remove the first part (subdomain) and join the rest
  const appDomain = "." + parts.slice(1).join(".")

  // Prepare env variables
  config.apiHeaders = {
    clientId: import.meta.env.VITE_APP_CLIENT_ID
  }

  config.appDomain = appDomain

  config.apiUrl = import.meta.env.VITE_APP_API_BASE_URL
  config.brandCode = import.meta.env.VITE_APP_BRAND_CODE
  config.siteCode = import.meta.env.VITE_APP_SITE_CODE

  config.formFieldStructure = brandFormConfig

  config.gtmId = import.meta.env.VITE_APP_GTM_ID
  config.kameleoonId = import.meta.env.VITE_APP_KAMELEOON_ID
  config.oneTrustId = import.meta.env.VITE_APP_ONE_TRUST_ID
  config.splunk = {
    appName: import.meta.env.VITE_APP_SPLUNK_APPNAME,
    critical: import.meta.env.VITE_APP_SPLUNK_CRITICAL,
    domain: import.meta.env.VITE_APP_SPLUNK_DOMAIN,
    env: import.meta.env.VITE_APP_SPLUNK_ENV,
    itDepartment: import.meta.env.VITE_APP_SPLUNK_ITDEPARTEMENT,
    itDomain: import.meta.env.VITE_APP_SPLUNK_ITDOMAIN,
    leanixId: import.meta.env.VITE_APP_SPLUNK_LEANIXID,
    pci: import.meta.env.VITE_APP_SPLUNK_PCI,
    realm: import.meta.env.VITE_APP_SPLUNK_REALM,
    snowId: import.meta.env.VITE_APP_SPLUNK_SNOWID,
    token: import.meta.env.VITE_APP_SPLUNK_ACCESSTOKEN
  }

  //Init RUM script before loading app
  initSplunk(config.splunk)

  const app = createApp(App) // Pass configuration as props
  const head = createHead()
  app.use(head)

  // inject config
  app.provide("brandConfig", config)

  app.use(router) // Common router

  app.use(i18n)

  const pinia = createPinia()
  app.use(pinia)

  // --- Inject Kameleoon Script ---
  if (config.kameleoonId) {
    const kameleoonLoaderScript = document.createElement("script")
    kameleoonLoaderScript.type = "text/javascript"
    kameleoonLoaderScript.textContent = `
    // Duration in milliseconds to wait while the Kameleoon application file is loaded
    var kameleoonLoadingTimeout = 1000;

    window.kameleoonQueue = window.kameleoonQueue || [];
    window.kameleoonStartLoadTime = new Date().getTime();
    if (! document.getElementById("kameleoonLoadingStyleSheet") && ! window.kameleoonDisplayPageTimeOut) {
      var kameleoonS = document.getElementsByTagName("script")[0];
      var kameleoonCc = "* { visibility: hidden !important; background-image: none !important; }";
      var kameleoonStn = document.createElement("style");
      kameleoonStn.type = "text/css";
      kameleoonStn.id = "kameleoonLoadingStyleSheet";
      if (kameleoonStn.styleSheet) {
        kameleoonStn.styleSheet.cssText = kameleoonCc;
      } else {
        kameleoonStn.appendChild(document.createTextNode(kameleoonCc));
      }
      kameleoonS.parentNode.insertBefore(kameleoonStn, kameleoonS);
      window.kameleoonDisplayPage = function(fromEngine) {
        if (!fromEngine) {
          window.kameleoonTimeout = true;
        }
        if (kameleoonStn.parentNode) {
          kameleoonStn.parentNode.removeChild(kameleoonStn);
        }
      };
      window.kameleoonDisplayPageTimeOut = window.setTimeout(window.kameleoonDisplayPage, kameleoonLoadingTimeout);
    }`

    const kameleoonScript = document.createElement("script")
    kameleoonScript.src = `//${config.kameleoonId}.kameleoon.eu/kameleoon.js`
    kameleoonScript.setAttribute("fetchpriority", "high")
    kameleoonScript.async = true

    document.head.append(kameleoonLoaderScript, kameleoonScript)
  }

  // --- Inject OneTrust Script ---
  if (config.oneTrustId) {
    const oneTrustLoaderScript = document.createElement("script")
    oneTrustLoaderScript.src = "https://cdn.cookielaw.org/scripttemplates/otSDKStub.js"
    oneTrustLoaderScript.setAttribute("data-document-language", "true")
    oneTrustLoaderScript.setAttribute("type", "text/javascript")
    oneTrustLoaderScript.setAttribute("charset", "UTF-8")
    oneTrustLoaderScript.setAttribute("data-domain-script", config.oneTrustId)

    // const oneTrustScript = document.createElement("script")
    // oneTrustScript.innerHTML = `function OptanonWrapper() { }`

    const oneTrustScript = document.createElement("script")
    oneTrustScript.innerHTML = `function OptanonWrapper() { }`

    document.head.append(oneTrustLoaderScript, oneTrustScript)
  }

  // --- Inject GTM Script ---
  const gtmScript = document.createElement("script")
  gtmScript.innerHTML = `
function initGTM() {
  (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://sgtm.fairmont.com/qg2bvifzdqcxwc2.js?aw='+i.replace(/^GTM-/, '')+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','${config.gtmId}');
}

// Wait for OneTrust consent before initializing GTM
if (window.Optanon) {
  window.Optanon.OnConsentChanged(function() {
    initGTM();
  });
} else {
  // If OneTrust is not available, add a listener for when it becomes available
  window.addEventListener('OneTrustGroupsUpdated', function() {
    initGTM();
  }, { once: true });
  
  // Fallback in case OneTrust doesn't load or user has already consented
  setTimeout(function() {
    if (!window.dataLayer) {
      initGTM();
    }
  }, 5000);
}
`
  document.head.appendChild(gtmScript)
  // -------------------------

  defineAdsTheme(config.theme)

  return app
}
