import { computed, reactive } from "vue"
import { User } from "./interface"
import { defineStore } from "pinia"

export const useUserStore = defineStore("user", () => {
  const loggedUser = reactive<User>({
    city: "",
    countryCode: "",
    email: "",
    firstName: "",
    id: "",
    isLoyaltyMember: false,
    lastName: "",
    loyaltyCardType: "",
    nightValue: 0,
    rewardPointsValue: 0,
    statusName: "",
    statusPointsValue: 0,
    statusSince: ""
  })

  const isLogged = computed(() => !!loggedUser.id)

  const updateId = (id: string) => {
    loggedUser.id = id
  }

  const updateCity = (city: string) => {
    loggedUser.city = city
  }

  const updateCountryCode = (countryCode: string) => {
    loggedUser.countryCode = countryCode
  }

  const updateEmail = (email: string) => {
    loggedUser.email = email
  }

  const updateFirstName = (firstName: string) => {
    loggedUser.firstName = firstName
  }

  const updateIsLoyaltyMember = (isLoyaltyMember: boolean) => {
    loggedUser.isLoyaltyMember = isLoyaltyMember
  }

  const updateLastName = (lastName: string) => {
    loggedUser.lastName = lastName
  }

  const updateLoyaltyCardType = (loyaltyCardType: string) => {
    loggedUser.loyaltyCardType = loyaltyCardType
  }

  const updateNightValue = (nightValue: number) => {
    loggedUser.nightValue = nightValue
  }

  const updateRewardPointsValue = (rewardPointsValue: number) => {
    loggedUser.rewardPointsValue = rewardPointsValue
  }

  const updateStatusName = (statusName: string) => {
    loggedUser.statusName = statusName
  }

  const updateStatusPointsValue = (statusPointsValue: number) => {
    loggedUser.statusPointsValue = statusPointsValue
  }

  const updateStatusSince = (statusSince: string) => {
    loggedUser.statusSince = statusSince
  }

  const updateInitialUser = (user: User) => {
    Object.assign(loggedUser, user)
  }

  const clearUserStore = () => {
    Object.assign(loggedUser, {
      city: "",
      countryCode: "",
      email: "",
      firstName: "",
      id: "",
      isLoyaltyMember: false,
      lastName: "",
      loyaltyCardType: "",
      nightValue: 0,
      rewardPointsValue: 0,
      statusName: "",
      statusPointsValue: 0,
      statusSince: ""
    })
  }

  return {
    clearUserStore,
    isLogged,
    loggedUser,
    updateCity,
    updateCountryCode,
    updateEmail,
    updateFirstName,
    updateId,
    updateInitialUser,
    updateIsLoyaltyMember,
    updateLastName,
    updateLoyaltyCardType,
    updateNightValue,
    updateRewardPointsValue,
    updateStatusName,
    updateStatusPointsValue,
    updateStatusSince
  }
})
