import { fetchPosCountriesGroupedByContinent, fetchPosCurrenciesGroupedByContinent } from "../../services/pos/pos.fetch"
import { Pos } from "./interface"
import { defineStore } from "pinia"
import { ref } from "vue"
import { useHTTPError } from "../../composables/useHTTPError"

export const usePosStore = defineStore("pos", () => {
  const pos = ref<Pos>({})

  const fetchPosCountriesGroupedByContinentInStore = async () => {
    if (!pos.value.posCountriesGroupedByContinent || pos.value.posCountriesGroupedByContinent.length === 0) {
      const { error: countriesError } = useHTTPError("pos-countries")

      try {
        countriesError.value = false
        pos.value.posCountriesGroupedByContinent = await fetchPosCountriesGroupedByContinent()
      } catch (error) {
        countriesError.value = true
        // eslint-disable-next-line no-console
        console.error("Failed to fetch data:", error)
      }
    }
  }

  const fetchPosCurrenciesGroupedByContinentInStore = async () => {
    if (!pos.value.posCurrenciesGroupedByContinent || pos.value.posCurrenciesGroupedByContinent.length === 0) {
      const { error: currenciesError } = useHTTPError("pos-currencies")
      try {
        currenciesError.value = false
        pos.value.posCurrenciesGroupedByContinent = await fetchPosCurrenciesGroupedByContinent()
      } catch (error) {
        currenciesError.value = true
        // eslint-disable-next-line no-console
        console.error("Failed to fetch data:", error)
      }
    }
  }

  const findCountryObjectByAccorCountryCode = (countryRegionLabel: string) => {
    if (!pos.value.posCountriesGroupedByContinent || !countryRegionLabel) return undefined

    const continent = pos.value.posCountriesGroupedByContinent.find((group) =>
      group.countryList.some((country) => country.accorCountryLanguageLabel === countryRegionLabel)
    )

    return continent?.countryList.find((country) => country.accorCountryLanguageLabel === countryRegionLabel)
  }

  const findCurrencyObjectByAccorCurrencyCode = (currencyCode: string) => {
    if (!pos.value.posCurrenciesGroupedByContinent || !currencyCode) return undefined

    const continent = pos.value.posCurrenciesGroupedByContinent.find((group) =>
      group.currencyList.some((currency) => currency.currencyCode === currencyCode)
    )

    return continent?.currencyList.find((currency) => currency.currencyCode === currencyCode)
  }

  const findContinentCodeByCurrencyCode = (currencyCode: string, isoCountryCode: string) => {
    const matchingContinent = pos.value.posCurrenciesGroupedByContinent?.filter((continent) =>
      continent.currencyList.some((currency) => currency.currencyCode === currencyCode)
    )

    if (!matchingContinent?.length) return ""

    if (matchingContinent.length === 1) {
      return matchingContinent[0].continentCode
    }

    const countryContinentCode = findContinentCodeByCountry(isoCountryCode)

    return (
      matchingContinent.find((continent) => continent.continentCode === countryContinentCode)?.continentCode ||
      matchingContinent[0].continentCode
    )
  }

  const findContinentCodeByCountry = (isoCountryCode: string) => {
    return (
      pos.value.posCountriesGroupedByContinent?.flatMap((continent) =>
        continent.countryList.flatMap((country) =>
          country.isoCountryCode.includes(isoCountryCode) ? [continent.continentCode] : []
        )
      )[0] || ""
    )
  }

  const findCountryLabelByCountryCodeAndLocale = (isoCountryCode: string, locale: string) => {
    let result = pos.value.posCountriesGroupedByContinent?.flatMap((continent) =>
      continent.countryList.filter(
        (country) => country.isoCountryCode.includes(isoCountryCode) && country.languageCode === locale
      )
    )

    // If no result, try to find the country by isoCountryCode only, in case the country is not available in the locale
    if (!result || result.length === 0) {
      result = pos.value.posCountriesGroupedByContinent?.flatMap((continent) =>
        continent.countryList.filter((country) => country.isoCountryCode.includes(isoCountryCode))
      )
    }

    return result?.length ? result[0].accorCountryLanguageLabel : ""
  }

  return {
    fetchPosCountriesGroupedByContinentInStore,
    fetchPosCurrenciesGroupedByContinentInStore,
    findContinentCodeByCountry,
    findContinentCodeByCurrencyCode,
    findCountryLabelByCountryCodeAndLocale,
    findCountryObjectByAccorCountryCode,
    findCurrencyObjectByAccorCurrencyCode,
    pos
  }
})
