import { computed, ref } from "vue"
import {
  datePickerInputsValidation,
  datePickerValidation,
  destinationValidation,
  iataSchema,
  useGA4Event,
  useIdentificationPlainText,
  useLoader,
  useSearch,
  useSearchQueryParams,
  useZodValidation
} from "../composables"
import { AppRoutes } from "../router/router.enum"
import { DateMoment } from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import { EventNameEnum } from "../global/enums"
import { UiRoomSelectDefault } from "@sb-components/organisms/UiRoom/constants"
import { filterDestinationsDesktop } from "@sb-helpers/destinationAutocomplete"
import { mapApiHotelsToDestinationDesktop } from "../services/hotels/hotels.mapper"
import { scrollToElement } from "@sb-helpers/scrollTo"
import { useHotelsStore } from "@stores/hotels/hotels"
import { useI18n } from "vue-i18n"
import { useRouter } from "vue-router"

const destinationDropdownIsOpen = ref(false)
const destinationDropdownIsValid = ref(true)
const destinationErrorMessage = ref<string | undefined>()

const datePickerErrorMessage = ref<string | undefined>()
const datePickerInputsAreValid = ref({ endDate: true, startDate: true })
const datePickerDropdownReference = ref()

const iataCodeErrorMessage = ref<string>()
const promoCodeErrorMessage = ref<string>()

const roomsAndGuestsInError = ref<number[]>([])

export const useSearchEngineSectionValidation = () => {
  const { loading } = useLoader("search")
  const { dates, iataCode, hasRedirectIataCode, hotel, isHotelInBlacklist, promoCodeIsValid, rooms } = useSearch()
  const { hotelsList } = useHotelsStore()
  const { pushGA4Event } = useGA4Event()
  const { queryParams } = useSearchQueryParams()
  const { refreshIdentification } = useIdentificationPlainText()
  const router = useRouter()
  const { t } = useI18n()

  const destinationInput = ref<string>(hotel.value?.name || "")

  const localStartDate = ref<string | undefined>(dates.value?.[DateMoment.START].toString())
  const localEndDate = ref<string | undefined>(dates.value?.[DateMoment.END].toString())

  const scrollOffset = -30 // the px value offset on the scrollTo

  const mappedHotels = computed(() => mapApiHotelsToDestinationDesktop(hotelsList))

  const filteredDestinations = computed(() => {
    return filterDestinationsDesktop(mappedHotels.value, destinationInput)
  })

  const filteredDestinationsFallback = computed(() => {
    return filteredDestinations.value.length === 0 ? mappedHotels.value : filteredDestinations.value
  })

  const destinationErrorMessageValidation = (allowEmptyString?: boolean) => {
    destinationErrorMessage.value = destinationValidation(
      destinationInput.value,
      filteredDestinations.value,
      allowEmptyString
    )

    if (hotel.value || !destinationInput.value || destinationInput.value === "") {
      destinationDropdownIsValid.value = true
    }
  }

  const validateDestinationDropdown = () => {
    destinationErrorMessageValidation()
    destinationDropdownIsValid.value = !!hotel.value
  }

  const datePickerErrorMessageValidation = (
    datesObject: { startDate?: string; endDate?: string },
    allowEmptyDates?: boolean
  ) => {
    localStartDate.value = datesObject.startDate
    localEndDate.value = datesObject.endDate

    if (datesObject?.startDate && datesObject?.startDate !== "" && datesObject?.startDate !== "-") {
      datePickerInputsAreValid.value.startDate = true
    }

    if (datesObject?.endDate && datesObject?.endDate !== "" && datesObject?.endDate !== "-") {
      datePickerInputsAreValid.value.endDate = true
    }

    datePickerErrorMessage.value = datePickerValidation(
      dates.value?.[DateMoment.START]?.toString() || "-",
      dates.value?.[DateMoment.END]?.toString() || "-",
      allowEmptyDates
    )
  }

  const validateDatePicker = () => {
    const datesObject = {
      endDate: dates.value?.[DateMoment.END]?.toString(),
      startDate: dates.value?.[DateMoment.START]?.toString()
    }

    datePickerErrorMessageValidation(datesObject)
    datePickerInputsAreValid.value = datePickerInputsValidation(localStartDate.value, localEndDate.value)
  }

  const roomsAndGuestsAllDropdownsValidation = () => {
    rooms.value.forEach((room) => {
      const roomDropdownsInError: number[] = []

      room.childrenAges.forEach((age, index) => {
        if (age === UiRoomSelectDefault) {
          room.hasErrorMessage = true
          roomDropdownsInError.push(index)
        }
      })

      room.dropdownsInError = [...new Set(roomDropdownsInError)]
    })
  }

  const roomsAndGuestsSingleDropdownValidation = (dropdown: { roomIndex: number; dropdownIndex: number }) => {
    if (rooms.value[dropdown.roomIndex].childrenAges[dropdown.dropdownIndex] === UiRoomSelectDefault) {
      if (rooms.value[dropdown.roomIndex].dropdownsInError) {
        rooms.value[dropdown.roomIndex]?.dropdownsInError?.push(dropdown.dropdownIndex)
      } else {
        rooms.value[dropdown.roomIndex].dropdownsInError = [dropdown.dropdownIndex]
      }
    }

    // if dropdown and not the default value, we have to filter the index out of the array
    else {
      const filteredErrors = rooms.value[dropdown.roomIndex].dropdownsInError?.filter(
        (item) => item !== dropdown.dropdownIndex
      )
      rooms.value[dropdown.roomIndex].dropdownsInError = filteredErrors

      // At this point, there might be no more errors for the room,
      // so we revalidate the rooms to remove the error message if applicable
      roomsAndGuestsRoomsValidation(true)
    }
  }

  const roomsAndGuestsRoomsValidation = (noError?: boolean) => {
    const roomsInError: number[] = []

    rooms.value.forEach((room, index) => {
      if (room.dropdownsInError?.length) {
        room.hasErrorMessage = true
        roomsInError.push(index)
      }
    })

    if (!noError) {
      roomsAndGuestsInError.value = roomsInError
    } else if (roomsInError.length === 0) {
      roomsAndGuestsInError.value = []
    }
  }

  const roomsAndGuestsErrorMessageValidation = () => {
    roomsAndGuestsAllDropdownsValidation()
    roomsAndGuestsRoomsValidation()
  }

  const validateSpecialRatesSection = () => {
    iataCodeErrorMessage.value = undefined
    promoCodeErrorMessage.value = undefined

    const validatedSchema = useZodValidation(iataCode.value, iataSchema)

    if (!validatedSchema.success) {
      const error = validatedSchema.error.format()
      iataCodeErrorMessage.value = error._errors[0]
    }

    if (!promoCodeIsValid.value) {
      promoCodeErrorMessage.value = t("errors.invalid_promo_code")
    }
  }

  const handleSearchEngineSectionValidation = async () => {
    await refreshIdentification()

    validateDestinationDropdown()
    validateDatePicker()
    roomsAndGuestsErrorMessageValidation()
    validateSpecialRatesSection()

    if (!destinationDropdownIsValid.value) {
      const searchEngineSection = document.querySelector("#Destination-dropdown")
      if (searchEngineSection) {
        scrollToElement(searchEngineSection as HTMLElement, undefined, scrollOffset)
      }
      destinationDropdownIsOpen.value = true
    } else if (!datePickerInputsAreValid.value?.endDate || !datePickerInputsAreValid.value?.startDate) {
      const datePicker = document.querySelector("#Date-picker-dropdown")
      if (datePicker) {
        scrollToElement(datePicker as HTMLElement, undefined, scrollOffset)
      }

      const moment = datePickerInputsAreValid.value?.startDate ? DateMoment.END : DateMoment.START
      datePickerDropdownReference.value.openDropdownModal(moment)
    } else if (roomsAndGuestsInError.value?.length > 0) {
      const roomsAndGuests = document.querySelector("#Rooms-and-guests-section")
      if (roomsAndGuests) {
        scrollToElement(roomsAndGuests as HTMLElement, undefined, scrollOffset)
      }
    } else if (iataCodeErrorMessage.value || promoCodeErrorMessage.value) {
      const specialRates = document.querySelector("#Special-rates-section")
      if (specialRates) {
        scrollToElement(specialRates as HTMLElement, undefined, scrollOffset)
      }
    } else {
      return true
    }
  }

  const handleSearchSubmit = async (blocInteractionPosition: string) => {
    const isValid = await handleSearchEngineSectionValidation()

    if (isValid) {
      await handleSearchEventLoading(blocInteractionPosition)
    }
  }

  const handleSearchEventLoading = async (blocInteractionPosition: string) => {
    if (hasRedirectIataCode() || isHotelInBlacklist()) {
      return
    }

    pushGA4Event("step1", "check availability", {
      eventName: EventNameEnum.bloc_interact,
      event_data: {
        bloc_interaction: blocInteractionPosition,
        bloc_name: "check availability & rates"
      }
    })

    loading.value = true

    if (hotel.value?.id && dates.value.length && promoCodeIsValid.value) {
      await router.push({
        path: AppRoutes.STAY,
        query: queryParams.value
      })
    }
  }

  return {
    datePickerDropdownReference,
    datePickerErrorMessage,
    datePickerErrorMessageValidation,
    datePickerInputsAreValid,
    destinationDropdownIsOpen,
    destinationDropdownIsValid,
    destinationErrorMessage,
    destinationErrorMessageValidation,
    destinationInput,
    filteredDestinationsFallback,
    handleSearchEventLoading,
    handleSearchSubmit,
    iataCodeErrorMessage,
    promoCodeErrorMessage,
    roomsAndGuestsErrorMessageValidation,
    roomsAndGuestsInError,
    roomsAndGuestsSingleDropdownValidation,
    validateSpecialRatesSection
  }
}
