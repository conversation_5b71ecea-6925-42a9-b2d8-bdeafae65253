import { SpecialRates } from "@sb-components/organisms/UiSpecialRates/enums"
import { getUserIdentification } from "../services/users/users.fetch"
import { reactive } from "vue"
import { useApiSemaphore } from "./useApiSemaphore"
import { useHTTPError } from "./useHTTPError"
import { useSearch } from "./useSearch"

export interface Identification {
  affiliation: object | null
  agency: object | null
  application: string | null
  cardNumber: number | null
  cardType: string | null
  clientType: string | null
  company: object | null
  context: string | null
  identificationId: string | null
  identificationToken: string | null
  lcahmember: boolean
  loyaltyCard: boolean
  loyaltyMember: boolean
  offerPreference: {
    offer: {
      code: string
      type: string
    }[]
  } | null
  partnerLoyaltyPrograms: string[]
  preferentialCode: number | null
  referer: string | null
}

const identification = reactive<Identification>({
  affiliation: null,
  agency: null,
  application: null,
  cardNumber: null,
  cardType: null,
  clientType: null,
  company: null,
  context: null,
  identificationId: null,
  identificationToken: null,
  lcahmember: false,
  loyaltyCard: false,
  loyaltyMember: false,
  offerPreference: null,
  partnerLoyaltyPrograms: [],
  preferentialCode: null,
  referer: null
})

export const useIdentificationPlainText = () => {
  const { setIdentificationInProgress } = useApiSemaphore()
  const { promoCodeIsValid, specialRate } = useSearch()
  const { error } = useHTTPError("identification")

  const refreshIdentification = async () => {
    try {
      error.value = false
      setIdentificationInProgress(true)
      const newIdentification = await getUserIdentification(identification)
      Object.assign(identification, newIdentification)

      promoCodeIsValid.value = true
    } catch {
      promoCodeIsValid.value = specialRate.value !== SpecialRates.PROMO_CODE
      error.value = true
    } finally {
      setIdentificationInProgress(false)
    }
  }

  return {
    identification,
    refreshIdentification
  }
}
