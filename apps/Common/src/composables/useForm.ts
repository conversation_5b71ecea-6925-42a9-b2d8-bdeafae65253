import { FormBuilderData, FormBuilderSection, FormContext } from "../components/FormBuilder/interface"
import { Ref, computed, inject, ref, watch } from "vue"
import { buildSectionFieldFromBrandSection, buildSectionSchemaFromBrandSection } from "../services/form/form.helpers"
import { ApiBasketSummary } from "../services/form/form.types"
import { BrandConfiguration } from "@shared/types"
import { getBasketSummary } from "../services/form/form.fetch"
import { useHTTPError } from "./useHTTPError"

export type FormConfig = {
  basketId: string
  currency: string
  countryMarket: string
}

export const useForm = (config: Ref<FormConfig>) => {
  const brandConfiguration = inject<BrandConfiguration>("brandConfig")
  const basketSummary = ref<ApiBasketSummary>()
  const { error } = useHTTPError("basketSummary")

  const formContext = computed<FormContext>(() => ({
    isRussianLawContext: basketSummary.value?.isRussianLawContext ?? false
  }))

  const sections = computed<FormBuilderSection[]>(() => {
    const sections: FormBuilderSection[] = []

    if (!basketSummary.value) return sections

    for (const brandSection of brandConfiguration?.formFieldStructure.sections || []) {
      const section: FormBuilderSection = {
        schema: buildSectionSchemaFromBrandSection(brandSection, basketSummary.value),
        structure: {
          expanded: sections.length === 0,
          fields: buildSectionFieldFromBrandSection(brandSection, basketSummary.value),
          key: brandSection.key,
          subtitle: brandSection.subtitle,
          title: brandSection.title
        }
      }

      sections.push(section)
    }

    return sections
  })

  const initialForm = computed<FormBuilderData | undefined>(() => {
    if (!basketSummary.value) return

    return Object.entries(basketSummary.value.displayFields).reduce((acc, [key, field]) => {
      if (!field.isInitiallyDisplayed) return acc

      acc[key] = field.initialValue

      return acc
    }, {} as FormBuilderData)
  })

  watch(
    config,
    async (currentConfig) => {
      if (!currentConfig) return

      const { basketId, countryMarket, currency } = currentConfig

      try {
        error.value = false
        basketSummary.value = await getBasketSummary(basketId, currency, countryMarket)
      } catch {
        error.value = true
      }
    },
    {
      immediate: true
    }
  )

  return { formContext, initialForm, sections }
}
