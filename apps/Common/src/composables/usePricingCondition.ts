import { computed, ref } from "vue"
import { ApiPricingCondition } from "../services/offer/offer.types"
import { cancellationPoliciesMapper } from "../services/offer/offer.mapper"
import { getPricingCondition } from "../services/offer/offer.fetch"

const pricingConditionsMap = ref<Map<string, ApiPricingCondition>>(new Map())

export const usePricingCondition = () => {
  const fetchMultiplePricingCondition = async (offerIds: string[]) => {
    const pricingConditions = await Promise.all(offerIds.map((id) => getPricingCondition(id)))

    pricingConditionsMap.value.clear()

    offerIds.forEach((id, index) => {
      pricingConditionsMap.value.set(id, pricingConditions[index])
    })
  }

  const getPricingConditionById = async (rateCode: string) => {
    let pricingCondition = pricingConditionsMap.value.get(rateCode)

    if (pricingCondition) return pricingCondition

    pricingCondition = await getPricingCondition(rateCode)
    pricingConditionsMap.value.set(rateCode, pricingCondition)

    return pricingCondition
  }

  const cancellationPolicies = computed(() => {
    const allPricingConditions = Array.from(pricingConditionsMap.value.values())

    if (!allPricingConditions.length) return null

    return cancellationPoliciesMapper(
      allPricingConditions.map((pricing) => pricing.offer.pricing.main.simplifiedPolicies)
    )
  })

  return {
    cancellationPolicies,
    fetchMultiplePricingCondition,
    getPricingConditionById,
    pricingConditionsMap
  }
}
