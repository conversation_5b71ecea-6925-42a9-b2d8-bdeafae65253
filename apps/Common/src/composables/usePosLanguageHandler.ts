import { AvailableLocales, i18n } from "../i18n"
import { RouteLocationRaw, useRouter } from "vue-router"
import { computed, onMounted, ref } from "vue"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"
import { usePosStore } from "@stores/pos/pos"

const countriesGeographicalArea = ref("")
const countryRegionLabel = ref("")

export function usePosLanguageHandler() {
  const router = useRouter()
  const { get: getCookies } = useCookies()
  const {
    pos,
    fetchPosCountriesGroupedByContinentInStore,
    findContinentCodeByCountry,
    findCountryLabelByCountryCodeAndLocale
  } = usePosStore()

  onMounted(async () => {
    // init pos country data from store
    await fetchPosCountriesGroupedByContinentInStore()

    countriesGeographicalArea.value = findContinentCodeByCountry(getCookies("userLocalization"))
    countryRegionLabel.value = findCountryLabelByCountryCodeAndLocale(
      getCookies("userLocalization"),
      getCookies("userLang")
    )
  })

  const countriesGeographicalAreaOptions = computed(() => {
    const options =
      pos.posCountriesGroupedByContinent?.map((continent) => ({
        label: continent.continentLabel,
        value: continent.continentCode
      })) || []

    if (!countriesGeographicalArea.value) {
      countriesGeographicalArea.value = options[0]?.value || ""
    }

    return options
  })

  const countryRegionOptions = computed(() => {
    const continent = pos.posCountriesGroupedByContinent?.find(
      (group) => group.continentCode === countriesGeographicalArea.value
    )

    const options =
      continent?.countryList.map((country) => ({
        label: country.accorCountryLanguageLabel,
        languageCode: country.languageCode,
        value: country.accorCountryLanguageLabel
      })) || []

    if (!countryRegionLabel.value || !options.some((option) => option.value === countryRegionLabel.value)) {
      countryRegionLabel.value = options[0]?.value || ""
    }

    return options
  })

  const handleLanguageChange = async () => {
    const langToChange = countryRegionOptions.value.find(
      (option) => option.value === countryRegionLabel.value
    )?.languageCode

    if (!langToChange || !(i18n.global.availableLocales as string[]).includes(langToChange)) return

    i18n.global.locale.value = langToChange as AvailableLocales
    await router.replace({
      ...router.currentRoute.value,
      params: {
        locale: langToChange
      }
    } as RouteLocationRaw)
  }

  const resetState = () => {
    countriesGeographicalArea.value = findContinentCodeByCountry(getCookies("userLocalization"))
    countryRegionLabel.value = findCountryLabelByCountryCodeAndLocale(
      getCookies("userLocalization"),
      getCookies("userLang")
    )
  }

  return {
    countriesGeographicalArea,
    countriesGeographicalAreaOptions,
    countryRegionLabel,
    countryRegionOptions,
    handleLanguageChange,
    resetState
  }
}
