import { ApiBestOffers, Offer } from "../services/offer/offer.types"
import { type AvailabilityStatus, RoomClassCode } from "../services/offer/offer.enums"
import { computed, ref } from "vue"
import { fetchOffers, getAccommodations } from "../services/offer/offer.fetch"
import { Accommodation } from "../services/hotels/hotels.types"
import { DateMoment } from "@sb-components/organisms/UiDatePicker/UiDatePickerDesktop/enums"
import { MatchedPair } from "../services/offer/offer.interface"
import { mapAndGroupByRoomClass } from "../services/offer/offer.mapper"
import { useHTTPError } from "./useHTTPError"
import { useLoader } from "./useLoader"
import { useSearch } from "./useSearch"
import { useUserPOS } from "./useUserPOS"
import { useUserStore } from "../stores/user/user"

export type FetchAllOffersOptions = {
  adults?: number
  childrenAges?: number[]
}

type Section = {
  code: RoomClassCode
  pairs: MatchedPair[]
  title: string
}

const availabilityStatus = ref<AvailabilityStatus | "">("")
const offers = ref<Offer[]>([])
const accommodations = ref<Map<string, Accommodation>>(new Map())
const groupBy = ref<ApiBestOffers["groupBy"]>()
const sections = ref<Section[]>([])

export const useOffersAndAccommodations = () => {
  const { hotelCode, rooms, lengthOfStay, dates, nbAdults } = useSearch()
  const { userPOS } = useUserPOS()
  const user = useUserStore()
  const { loading: isLoadingOffers } = useLoader("search")
  const { error } = useHTTPError("offers")

  const fetchAllOffers = async () => {
    try {
      isLoadingOffers.value = true
      error.value = false

      //Empty composable to avoid displaying data during loading times of the API
      offers.value = []
      accommodations.value.clear()
      groupBy.value = undefined
      sections.value = []
      availabilityStatus.value = ""

      const childrenAges = rooms.value.reduce((acc: number[], room) => {
        acc.push(...room.childrenAges)
        return acc
      }, [])

      if (!hotelCode.value || !userPOS.countryMarket) return

      const apiResult = await fetchOffers(
        hotelCode.value,
        dates.value[DateMoment.START].toISOString().split("T")[0],
        lengthOfStay.value,
        nbAdults.value,
        childrenAges,
        userPOS.currency,
        hasAccessibility.value,
        userPOS.countryMarket
      )

      availabilityStatus.value = apiResult.availability.status
      offers.value = apiResult.bestOffers
      groupBy.value = apiResult.groupBy

      sections.value = mapAndGroupByRoomClass(
        offers.value,
        userPOS.countryMarket,
        lengthOfStay.value,
        user.isLogged,
        !!hotelCode.value,
        groupBy.value
      )
    } catch {
      error.value = true
    } finally {
      isLoadingOffers.value = false
    }
  }

  const getOfferAccommodations = async (productId: string) => {
    if (!hotelCode.value || accommodations.value.get(productId)) return

    const response = await getAccommodations(hotelCode.value, productId)

    response.accommodations.forEach((accommodation) => accommodations.value.set(productId, accommodation))
  }

  const getOfferAccommodationById = async (offer: Offer) => {
    let offerAccommodation = accommodations.value.get(offer.product.id)

    if (!offerAccommodation) {
      await getOfferAccommodations(offer.product.id)
      offerAccommodation = accommodations.value.get(offer.product.id)
    }

    return offerAccommodation
  }

  const hasAccessibility = computed(() => rooms.value.some((room) => room.accessibility))

  const higherDeductionPercentageInAllOffers = computed(() =>
    offers.value.reduce((acc, offer) => {
      const deduction = offer.pricing?.deduction?.[0]
      const deductionPercentage = deduction?.percent
      return Math.max(acc, deductionPercentage ?? 0)
    }, 0)
  )

  return {
    accommodations,
    availabilityStatus,
    fetchOffers: fetchAllOffers,
    getOfferAccommodationById,
    getOfferAccommodations,
    groupBy,
    hasAccessibility,
    higherDeductionPercentageInAllOffers,
    isLoadingOffers,
    offers,
    sections
  }
}
