import { ref } from "vue"

// Default to true to ensure that it's the first identification plaintext that release all the HTTP calls
const isIdentificationInProgress = ref(true)
const isUserDetailsInProgress = ref(false)

export const useApiSemaphore = () => {
  const setIdentificationInProgress = (status: boolean) => (isIdentificationInProgress.value = status)
  const setUserDetailsInProgress = (status: boolean) => (isUserDetailsInProgress.value = status)

  const waitUntilIdentification = async (interval = 1000) => {
    await new Promise((resolve) => {
      const timer = setInterval(() => {
        if (!isIdentificationInProgress.value) {
          clearInterval(timer)
          resolve(true)
        }
      }, interval)
    })
  }

  const waitUntilUserDetails = async (interval = 1000) => {
    await new Promise((resolve) => {
      const timer = setInterval(() => {
        if (!isUserDetailsInProgress.value) {
          clearInterval(timer)
          resolve(true)
        }
      }, interval)
    })
  }

  return {
    isIdentificationInProgress,
    isUserDetailsInProgress,
    setIdentificationInProgress,
    setUserDetailsInProgress,
    waitUntilIdentification,
    waitUntilUserDetails
  }
}
