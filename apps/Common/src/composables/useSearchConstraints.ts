import { Ref, computed, isRef, onMounted, ref, watch } from "vue"
import {
  ReferentialListCalendar,
  ReferentialListKey,
  ReferentialListOccupancy
} from "../services/referentials/referentials.type"
import { ApiHotel } from "../services/hotels/hotels.types"
import { fetchHotel } from "../services/hotels/hotels.fetch"
import { fetchLists } from "../services/referentials/referentials.fetch"
import { useHTTPError } from "./useHTTPError"

const expandedConstraints: ReferentialListKey[] = ["calendar", "room-occupancy"]
const constraintList = ref<Awaited<ReturnType<typeof fetchLists>>>()

export const useSearchConstraints = (hotelId?: Ref<string | undefined> | string | undefined) => {
  const { error: referentialsError } = useHTTPError("referentials")
  const { error: hotelError } = useHTTPError("hotels")

  const id = computed(() => (isRef(hotelId) ? hotelId.value : hotelId))
  const apiHotel = ref<ApiHotel>()

  onMounted(async () => {
    if (!constraintList.value) {
      try {
        referentialsError.value = false
        constraintList.value = await fetchLists(expandedConstraints)
      } catch {
        referentialsError.value = true
      }
    }
  })

  watch(
    id,
    async (newHotelId) => {
      if (newHotelId && apiHotel.value?.id !== newHotelId) {
        try {
          hotelError.value = false
          apiHotel.value = await fetchHotel(newHotelId)
        } catch {
          hotelError.value = true
        }
      } else if (!newHotelId) {
        apiHotel.value = undefined
      }
    },
    {
      immediate: true
    }
  )

  const calendarConstraints = computed<ReferentialListCalendar | undefined>(() => constraintList.value?.calendar)

  const occupancyConstraints = computed<ReferentialListOccupancy | undefined>(() => {
    if (!constraintList.value?.["room-occupancy"]) return

    const constraints: ReferentialListOccupancy = { ...constraintList.value["room-occupancy"] }

    if (apiHotel.value) {
      const hotelOccupancy = apiHotel.value.roomOccupancy

      if (hotelOccupancy.maxAdult) constraints.maxAdult = hotelOccupancy.maxAdult
      if (hotelOccupancy.maxChild) constraints.maxChild = hotelOccupancy.maxChild
      if (hotelOccupancy.maxChildAge) constraints.maxChildAge = hotelOccupancy.maxChildAge
      if (hotelOccupancy.maxPax) constraints.maxPax = hotelOccupancy.maxPax
      if (hotelOccupancy.maxRoom) constraints.maxRoom = hotelOccupancy.maxRoom
    }

    return constraints
  })

  return { calendarConstraints, occupancyConstraints }
}
