import { onMounted, ref } from "vue"
import { PaymentModule } from "@shared/payment"

const API_KEY = import.meta.env.VITE_APP_PAYMENT_API_KEY
const MODULE_URL = import.meta.env.VITE_APP_PAYMENT_IFRAME_MODULE

const paymentModule = ref<PaymentModule>()

/**
 * @see https://accor-it.atlassian.net/wiki/spaces/PAYM/pages/5649694755/HOW+TO+Payment+Card+Module+User+Guide
 */
export function usePaymentIntegration() {
  onMounted(async () => {
    if (paymentModule.value) return

    /* @vite-ignore */
    paymentModule.value = await import(MODULE_URL)
  })

  const iframeMounted = ref(false)

  const mountIframe = (selector: string, transactionId: string) => {
    if (iframeMounted.value || !paymentModule.value) return

    paymentModule.value.initIframe({
      containerSelector: selector,
      params: {
        apiKey: API_KEY,
        tId: transactionId
      }
    })

    iframeMounted.value = true
  }

  return {
    mountIframe,
    paymentModule
  }
}
