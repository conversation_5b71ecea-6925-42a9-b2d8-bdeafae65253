import { computed, onMounted, ref, watch } from "vue"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"
import { usePosStore } from "@stores/pos/pos"
import { useUserPOS } from "./useUserPOS"

const currenciesGeographicalArea = ref("")

export function usePosCurrencyHandler() {
  const { userPOS } = useUserPOS()
  const { get: getCookies } = useCookies()
  const { pos, fetchPosCurrenciesGroupedByContinentInStore, findContinentCodeByCurrencyCode } = usePosStore()

  const localCurrencyCode = ref(userPOS.currency)
  const localGeographicalArea = ref()

  onMounted(async () => {
    // init pos currency data from store
    await fetchPosCurrenciesGroupedByContinentInStore()

    localCurrencyCode.value = userPOS.currency
    const geographicalArea = (currenciesGeographicalArea.value = findContinentCodeByCurrencyCode(
      getCookies("userCurrency"),
      getCookies("userLocalization")
    ))

    currenciesGeographicalArea.value = geographicalArea
    localGeographicalArea.value = geographicalArea
  })

  const currenciesGeographicalAreaOptions = computed(() => {
    const options =
      pos.posCurrenciesGroupedByContinent?.map((continent) => ({
        label: continent.continentLabel,
        value: continent.continentCode
      })) || []

    return options
  })

  const posCurrenciesGroupedByContinent = computed(() => pos.posCurrenciesGroupedByContinent)

  const currencyCodeOptions = computed(() => {
    const continent = posCurrenciesGroupedByContinent.value?.find(
      (group) => group.continentCode === currenciesGeographicalArea.value
    )

    const options =
      continent?.currencyList.map((currency) => ({
        currency,
        currencyCode: currency.currencyCode,
        label: `${currency.currencyCode} - ${currency.currencyLabel}`,
        value: currency.currencyCode
      })) || []

    if (!localCurrencyCode.value || !options.some((option) => option.value === localCurrencyCode.value)) {
      localCurrencyCode.value = options[0]?.value || ""
    }

    return options
  })

  const currentCurrencyCodeOptions = computed(() => {
    const continent = posCurrenciesGroupedByContinent.value?.find(
      (group) => group.continentCode === localGeographicalArea.value
    )

    const options =
      continent?.currencyList.map((currency) => ({
        currency,
        currencyCode: currency.currencyCode,
        label: `${currency.currencyCode} - ${currency.currencyLabel}`,
        value: currency.currencyCode
      })) || []

    return options
  })

  const currentCurrencyLabel = computed(() => {
    const currentOption = currentCurrencyCodeOptions.value.find((option) => option.value === userPOS.currency)

    if (!currentOption) return userPOS.currency

    const { currencyCode, currencySymbol } = currentOption.currency

    if (currencySymbol) {
      return `${currencyCode} (${currencySymbol})`
    }

    return currencyCode
  })

  const resetStateCurrency = () => {
    const geographicalArea = findContinentCodeByCurrencyCode(getCookies("userCurrency"), getCookies("userLocalization"))

    localCurrencyCode.value = userPOS.currency
    currenciesGeographicalArea.value = geographicalArea
    localGeographicalArea.value = geographicalArea
  }

  watch(userPOS, (newVal) => {
    localCurrencyCode.value = newVal.currency
  })

  return {
    currenciesGeographicalArea,
    currenciesGeographicalAreaOptions,
    currencyCode: localCurrencyCode,
    currencyCodeOptions,
    currentCurrencyLabel,
    resetStateCurrency
  }
}
