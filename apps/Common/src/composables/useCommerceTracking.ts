import { ApiCommerceTracking } from "../services/tracking/tracking.types"
import { getCommerceTracking } from "../services/tracking/tracking.fetch"
import { ref } from "vue"
import { useHTTPError } from "./useHTTPError"

const commerceTracking = ref<ApiCommerceTracking>()

export const useCommerceTracking = () => {
  const { error } = useHTTPError("commerceTracking")

  const fetchCommerceTracking = async () => {
    commerceTracking.value = undefined

    try {
      error.value = false
      const apiCommerceTracking = await getCommerceTracking()

      commerceTracking.value = apiCommerceTracking
    } catch {
      error.value = true
    }
  }

  return {
    commerceTracking,
    fetchCommerceTracking
  }
}
