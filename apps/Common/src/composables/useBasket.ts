import { createBasket, getBasket, updateBasketItems } from "../services/basket/basket.fetch"
import { onMounted, ref } from "vue"
import { Basket } from "../services/basket/basket.types"
import { basketMapper } from "../services/basket/basket.mapper"
import { useHTTPError } from "./useHTTPError"
import { useRouteQuery } from "@vueuse/router"
import { useUserPOS } from "./useUserPOS"

type UseBasketOptions = {
  forceRefetch?: boolean
}

const basket = ref<Basket>()
const isLoading = ref<boolean>(false)

export const useBasket = (options?: UseBasketOptions) => {
  const basketId = useRouteQuery<string>("basketId")
  const { userPOS } = useUserPOS()
  const { error } = useHTTPError("basket")

  onMounted(async () => {
    if (!basketId.value) return

    if (basket.value?.id !== basketId.value || options?.forceRefetch) {
      try {
        error.value = false
        isLoading.value = true

        const apiBasket = await getBasket(basketId.value, userPOS.currency, userPOS.countryMarket)
        basket.value = basketMapper(apiBasket.basket)
      } catch {
        error.value = true
      } finally {
        isLoading.value = false
      }
    }
  })

  const upsertBasket = async (offerIds: string[]) => {
    try {
      error.value = false
      isLoading.value = true

      if (basketId.value) {
        const apiBasket = await updateBasketItems(basketId.value, offerIds, userPOS.currency, userPOS.countryMarket)
        basket.value = basketMapper(apiBasket.basket)
      } else {
        const apiBasket = await createBasket(offerIds, userPOS.currency, userPOS.countryMarket)
        basket.value = basketMapper(apiBasket.basket)
      }

      basketId.value = basket.value.id
      return basket.value
    } catch {
      error.value = true
    } finally {
      isLoading.value = false
    }
  }

  return { basket, upsertBasket }
}
