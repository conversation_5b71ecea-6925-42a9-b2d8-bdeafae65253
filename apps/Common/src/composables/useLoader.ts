import { computed, reactive } from "vue"
import { useRouter } from "vue-router"

type LoaderOptions = {
  resetOnNavigation?: boolean
}

const loaders = reactive<Record<string, boolean>>({})

export const useLoader = (loaderKey: string, options: LoaderOptions = {}) => {
  const opts = Object.assign<LoaderOptions, LoaderOptions, LoaderOptions>({}, { resetOnNavigation: true }, options)
  const router = useRouter()

  router.beforeResolve((to, from) => {
    if (opts.resetOnNavigation && to.params.locale === from.params.locale) {
      Object.keys(loaders).forEach((key) => {
        loaders[key] = false
      })
    }
  })

  const loading = computed({
    get() {
      return loaders[loaderKey]
    },
    set(value) {
      loaders[loaderKey] = value
    }
  })

  return { loading }
}
