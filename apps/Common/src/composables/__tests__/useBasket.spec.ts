import { afterAll, beforeEach, describe, expect, it, vi } from "vitest"
import { Basket } from "../../services/basket/basket.types"
import { Currency } from "@/global/enums"
import { TotalAmountMode } from "../../services/basket/basket.enums"
import { defineComponent } from "vue"
import { getBasket } from "../../services/basket/basket.fetch"
import { mount } from "@vue/test-utils"
import router from "../../router"
import { useBasket } from "../useBasket"

vi.mock("../../services/basket/basket.fetch")
vi.mock("../useUserPOS", () => ({
  useUserPOS: () => ({
    userPOS: {
      countryMarket: "FR",
      currency: "EUR",
      fullLocale: "",
      locale: "fr"
    }
  })
}))

const basketMock: Basket = {
  canpol: "canpol",
  currency: "EUR",
  currencySymbol: "€",
  hotel: {
    brand: {
      code: "code",
      label: "label"
    },
    checkInHour: "check-in hour",
    checkOutHour: "check-out hour",
    formattedCheckInHour: "check-in hour formatted",
    formattedCheckOutHour: "check-out hour formatted",
    id: "hotelId",
    localRating: 5,
    media: {
      category: "media category",
      formats: [],
      type: "media type"
    },
    name: "hotel name"
  },
  hotelCurrency: "€",
  id: "basketId",
  items: [],
  pricing: {
    formattedTotalAmount: "  42",
    formattedTotalAmountHotelCurrency: "€",
    totalAmount: 42,
    totalAmountHotelCurrency: 12
  }
}

const useBasketSpy = vi.fn((...args) => useBasket(...args))
const TestComponent = defineComponent({
  props: {
    basket: {
      default: undefined,
      required: false,
      type: Object
    },
    forceRefresh: {
      default: false,
      type: Boolean
    }
  },
  render() {
    return ""
  },
  setup(props) {
    return useBasketSpy(
      props.forceRefresh === undefined
        ? undefined
        : {
            forceRefetch: !!props.forceRefresh
          }
    )
  }
})

describe("useBasket", () => {
  vi.mocked(getBasket).mockImplementation(async () => ({
    basket: {
      currency: Currency.EUR,
      currencySymbol: "€",
      hotel: {
        brand: {
          code: "brand code",
          label: "brand label"
        },
        checkInHour: "check-in hour",
        checkOutHour: "check-out hour",
        formattedCheckInHour: "formatted check-in hour",
        formattedCheckOutHour: "formatted check-out hour",
        id: "get hotel id",
        localRating: 5,
        media: {
          category: "get hotel media category",
          formats: [],
          type: "get hotel media type"
        },
        name: "get hotel name"
      },
      hotelCurrency: Currency.EUR,
      hotelCurrencySymbol: "€",
      id: "get hotel id",
      itemTotalAmountMode: TotalAmountMode.AFTER_TAXES,
      items: [],
      pricing: {
        formattedTotalAmount: "formatted total amount",
        formattedTotalAmountHotelCurrency: "formatted total amount hotel currency",
        totalAmount: 42,
        totalAmountHotelCurrency: 12
      },
      request: {} as any, // eslint-disable-line @typescript-eslint/no-explicit-any,
      simplifiedCanpol: {
        code: 1,
        label: "simplified canpol label"
      }
    },
    modificationReport: [],
    operationWarnings: []
  }))

  afterAll(() => {
    vi.resetAllMocks()
  })

  beforeEach(() => {
    delete router.currentRoute.value.query.basketId

    const wrapper = mount(TestComponent, {
      global: {
        plugins: [router]
      }
    })
    wrapper.vm.basket = undefined as any // eslint-disable-line @typescript-eslint/no-explicit-any

    vi.clearAllMocks()
  })

  describe.sequential("onMounted", () => {
    it("should do nothing if no basket id is set in query string", () => {
      mount(TestComponent, {
        global: {
          plugins: [router]
        }
      })

      expect(getBasket).toHaveBeenCalledTimes(0)
    })

    it("should do nothing if the basket ids match and not forced to refresh", async () => {
      const wrapper = mount(TestComponent, {
        global: {
          plugins: [router]
        }
      })
      expect(getBasket).toHaveBeenCalledTimes(0)

      wrapper.vm.basket = structuredClone(basketMock)
      router.currentRoute.value.query.basketId = "basketId"

      mount(TestComponent, {
        global: {
          plugins: [router]
        }
      })
      expect(useBasketSpy).toBeCalledTimes(2)
      expect(getBasket).toHaveBeenCalledTimes(0)
    })

    it("should fetch the basket if not loaded yet", async () => {
      router.currentRoute.value.query.basketId = "basketId"
      mount(TestComponent, {
        global: {
          plugins: [router]
        }
      })
      expect(getBasket).toHaveBeenCalledTimes(1)
    })

    it("should fetch the basket if ids match but forced", async () => {
      const wrapper = mount(TestComponent, {
        global: {
          plugins: [router]
        }
      })
      expect(getBasket).toHaveBeenCalledTimes(0)

      wrapper.vm.basket = structuredClone(basketMock)
      router.currentRoute.value.query.basketId = "basketId"

      mount(TestComponent, {
        global: {
          plugins: [router]
        },
        props: {
          forceRefresh: true
        }
      })
      expect(useBasketSpy).toBeCalledTimes(2)
      expect(getBasket).toHaveBeenCalledTimes(1)
    })
  })
})
