import { EcommerceType, EventType, PageDataType, UserDataType } from "../global/types"
import {
  buildEcommerceData,
  buildEventData,
  buildPageData,
  buildUserData,
  findContinentByHotelId,
  hotelRegionMapper,
  pushEcommerce,
  pushGA4,
  pushPageView
} from "../helpers"
import { EcommerceEventNameEnum } from "../global/enums"
import { sha256Hash } from "@shared/utils"
import { useHotelsStore } from "@stores/hotels/hotels"
import { useI18n } from "vue-i18n"
import { useSearch } from "../composables/useSearch"
import { useUserPOS } from "../composables/useUserPOS"
import { useUserStore } from "@stores/user/user"

export const useEcommerceEvent = () => {
  const { userPOS } = useUserPOS()

  const buildCurrentContextEcommerceData = (overrides?: EcommerceType["ecommerce"]) => {
    return {
      currency: userPOS.currency.toLowerCase(),
      ...overrides
    }
  }

  const pushEcommerceEvent = (ecommerceData: EcommerceType["ecommerce"], eventName: EcommerceEventNameEnum) => {
    const contextEcommerceData = buildCurrentContextEcommerceData(ecommerceData)

    pushEcommerce(buildEcommerceData({ eventName, overrides: contextEcommerceData }))
  }

  return {
    pushEcommerceEvent
  }
}

export const usePageViewEvent = () => {
  const { hotelsList } = useHotelsStore()
  const { dateIn, dateOut, hotel, lengthOfStay, nbAdults, nbChildren, rooms } = useSearch()
  const { locale } = useI18n()
  const { userPOS } = useUserPOS()
  const { isLogged, loggedUser } = useUserStore()

  const buildCurrentContextPageData = (overrides?: PageDataType) => {
    const arrivalDate = dateIn.value
      ? new Date(dateIn.value).toLocaleDateString("en-US", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric"
        })
      : ""
    const departureDate = dateOut.value
      ? new Date(dateOut.value).toLocaleDateString("en-US", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric"
        })
      : ""

    const leadTime = dateIn.value
      ? Math.max(
          0,
          Math.floor((new Date(dateIn.value).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        ).toString()
      : "0"
    const hotelContinent = findContinentByHotelId(hotelsList, hotel.value?.id) ?? ""
    const hotelRegion = hotelRegionMapper(hotelContinent, hotel.value?.country ?? "")

    const defaultPageData = <PageDataType>{
      adults_nb: nbAdults.value.toString(),
      arrival_date: arrivalDate,
      children_nb: nbChildren.value.toString() || "",
      departure_date: departureDate || "",
      error_type: "",
      hotel_city_name: hotel.value?.city.toLowerCase() || "",
      hotel_continent: hotelContinent,
      hotel_country_name: hotel.value?.country.toLowerCase() || "",
      hotel_name: hotel.value?.name || "",
      hotel_region: hotelRegion,
      hotel_rid_code: hotel.value?.id || "",
      lead_time: leadTime,
      merchant_id: "",
      night_nb: lengthOfStay.value.toString() || "",
      page_language: locale.value || "",
      room_nb: rooms.value.length.toString() || "",
      source_id: ""
    }

    return {
      ...defaultPageData,
      ...overrides
    }
  }

  const buildCurrentContextUserData = async (overrides?: UserDataType) => {
    const defaultUserData = <UserDataType>{
      country_market: userPOS.countryMarket.toLowerCase() || "",
      uiemailsha256: isLogged ? await sha256Hash(loggedUser.email) : "",
      user_all_member: isLogged ? (loggedUser.isLoyaltyMember ? "yes" : "no") : "not_logged",
      user_city: isLogged ? loggedUser.city : "",
      user_country: isLogged ? loggedUser.countryCode : "",
      user_id: isLogged ? loggedUser.id : "",
      user_is_logged: Number(isLogged).toString() || "",
      user_loyalty_card_type: isLogged ? loggedUser.loyaltyCardType : ""
    }

    return {
      ...defaultUserData,
      ...overrides
    }
  }

  const pushPageViewEvent = async (
    step: string,
    subCategory: string,
    pageDataOverrides?: PageDataType,
    userDataOverrides?: UserDataType
  ) => {
    const contextPageData = buildCurrentContextPageData(pageDataOverrides)
    const contextUserData = await buildCurrentContextUserData(userDataOverrides)

    pushPageView(
      buildPageData({ overrides: contextPageData, step, subCategory }),
      buildUserData({ overrides: contextUserData })
    )
  }

  return {
    pushPageViewEvent
  }
}

export const useGA4Event = () => {
  const pushGA4Event = (step: string, subCategory: string, eventDataOverrides?: EventType) => {
    pushGA4(buildEventData({ overrides: eventDataOverrides, step, subCategory }))
  }

  return {
    pushGA4Event
  }
}
