import { useBasket } from "./useBasket"
import { useOffersAndAccommodations } from "./useOffersAndAccommodations"
import { usePricingCondition } from "./usePricingCondition"
import { useSearch } from "./useSearch"

export const useResetProcess = () => {
  const { rooms } = useSearch()
  const { basket } = useBasket()
  const { accommodations, availabilityStatus, groupBy, offers, sections } = useOffersAndAccommodations()
  const { pricingConditionsMap } = usePricingCondition()

  const resetProcess = () => {
    rooms.value = rooms.value.map((room) => ({ ...room, offerId: undefined, productCode: undefined }))

    basket.value = undefined

    accommodations.value.clear()
    availabilityStatus.value = ""
    groupBy.value = undefined
    offers.value = []
    sections.value = []

    pricingConditionsMap.value = new Map()
  }

  return { resetProcess }
}
