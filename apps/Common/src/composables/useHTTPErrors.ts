import { reactive } from "vue"
import { useRouter } from "vue-router"

const errors = reactive<Record<string, boolean>>({})

export const useHTTPErrors = () => {
  const router = useRouter()

  // This hook might be called in naviguation guard that doesn't expose useRouter composable, that's why router might be undefined
  router?.beforeResolve(() => {
    Object.keys(errors).forEach((key) => {
      errors[key] = false
    })
  })

  return { errors }
}
