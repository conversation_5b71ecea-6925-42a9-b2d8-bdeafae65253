import { LocationQuery } from "vue-router"

/**
 * @see https://accor-it.atlassian.net/browse/CONVERT-74
 */
export const QueryParameter = {
  AccessibleProducts: "accessibleProducts",
  /**
   * IATA code
   */
  AgencyId: "agencyId",
  DateIn: "dateIn",
  /**
   * Hotel ID
   */
  HotelCodes: "hotelCodes",
  /**
   * Unit for length of stay - always "NIGHT"
   */
  LengthOfStayUnit: "lengthOfStayUnit",
  /**
   * Number of nights
   */
  LengthOfStayValue: "lengthOfStayValue",
  /**
   * Promo code
   */
  PreferredRateCodes: "preferredRateCodes",
  PromoCode: "promoCode",
  Room: new Array(99).fill(null).map((_, index) => ({
    Accessibility: `product[${index}][accessibility]`,
    Adults: `product[${index}][adultNumber]`,
    ChildrenAge: new Array(99).fill(null).map((_, childIndex) => `product[${index}][childrenAges][${childIndex}]`),
    ClassCode: `product[${index}][classCode]`,
    OfferId: `product[${index}][offerId]`,
    /**
     * Rate code
     */
    RateCode: `product[${index}][rateCode]`,
    RateId: `product[${index}][rateId]`,
    /**
     * Product code (room ID)
     */
    RoomType: `product[${index}][roomType]`
  })),
  SpecialRate: "specialRate"
}

/**
 * Test if a given query parameter key correspond to a children age
 */
export const isChildAgeKey = (key: string, roomIndex: number) => key.startsWith(`product[${roomIndex}][childrenAges]`)

/**
 * Extract all the room indices defined in the query parameters
 */
export const extractRoomIndicesFromQuery = (query: LocationQuery) => {
  const roomIndices = Object.keys(query).reduce((set, key) => {
    const match = /^product\[(\d+)\]/.exec(key)

    if (match?.[1]) set.add(Number(match[1]))

    return set
  }, new Set<number>())

  return Array.from(roomIndices.values())
}

/**
 * For a given room, extract all the children age indices
 */
export const extractRoomChildrenAgeIndicesFromQuery = (query: LocationQuery, roomIndex: number) => {
  const childrenAgeIndices = Object.keys(query).reduce((set, key) => {
    if (!isChildAgeKey(key, roomIndex)) return set

    const regex = new RegExp(`^product\\[${roomIndex}\\]\\[childrenAges\\]\\[(\\d+)\\]`)
    const match = regex.exec(key)

    if (match?.[1]) set.add(Number(match[1]))

    return set
  }, new Set<number>())

  return Array.from(childrenAgeIndices.values())
}
