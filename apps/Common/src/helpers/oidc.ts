import { RouteLocationNormalizedLoaded } from "vue-router"
import { useCookies } from "@vueuse/integrations/useCookies.mjs"

export function setOidcCookies(route: RouteLocationNormalizedLoaded) {
  const { set: setCookie } = useCookies()
  const origin = window.location.origin
  const basePath = import.meta.env.VITE_BASE_URL
  const fullRedirectPath = `${origin}${basePath}${route.fullPath}`

  const expires = new Date()
  expires.setTime(expires.getTime() + 60 * 1000) // 1 minute

  setCookie("oidc_after_login_app_redirect_url", fullRedirectPath, { expires: expires, path: "/" })
}
