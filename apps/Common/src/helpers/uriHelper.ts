import { brandConfiguration } from "../global/consts"

export function replaceUriParams(uri: string, origin: string, locale: string) {
  if (uri?.includes("{BASE_URI}")) {
    uri = uri.replace("{BASE_URI}", origin)
  }

  if (uri?.includes("{locale}")) {
    uri = uri.replace("{locale}", locale)
  }

  if (uri?.includes("{siteCode}")) {
    uri = uri.replace("{siteCode}", brandConfiguration.siteCode)
  }

  return uri
}
