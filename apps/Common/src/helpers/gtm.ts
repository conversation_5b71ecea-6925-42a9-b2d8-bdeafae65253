import { EcommerceType, EventType, PageDataType, UserDataType } from "../global/types"
import { ApiHotel } from "../services/hotels/hotels.types"
import { EcommerceEventNameEnum } from "../global/enums"
import { HotelsList } from "@stores/hotels/interface"

// Add this function to check OneTrust consent
export const hasOneTrustConsent = (): boolean => {
  // Check if OneTrust is available
  if (window.Optanon) {
    // Check for analytics consent group (usually group 2)
    // You may need to adjust the group number based on your OneTrust configuration
    return window.Optanon.IsAlertBoxClosed() && 
           window.Optanon.GetDomainData().Groups.some(
             (group: any) => group.GroupId === 'C0002' && group.Status === true
           );
  }
  
  // If OneTrust is not available, check for cookies
  const oneTrustCookie = document.cookie
    .split('; ')
    .find(row => row.startsWith('OptanonConsent='));
    
  return !!oneTrustCookie;
}

export const pushPageView = (pageData: PageDataType, userData: UserDataType) => {
  // need to push a null page_data before pushing real event (Accor guideline)
  window.dataLayer?.push({ page_data: null })

  window.dataLayer?.push({ event: "page_view", page_data: pageData, user_data: userData })
}

export const pushGA4 = (eventType: EventType) => {
  // need to push a null event_data before pushing real event (Accor guideline)
  window.dataLayer?.push({ event_data: null })

  window.dataLayer?.push({ event: "GA4event", eventName: eventType.eventName, event_data: eventType.event_data })
}

export const pushEcommerce = (ecommerceData: EcommerceType) => {
  //need to push a null event_data before pushing real event (Accor guideline)
  window.dataLayer?.push({ ecommerce: null })

  window.dataLayer?.push({ ecommerce: ecommerceData.ecommerce, event: ecommerceData.event })
}

export const buildEcommerceData = ({
  overrides,
  eventName
}: {
  overrides?: EcommerceType["ecommerce"]
  eventName: EcommerceEventNameEnum
}) => {
  return {
    ecommerce: {
      ...overrides
    },
    event: eventName
  }
}

export const buildEventData = ({
  step,
  subCategory,
  overrides = {}
}: {
  step: string
  subCategory?: string
  overrides?: EventType
}) => {
  return {
    eventName: overrides.eventName || "",
    event_data: {
      pagename: `reservations::${step}::${subCategory}`,
      ...overrides.event_data
    }
  }
}

export const buildPageData = ({
  step,
  subCategory,
  overrides = {}
}: {
  step: string
  subCategory?: string
  overrides?: PageDataType
}) => {
  return {
    adults_nb: "",
    arrival_date: "",
    children_nb: "",
    departure_date: "",
    dynamic_data_ready: "true",
    error_type: "",
    funnel_type: "lbf",
    hotel_city_name: "",
    hotel_continent: "",
    hotel_country_name: "",
    hotel_name: "",
    hotel_region: "",
    hotel_rid_code: "",
    lead_time: "",
    merchant_id: "",
    night_nb: "",
    offer_code: "",
    page_category: "reservations",
    page_language: "",
    page_sub_category_level1: step,
    page_sub_category_level2: subCategory,
    pagename: `reservations::${step}::${subCategory}`,
    partner_id: "",
    room_nb: "",
    source_id: "",
    ...overrides
  }
}

export const buildUserData = ({ overrides = {} }: { overrides?: UserDataType }) => {
  return {
    country_market: "",
    uiemailsha256: "",
    user_all_member: "not logged",
    user_city: "",
    user_country: "",
    user_is_logged: "0",
    user_loyalty_card_type: "",
    ...overrides
  }
}

export function findContinentByHotelId(hotelsList: HotelsList, id: string | undefined) {
  if (!id) return null

  for (const [regions, hotels] of Object.entries(hotelsList)) {
    const hotel = hotels.find((hotel: ApiHotel) => hotel.id === id)
    if (hotel) {
      return regions.replace(/([A-Z])/g, " $1").toLowerCase()
    }
  }

  return null
}

export const hotelRegionMapper = (hotelContinent: string, hotelCountry: string) => {
  if (hotelCountry.toLowerCase() === "china") return "china"

  switch (hotelContinent) {
    case "north america":
    case "south america":
      return "americas"
    case "middle est africa":
    case "africa":
      return "mea"
    case "europe":
      return "eme"
    case "asia":
    case "asia pacific":
      return "apac"
    default:
      return ""
  }
}
