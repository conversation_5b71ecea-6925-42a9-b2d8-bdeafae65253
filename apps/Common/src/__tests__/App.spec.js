import App from "@/App.vue"
import { fetchHotelsByRegion } from "@/services/hotels/hotels.fetch"
import { useHotelsStore } from "@/stores/hotels/hotels"
import { flushPromises, shallowMount } from "@vue/test-utils"
import { create<PERSON><PERSON> } from "pinia"
import { describe, expect, it, vi } from "vitest"
import { createRouter, createWebHistory } from "vue-router"

vi.mock("../services/referentials/referentials.fetch")
vi.mock("../composables/useUserPOS")

// Mock router links
const routes = [
  { path: "/", component: { template: "<div>Home Page</div>" } },
  { path: "/search", component: { template: "<div>Search Page</div>" } },
  { path: "/enhance", component: { template: "<div>Enhance Page</div>" } },
  { path: "/complete", component: { template: "<div>Complete Page</div>" } },
  { path: "/summary", component: { template: "<div>Summary Page</div>" } },
  { path: "/about", component: { template: "<div>About Page</div>" } }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

vi.mock("../services/hotels/hotels.fetch.ts")

vi.mock("../services/tracking/tracking.fetch.ts", () => ({
  getCommerceTracking: vi.fn().mockResolvedValue({
    affCookie: "AH|AH|AH|AH",
    currentVisit: {
      sourceId: "AH",
      merchantId: "AH",
      leverLabel: "Direct Access",
      leverCategory: 1
    },
    outputBestVisit: {
      sourceId: "AH",
      merchantId: "AH",
      leverLabel: "Direct Access",
      leverCategory: 1
    },
    affcookie: "egF+r4RKyHhlSDGT0HNOSA=="
  })
}))

vi.mock("../services/users/users.fetch.ts", () => ({
  getUserIdentification: vi.fn().mockResolvedValue({
    identification: {
      identificationId: "cdb6f7a4-30cc-4327-9449-a50cabead1df",
      affiliation: null,
      company: null,
      agency: null,
      lcahmember: false,
      loyaltyCard: false,
      loyaltyMember: false,
      cardNumber: null,
      cardType: null,
      offerPreference: null,
      preferentialCode: null,
      referer: null,
      clientType: "B2C",
      application: null,
      context: "STANDARD",
      partnerLoyaltyPrograms: [],
      booking: null
    }
  })
}))

describe("App.vue", () => {
  describe("fetching hotels", () => {
    it("fetches hotels on mount", async () => {
      const mockBrandConfig = {
        brandCode: "luxury-brand",
        logo: "logo-url"
      }

      // Mount component with mocked dependencies
      const wrapper = shallowMount(App, {
        global: {
          plugins: [router, createPinia()],
          provide: {
            // abstractionLayer: mockAbstractionLayer,
            brandConfig: mockBrandConfig
          }
        }
      })

      // Wait for async calls
      await wrapper.vm.$nextTick()

      await router.isReady() // Wait for the router to be ready

      // Assertions
      expect(fetchHotelsByRegion).toBeCalledTimes(5) // Check if called with brandCode
    })

    it("should be resilient to promises failing", async () => {
      await flushPromises() // wait for pending promises to settle before clearing mocks
      fetchHotelsByRegion.mockClear()

      // throw 2 first times
      fetchHotelsByRegion
        .mockImplementationOnce(
          vi.fn(async () => {
            throw new Error("error")
          })
        )
        .mockImplementationOnce(
          vi.fn(async () => {
            throw new Error("error")
          })
        )

      const wrapper = shallowMount(App, {
        global: {
          plugins: [router, createPinia()],
          provide: {
            brandConfig: {}
          }
        }
      })
      await router.isReady()

      // wait for promises to settle
      await flushPromises()

      expect(fetchHotelsByRegion).toBeCalledTimes(5) // Check if called with brandCode
      const store = useHotelsStore(wrapper.vm.$pinia)

      expect(store.hotelsList.africa).toHaveLength(0)
      expect(store.hotelsList.asia).toHaveLength(0)
      expect(store.hotelsList.europe.length).toBeGreaterThan(0)
      expect(store.hotelsList.northAmerica.length).toBeGreaterThan(0)
      expect(store.hotelsList.southAmerica.length).toBe(0)

      // @todo: test how errors are processed
    })
  })
})
