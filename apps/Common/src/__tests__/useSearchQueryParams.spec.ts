import { beforeEach, describe, expect, it } from "vitest"
import { useQueryParametersSynchronizer, useToQueryParameters } from "../composables/useSearchQueryParams"
import { DefaultRoomDetails } from "@sb-components/organisms/UiRooms/constants"
import { LocationQuery } from "vue-router"
import { SpecialRates } from "@sb-components/organisms/UiSpecialRates/enums"
import { useSearch } from "../composables/useSearch"

const fmDate = (date: Date) => date.toISOString().slice(0, 10)

describe("Query parameters synchronization", () => {
  beforeEach(() => {
    const { dates, hotel, iataCode, promoCode, rooms, specialRate } = useSearch()
    const today = new Date()
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)

    dates.value = [today, tomorrow]
    hotel.value = undefined
    iataCode.value = undefined
    promoCode.value = undefined
    rooms.value = [
      {
        accessibility: false,
        adults: 1,
        children: 0,
        childrenAges: [],
        id: 0
      }
    ]
    specialRate.value = SpecialRates.NONE
  })

  describe("query parameters synchronization to hook data", () => {
    it("update the dates from the query parameters", () => {
      const { dateIn, dateOut, lengthOfStay, dates } = useSearch()
      const { synchronize } = useQueryParametersSynchronizer()

      // Initial status verification
      const today = new Date()
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)

      expect(fmDate(dateIn.value)).toBe(fmDate(today))
      expect(lengthOfStay.value).toBe(1)
      expect(fmDate(dateOut.value)).toBe(fmDate(tomorrow))
      expect(dates.value.map(fmDate)).toEqual([fmDate(today), fmDate(tomorrow)])

      // Query parameters synchronization
      const testDateIn = new Date()
      testDateIn.setMonth(testDateIn.getMonth() + 1)
      const testLengthOfStay = 5

      const queryParams: LocationQuery = {
        dateIn: fmDate(testDateIn),
        lengthOfStayValue: testLengthOfStay.toString()
      }
      synchronize(queryParams)

      // Post synchronization assertions
      const testDateOut = new Date(testDateIn)
      testDateOut.setDate(testDateOut.getDate() + testLengthOfStay)

      expect(fmDate(dateIn.value)).toBe(fmDate(testDateIn))
      expect(lengthOfStay.value).toBe(testLengthOfStay)
      expect(fmDate(dateOut.value)).toBe(fmDate(testDateOut))
      expect(dates.value.map(fmDate)).toEqual([fmDate(testDateIn), fmDate(testDateOut)])
    })

    it("update the hotel from the query parameters", () => {
      const { hotelCode } = useSearch()
      const { synchronize } = useQueryParametersSynchronizer()

      expect(hotelCode.value).toBeUndefined()

      const queryParams: LocationQuery = {
        hotelCodes: "A7Z4" // Fairmont Austin
      }
      synchronize(queryParams)

      expect(hotelCode.value).toBe("A7Z4")
    })

    it("update the promo codes from the query parameters", () => {
      const { iataCode, promoCode } = useSearch()
      const { synchronize } = useQueryParametersSynchronizer()

      expect(iataCode.value).toBeUndefined()
      expect(promoCode.value).toBeUndefined()

      const queryParams: LocationQuery = {
        agencyId: "IATA_CODE",
        promoCode: "PROMO_CODE"
      }
      synchronize(queryParams)

      expect(iataCode.value).toBe("IATA_CODE")
      expect(promoCode.value).toBe("PROMO_CODE")
    })

    it("update the rooms from the query parameters", () => {
      const { rooms } = useSearch()
      const { synchronize } = useQueryParametersSynchronizer()

      expect(rooms.value.length).toBe(1)
      expect(rooms.value[0]).toEqual({
        ...DefaultRoomDetails,
        id: 0
      })

      const queryParams: LocationQuery = {
        // Room 1 - 2 adults
        "product[0][adultNumber]": "2",
        // Room 2 - 1 adult, 2 children (12 and 2)
        "product[1][adultNumber]": "1",
        "product[1][childrenAges][0]": "12",
        "product[1][childrenAges][1]": "2",
        // Room 3 - 3 adults, 1 children (5)
        "product[2][adultNumber]": "3",
        "product[2][childrenAges][0]": "5"
      }

      synchronize(queryParams)

      expect(rooms.value.length).toBe(3)
      // Room 1
      expect(rooms.value[0].adults).toBe(2)
      expect(rooms.value[0].children).toBe(0)
      expect(rooms.value[0].childrenAges).toEqual([])
      // Room 2
      expect(rooms.value[1].adults).toBe(1)
      expect(rooms.value[1].children).toBe(2)
      expect(rooms.value[1].childrenAges).toEqual([12, 2])
      // Room 3
      expect(rooms.value[2].adults).toBe(3)
      expect(rooms.value[2].children).toBe(1)
      expect(rooms.value[2].childrenAges).toEqual([5])

      // Accessibility
      expect(rooms.value.every((room) => !room.accessibility)).toBe(true)

      queryParams.accessibleProducts = "true"
      synchronize(queryParams)

      expect(rooms.value.every((room) => room.accessibility)).toBe(true)
    })
  })

  describe("hook data to query parameters", () => {
    it("handle dates properly", () => {
      const { dates } = useSearch()
      const qp = useToQueryParameters()

      const today = new Date()

      expect(qp.value.dateIn).toBe(fmDate(today))
      expect(qp.value.lengthOfStayValue).toBe(1)

      const lengthOfStay = 5
      const dateIn = new Date()
      dateIn.setMonth(dateIn.getMonth() + 1)
      const dateOut = new Date(dateIn)
      dateOut.setDate(dateOut.getDate() + lengthOfStay)

      dates.value = [dateIn, dateOut]

      expect(qp.value.dateIn).toBe(fmDate(dateIn))
      expect(qp.value.lengthOfStayValue).toBe(lengthOfStay)
    })

    it("handle hotel properly", () => {
      const { hotel, hotelCode } = useSearch()
      const qp = useToQueryParameters()

      expect(hotelCode.value).toBeUndefined()
      expect(qp.value.hotelCodes).toBeUndefined()

      hotel.value = {
        city: "Test city",
        country: "Testland",
        expediaCompliant: true,
        id: "TEST_HOTEL",
        name: "Test Hotel"
      }

      expect(hotelCode.value).toBe("TEST_HOTEL")
      expect(qp.value.hotelCodes).toBe("TEST_HOTEL")
    })

    it("handle the promo codes properly", () => {
      const { iataCode, promoCode } = useSearch()
      const qp = useToQueryParameters()

      expect(qp.value.agencyId).toBeUndefined()
      expect(qp.value.preferredRateCodes).toBeUndefined()

      iataCode.value = "IATA_CODE"
      expect(qp.value.agencyId).toBe("IATA_CODE")

      promoCode.value = "PROMO_CODE"
      expect(qp.value.promoCode).toBe("PROMO_CODE")
    })

    it("handle the special rates properly", () => {
      const { specialRate } = useSearch()
      const qp = useToQueryParameters()

      expect(qp.value.specialRate).toBe(SpecialRates.NONE)

      specialRate.value = SpecialRates.MILITARY_VETERAN
      expect(qp.value.specialRate).toBe(SpecialRates.MILITARY_VETERAN)
    })

    it("handle the rooms properly", () => {
      const { rooms } = useSearch()
      const qp = useToQueryParameters()

      const keyAdult = (roomIndex: number) => `product[${roomIndex}][adultNumber]`
      const keyChildren = (roomIndex: number, childIndex: number) =>
        `product[${roomIndex}][childrenAges][${childIndex}]`
      const keyAccessibility = (roomIndex: number) => `product[${roomIndex}][accessibility]`

      expect(qp.value[keyAdult(0)]).toBe(1)
      expect(qp.value[keyChildren(0, 0)]).toBeUndefined()

      rooms.value = [
        {
          accessibility: true,
          adults: 2,
          children: 2,
          childrenAges: [2, 4],
          id: 0
        }
      ]

      expect(qp.value[keyAdult(0)]).toBe(2)
      expect(qp.value[keyChildren(0, 0)]).toBe(2)
      expect(qp.value[keyChildren(0, 1)]).toBe(4)
      expect(qp.value[keyAccessibility(0)]).toBe("true")

      rooms.value.push({
        accessibility: false,
        adults: 3,
        children: 3,
        childrenAges: [1, 5, 6],
        id: 0
      })

      expect(qp.value[keyAdult(1)]).toBe(3)
      expect(qp.value[keyChildren(1, 0)]).toBe(1)
      expect(qp.value[keyChildren(1, 1)]).toBe(5)
      expect(qp.value[keyChildren(1, 2)]).toBe(6)
      expect(qp.value[keyAccessibility(1)]).toBe("false")
      expect(qp.value[keyAccessibility(0)]).toBe("true")
    })
  })
})
