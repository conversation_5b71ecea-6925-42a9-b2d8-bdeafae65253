import { NavigationGuard } from "vue-router"
import { Region } from "../../global/enums"
import { fetchHotelsByRegion } from "../../services/hotels/hotels.fetch"
import { useHotelsStore } from "../../stores/hotels/hotels"

type HotelsList = ReturnType<typeof useHotelsStore>["hotelsList"]
type UpdateHotelsList = ReturnType<typeof useHotelsStore>["updateHotelsList"]

const hydrateHotels = async (hotelsList: HotelsList, updateHotelsList: UpdateHotelsList) => {
  const alreadyFetched = Object.values(hotelsList).some((region) => region.length > 0)

  if (alreadyFetched) return

  const hotels = await Promise.allSettled([
    fetchHotelsByRegion(Region.AFRICA),
    fetchHotelsByRegion(Region.ASIA),
    fetchHotelsByRegion(Region.EUROPE),
    fetchHotelsByRegion(Region.NORTH_AMERICA),
    fetchHotelsByRegion(Region.SOUTH_AMERICA)
  ])

  updateHotelsList({
    africa: hotels[0].status === "fulfilled" ? hotels[0].value.results.map((result) => result.hotel) : [],
    asia: hotels[1].status === "fulfilled" ? hotels[1].value.results.map((result) => result.hotel) : [],
    europe: hotels[2].status === "fulfilled" ? hotels[2].value.results.map((result) => result.hotel) : [],
    northAmerica: hotels[3].status === "fulfilled" ? hotels[3].value.results.map((result) => result.hotel) : [],
    southAmerica: hotels[4].status === "fulfilled" ? hotels[4].value.results.map((result) => result.hotel) : []
  })
}

export const guardQueryParametersHotel: NavigationGuard = async (to) => {
  const { updateHotelsList, hotelsList } = useHotelsStore()

  if (!to.query.hotelCodes) return

  const hotelCode = Array.isArray(to.query.hotelCodes) ? to.query.hotelCodes[0] : to.query.hotelCodes

  if (!hotelCode) return

  await hydrateHotels(hotelsList, updateHotelsList)

  const allHotelIds = Object.values(hotelsList).flatMap((hotelsInRegion) => hotelsInRegion.map((hotel) => hotel.id))
  const hotelCodeExists = allHotelIds.includes(hotelCode)

  if (!hotelCodeExists) {
    const query = { ...to.query }
    delete query.hotelCodes

    return { ...to, name: "search", query, replace: true }
  }
}
