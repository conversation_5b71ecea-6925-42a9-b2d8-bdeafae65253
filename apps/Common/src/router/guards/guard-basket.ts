import { LocationQuery, NavigationGuard } from "vue-router"
import { QueryParameter, extractRoomChildrenAgeIndicesFromQuery } from "../../helpers/queryParameters"
import { ApiBasket } from "../../services/basket/basket.types"
import { getBasket } from "../../services/basket/basket.fetch"

type NewQP = Record<string, string | number>

function roomDifferences(basket: ApiBasket, query: LocationQuery) {
  const newQP: NewQP = {}

  for (let roomIndex = 0; roomIndex < basket.items.length; roomIndex++) {
    const basketRoom = basket.items[roomIndex]

    // Number of adults
    const qpRoomAdults = Number(query[QueryParameter.Room[roomIndex].Adults])
    if (basketRoom.adults !== qpRoomAdults) {
      newQP[QueryParameter.Room[roomIndex].Adults] = basketRoom.adults
    }

    // Children ages
    const qpRoomChildIndices = extractRoomChildrenAgeIndicesFromQuery(query, roomIndex)
    const qpRoomChildren = qpRoomChildIndices
      .map((index) => Number(query[QueryParameter.Room[roomIndex].ChildrenAge[index]]))
      .filter((age) => Number.isSafeInteger(age))

    const forceChildrenAgeSynchronization = basketRoom.childrenAges.length !== qpRoomChildren.length

    for (let childrenAgeIndex = 0; childrenAgeIndex <= basketRoom.childrenAges.length; childrenAgeIndex++) {
      const basketRoomChildrenAge = basketRoom.childrenAges[childrenAgeIndex]
      const qpRoomChildrenAge = qpRoomChildren[childrenAgeIndex]

      if (basketRoomChildrenAge !== qpRoomChildrenAge || forceChildrenAgeSynchronization) {
        newQP[QueryParameter.Room[roomIndex].ChildrenAge[childrenAgeIndex]] = basketRoomChildrenAge
      }
    }

    // Room code
    const qpRoomCode = query[QueryParameter.Room[roomIndex].RoomType]
    if (basketRoom.detail.mainProductId !== qpRoomCode) {
      newQP[QueryParameter.Room[roomIndex].RoomType] = basketRoom.detail.mainProductId
    }

    // Rate ID
    const qpRoomRateId = query[QueryParameter.Room[roomIndex].RateId]
    if (basketRoom.offerId !== qpRoomRateId) {
      newQP[QueryParameter.Room[roomIndex].RateId] = basketRoom.offerId
    }

    // Rate Code
    const qpRoomRateCode = query[QueryParameter.Room[roomIndex].RateCode]
    if (basketRoom.detail.rateId !== qpRoomRateCode) {
      newQP[QueryParameter.Room[roomIndex].RateCode] = basketRoom.detail.rateId
    }
  }

  return newQP
}

function dateDifferences(basket: ApiBasket, query: LocationQuery) {
  const newQP: NewQP = {}

  // Date in
  const qpDateIn = query[QueryParameter.DateIn] as string
  if (basket.request.dateIn !== qpDateIn) {
    newQP[QueryParameter.DateIn] = basket.request.dateIn
  }

  // Length of stay
  const qpLengthOfStayValue = Number(query[QueryParameter.LengthOfStayValue])
  if (basket.request.lengthOfStay.value !== qpLengthOfStayValue) {
    newQP[QueryParameter.LengthOfStayValue] = basket.request.lengthOfStay.value
  }

  return newQP
}

export const guardBasket: NavigationGuard = async (to) => {
  const { query } = to
  const { basketId } = query

  if (typeof basketId !== "string") return

  try {
    const { basket } = await getBasket(basketId, "USD", "US") // Country market and currency can be ignored, we don't care regarding price format and such here

    if (basket.items.length <= 0) return

    const newQP: NewQP = {}

    Object.assign(newQP, roomDifferences(basket, query), dateDifferences(basket, query))

    if (Object.keys(newQP).length > 0) {
      return {
        ...to,
        query: { ...query, ...newQP },
        replace: true
      }
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    const newQP = { ...query }
    delete newQP.basketId

    return { ...to, query: newQP, replace: true }
  }
}
