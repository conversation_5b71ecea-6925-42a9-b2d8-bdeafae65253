import { NavigationGuard, NavigationHookAfter } from "vue-router"
import { guardBasket } from "./guard-basket"
import { guardLocale } from "./guard-locale"
import { guardQueryParametersDates } from "./guard-query-parameters-dates"
import { guardQueryParametersHotel } from "./guard-query-parameters-hotel"
import { guardQueryParametersRooms } from "./guard-query-parameters-rooms"
import { guardScrollToTop } from "./guard-scroll-to-top"

type GlobalNavigationGuards = {
  afterEach: NavigationHookAfter[]
  beforeEach: NavigationGuard[]
}

export const navigationGuards: GlobalNavigationGuards = {
  afterEach: [guardScrollToTop],
  beforeEach: [
    guardLocale,
    guardBasket,
    guardQueryParametersHotel,
    guardQueryParametersDates,
    guardQueryParametersRooms
  ]
}
