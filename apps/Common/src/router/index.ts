import { createRouter, createWebHistory } from "vue-router"
import MainLayout from "../layouts/MainLayout/MainLayout.vue"
import { i18n } from "../i18n"
import { navigationGuards } from "./guards"

const base_url = import.meta.env.VITE_BASE_URL || import.meta.env.BASE_URL

const router = createRouter({
  history: createWebHistory(base_url),
  routes: [
    // Redirect root to locale-based search
    {
      path: "/",
      redirect: () => ({
        name: "search",
        params: {
          locale: i18n.global.locale.value || "en"
        }
      })
    },
    {
      children: [
        {
          component: () => import("../views/SearchView.vue"),
          meta: {
            title: "Search - Choose your stay"
          },
          name: "search",
          path: "search"
        },
        {
          component: () => import("../views/StayView.vue"),
          meta: {
            title: "Select - Choose your room"
          },
          name: "stay",
          path: "stay"
        },
        {
          component: () => import("../views/EnhanceView.vue"),
          meta: {
            title: "Enhance - Extend your stay"
          },
          name: "enhance",
          path: "enhance"
        },
        {
          component: () => import("../views/CompleteView.vue"),
          meta: {
            title: "Complete - Book your stay"
          },
          name: "complete",
          path: "complete"
        },
        {
          component: () => import("../views/SummaryView.vue"),
          meta: {
            title: "Summary - Check your stay"
          },
          name: "summary",
          path: "summary"
        },
        {
          component: () => import("../views/AboutView.vue"),
          meta: {
            title: `About ${import.meta.env.VITE_APP_BRAND_NAME}`
          },
          name: "about",
          path: "about"
        },
        // TODO - To remove
        {
          component: () => import(/* webpackChunkName: "demo" */ "../views/FormView.vue"),
          meta: {
            layout: MainLayout,
            title: "Form"
          },
          name: "demo-form",
          path: "demo-form"
        }
      ],
      meta: {
        layout: MainLayout,
        title: `Home - ${import.meta.env.VITE_APP_BRAND_NAME}`
      },
      name: "home",
      path: "/:locale/",
      redirect: () => ({
        name: "search"
      })
    },
    {
      path: "/:pathMatch(.*)*",
      redirect: () => {
        window.location.href = "/en/404-page.html"

        return ""
      }
    }
  ]
})

/**
 * Apply navigation guards
 */
for (const beforeEachGuard of navigationGuards.beforeEach) {
  router.beforeEach(beforeEachGuard)
}

for (const afterEachGuard of navigationGuards.afterEach) {
  router.afterEach(afterEachGuard)
}

export default router
