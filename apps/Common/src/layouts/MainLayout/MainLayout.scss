@use "sass:map";
@use "@sb-base/layout";
@use "@sb-config/spaces" as spacesConfig;
@use "@sb-utilities/mq";
@use "@sb-utilities/spaces";

.Main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  &__main-content {
    grid-column: 1 / -1;

    @include mq.media(">medium") {
      padding: map.get(spacesConfig.$sizes, "8") 0;
      grid-column: 1 / 10;
    }
  }

  &__main-content--no-sidebar {
    grid-column: 1 / -1;
  }

  &__sidebar-content {
    grid-column: 1 / -1;

    @include mq.media(">medium") {
      padding: map.get(spacesConfig.$sizes, "8") 0;
      grid-column: 10 / -1;
    }
  }
}
