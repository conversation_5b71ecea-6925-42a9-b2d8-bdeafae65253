import { AdsTheme } from "@accor/ads-components"
import { type BrandConfiguration } from "@shared/types"
import brandFormConfig from "@apps/common/src/brandForm.config"
import { initializeApp } from "@apps/common/src/main"
import logoInvertSrc from "./assets/brand-logo-invert.svg?url"
import logoSrc from "./assets/brand-logo.svg?url"

// Define brand-specific configuration
const theme = {
  colors: {
    accent: {
      base: {
        dark: "#FFFFFF",
        light: "#2F2F2F"
      },
      hover: {
        light: "#FFFFFF"
      },
      pressed: {
        light: "#3A3A3A"
      }
    },
    bg: {
      min: {
        base: {
          dark: "white",
          light: "black"
        }
      }
    },
    "on-accent": {
      base: {
        light: "white"
      }
    },
    primary: {
      base: {
        dark: "black",
        light: "white"
      },
      hover: {
        light: "black"
      },
      pressed: {
        light: "black"
      }
    },
    "surface-container": {
      low: {
        base: {
          light: "black"
        }
      }
    }
  },
  radii: {
    button: "1px"
  },
  space: {
    px: "4px"
  }
} as AdsTheme

const brandConfig: BrandConfiguration = {
  allHostname: "",
  apiHeaders: {
    clientId: ""
  },
  apiUrl: "",
  appDomain: "",
  brandCode: "",
  endPoints: {
    commerceTracking: "",
    hotels: "",
    identification: "",
    offers: "",
    pricing: ""
  },
  formFieldStructure: brandFormConfig,
  gtmId: "",
  kameleoonId: "",
  legacy: {
    endpoints: {
      moreNumbers: "",
      sitemap: "",
      termsAndConditions: ""
    },
    hostname: ""
  },
  logo: logoSrc,
  logoInvert: logoInvertSrc,
  myBookingsUrl: "",
  name: "Fairmont",
  oneTrustId: "",
  paymentModuleApiKey: "",
  paymentModuleUrl: "",
  siteCode: "",
  step3Permalink: {
    client: "",
    host: "",
    path: ""
  },
  theme: theme
}

// Initialize the app
initializeApp(brandConfig).then((app) => {
  // Mount the app to the DOM
  app.mount("#app")
})
