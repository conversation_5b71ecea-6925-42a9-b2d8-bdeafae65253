{"extends": "@tools/tsconfig/tsconfig.base.json", "include": ["env.d.ts", "src/**/*"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@stores/*": ["../Common/src/stores/*"], "@sb-assets/*": ["../../storybook/src/assets/*"], "@sb-base/*": ["../../storybook/src/styles/base/*"], "@sb-components/*": ["../../storybook/src/components/*"], "@sb-composables/*": ["../../storybook/src/composables/*"], "@sb-config/*": ["../../storybook/src/styles/config/*"], "@sb-fonts/*": ["../../storybook/public/fonts/*"], "@sb-helpers/*": ["../../storybook/src/helpers/*"], "@sb-i18n/*": ["../../storybook/src/i18n/*"], "@sb-styles/*": ["../../storybook/src/styles/*"], "@sb-utilities/*": ["../../storybook/src/styles/utilities/*"]}}}