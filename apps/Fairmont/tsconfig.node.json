{
  //"extends": "@tsconfig/node22/tsconfig.json",
  "extends": "@tools/tsconfig/tsconfig.base.json",
  "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*"],
  "compilerOptions": {
    "module": "esnext",
    "target": "esnext", // Use "es2020" or later
    //"types": ["vite/client"], // Add Vite client types
    "paths": {
      "@/*": ["./src/*"],
      "@service/*": ["./src/services/*"]
    }
  }
}
