VITE_BASE_URL=/booking
VITE_APP_API_BASE_URL=/booking/api/
## OIDC APIs
VITE_OIDC_SIGN_IN_URI=https://rec-login.accor.com/as/authorization.oauth2?client_id=com.myfairmont&persistent=yes&response_type=code&scope=openid%20PROFILE.READ%20PROFILE.WRITE%20PROFILE.WALLET&redirect_uri={BASE_URI}/booking/oidc/redirect&ui_locales={locale}
VITE_OIDC_SIGN_UP_URI=https://rec-login.accor.com/as/authorization.oauth2?client_id=com.myfairmont&persistent=yes&response_type=code&scope=openid%20PROFILE.READ%20PROFILE.WRITE%20PROFILE.WALLET&ui=%7B%22brand%22%3A%7B%22name%22%3A%22Fair<PERSON>%22%2C%22logoSrc%22%3A%22https%3A%2F%2Fuatweb3.myfairmont.com%2Ffrhi%2Finc%2Fimg%2Ffairmont-logo-300.svg%22%2C%22logoWidth%22%3A300%7D%2C%22lcahLoginLinkDisplay%22%3Atrue%2C%22lcahOptinDefault%22%3Atrue%2C%22lcahOptinDisplay%22%3Atrue%2C%22rememberMeDisplay%22%3Atrue%2C%22buttonRegisterDisplay%22%3Atrue%2C%22newsLettersOptinCodes%22%3A%5B%22FRM%22%2C%22ALL%22%5D%2C%22newsLettersOptinDefault%22%3Atrue%2C%22newsLettersOptinDisplay%22%3Atrue%2C%22cancelButtonDisplay%22%3Afalse%2C%22socialNetworkDisplay%22%3Atrue%2C%22styles%22%3A%7B%22primaryButtonBackgroundColor%22%3A%22%********%22%2C%22primaryButtonColor%22%3A%22%23ffffff%22%2C%22secondaryButtonBackgroundColor%22%3A%22%23ffffff%22%2C%22secondaryButtonColor%22%3A%22%********%22%2C%22cancelButtonBackgroundColor%22%3A%22%********%22%2C%22cancelButtonColor%22%3A%22%23ffffff%22%7D%7D&accorregister=true&redirect_uri={BASE_URI}/booking/oidc/redirect&ui_locales={locale}
## The correct production URI is below, but we need to refactor the .env files to properly separate the different environments
## VITE_OIDC_SIGN_IN_URI=https://login.accor.com/as/authorization.oauth2?client_id=com.fairmont&persistent=yes&response_type=code&scope=openid PROFILE.READ PROFILE.WRITE PROFILE.WALLET&redirect_uri={BASE_URI}/booking/oidc/redirect&ui={"brand"%3A{"name"%3A"Fairmont"%2C"logoSrc"%3A"https%3A%2F%2Fwww.fairmont.com%2Ffrhi%2Finc%2Fimg%2Ffairmont-logo-300.svg"%2C"logoWidth"%3A300}%2C"lcahLoginLinkDisplay"%3Atrue%2C"rememberMeDisplay"%3Atrue%2C"lcahOptinDefault"%3Atrue%2C"lcahOptinDisplay"%3Atrue%2C"buttonRegisterDisplay"%3Atrue%2C"newsLettersOptinCodes"%3A["FRM"%2C"ALL"]%2C"newsLettersOptinDefault"%3Atrue%2C"newsLettersOptinDisplay"%3Atrue%2C"cancelButtonDisplay"%3Afalse%2C"socialNetworkDisplay"%3Atrue%2C"styles"%3A{"primaryButtonBackgroundColor"%3A"%********"%2C"primaryButtonColor"%3A"%23ffffff"%2C"secondaryButtonBackgroundColor"%3A"%23ffffff"%2C"secondaryButtonColor"%3A"%********"%2C"cancelButtonBackgroundColor"%3A"%********"%2C"cancelButtonColor"%3A"%23ffffff"}}
## VITE_OIDC_SIGN_UP_URI=https://login.accor.com/as/authorization.oauth2?client_id=com.myfairmont&persistent=yes&response_type=code&scope=openid%20PROFILE.READ%20PROFILE.WRITE%20PROFILE.WALLET&ui=%7B%22brand%22%3A%7B%22name%22%3A%22Fairmont%22%2C%22logoSrc%22%3A%22https%3A%2F%2Fuatweb3.myfairmont.com%2Ffrhi%2Finc%2Fimg%2Ffairmont-logo-300.svg%22%2C%22logoWidth%22%3A300%7D%2C%22lcahLoginLinkDisplay%22%3Atrue%2C%22lcahOptinDefault%22%3Atrue%2C%22lcahOptinDisplay%22%3Atrue%2C%22rememberMeDisplay%22%3Atrue%2C%22buttonRegisterDisplay%22%3Atrue%2C%22newsLettersOptinCodes%22%3A%5B%22FRM%22%2C%22ALL%22%5D%2C%22newsLettersOptinDefault%22%3Atrue%2C%22newsLettersOptinDisplay%22%3Atrue%2C%22cancelButtonDisplay%22%3Afalse%2C%22socialNetworkDisplay%22%3Atrue%2C%22styles%22%3A%7B%22primaryButtonBackgroundColor%22%3A%22%********%22%2C%22primaryButtonColor%22%3A%22%23ffffff%22%2C%22secondaryButtonBackgroundColor%22%3A%22%23ffffff%22%2C%22secondaryButtonColor%22%3A%22%********%22%2C%22cancelButtonBackgroundColor%22%3A%22%********%22%2C%22cancelButtonColor%22%3A%22%23ffffff%22%7D%7D&accorregister=true&redirect_uri={BASE_URI}/booking/oidc/redirect

## ALL.com Loyalty Member Account redirection
VITE_LOYALTY_ACCOUNT_REDIRECTION_URI=https://rec1-all.accor.com/account/index.{locale}.shtml
## The correct production URI is below, but we need to refactor the .env files to properly separate the different environments
# VITE_LOYALTY_ACCOUNT_REDIRECTION_URI=https://all.accor.com/account/index.{locale}.shtml

## External URLs
VITE_APP_LEGACY_HOMEPAGE=https://www.fairmont.com/{locale}.html
VITE_APP_LEGACY_MORE_NUMBERS=https://www.fairmont.com/{locale}/more-numbers.html
VITE_APP_LEGACY_SITEMAP=https://www.fairmont.com/{locale}/site-map.html
VITE_APP_LEGACY_TERMS_AND_CONDITIONS=https://www.fairmont.com/{locale}/terms-and-conditions.html
VITE_APP_LEGACY_WEB_ACCESSIBILITY=https://www.fairmont.com/{locale}/web-accessibility.html
##
VITE_APP_ALL_HOSTNAME=all.accor.com
VITE_APP_GTM_ID=GTM-TXVGDVH2
VITE_APP_KAMELEOON_ID=d1et0fu7hk
VITE_APP_LEGACY_HOSTNAME=www.fairmont.com
VITE_APP_ONE_TRUST_ID=fa63b755-710a-475f-bc21-16c706164907
VITE_APP_STEP_3_PERMALINK_HOST=https://permalink.fairmont.com
#SPLUNK
VITE_APP_SPLUNK_ACCESSTOKEN=_3V8zEkaVMwglsfP95QfKQ
VITE_APP_SPLUNK_ENV=PROD
VITE_APP_SPLUNK_CRITICAL=C1