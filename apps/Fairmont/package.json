{"name": "@accor/fairmont-booking", "version": "0.14.0", "files": ["dist/*"], "scripts": {"clean": "rm -rf node_modules && rm -rf dist && rm -rf coverage", "dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:dev": "NODE_ENV=development pnpm run build", "preview": "vite preview --port 4001 --host", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@apps/common": "workspace:*", "@shared/types": "workspace:*", "@shared/utils": "workspace:*", "design-system": "workspace:*", "include-media": "^2.0.0", "normalize.css": "^8.0.1", "sass-loader": "^16.0.4", "vite-svg-loader": "^5.1.0", "zod": "^3.24.3"}, "devDependencies": {"@tools/tsconfig": "workspace:*", "rollup-plugin-copy": "^3.5.0"}}