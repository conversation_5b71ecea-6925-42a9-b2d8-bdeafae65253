# 🛠️ Install stack

### 🗂️ Clone repository

Before cloning the repository, you have to add your public ssh key in your Accor Gitlab account :
[Profile](https://gitlab.softfactory-accor.net/-/user_settings/profile) > [SSH Keys](https://gitlab.softfactory-accor.net/-/user_settings/ssh_keys) > Add

Display and copy/paste your public ssh key :

```
cat ~/.ssh/id_rsa.pub
```

Save and then you can clone :

```
<NAME_EMAIL>:ecom/brands/luxury-booking.git
```

Init and update submodule

```
git submodule update --init --recursive
```

### ⚙️ Install / Run

Add following hosts in your `/etc/hosts`

```
127.0.0.1 booking.fairmont.local
127.0.0.1 storybook.fairmont.local
```

Run

```
npm run dev
```

Run (with force node_modules installation)

```
npm run dev:install
```
