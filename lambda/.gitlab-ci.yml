workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-skip\]/
      when: never
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-release\]/ && $CI_COMMIT_BRANCH == 'main'
      when: never
    - when: always

include:
  - project: "devops/features/pipelines/runner/k8s"
    ref: "master"
    file: "full-k8s-conf.gitlab-ci.yml"
  - project: devops/features/deployment/nexus-raw-uploader
    ref: "v19.17.0"
    file: nexus-raw-upload.gitlab-ci.yml
  - project: ecom/front/sf-management/front-common-ci
    ref: "main"
    file: gitflow.gitlab-ci.yml
  - project: ecom/front/sf-management/front-common-ci
    ref: "main"
    file: gitflow-versionning.gitlab-ci.yml


# image: public.ecr.aws/sam/build-nodejs20.x:1.121.0-20240730174606
image: registry.softfactory-accor.net/devops/tooling/docker-images/node-build:22.3-alpine3.19

stages:
  - .pre
  - install
  - build
  - test
  - quality
  - publish
  - tag
  - delivery
  - notification
  - .post

variables:
  SONAR_EXCLUSIONS: "**/node_modules/**,**/coverage/**,**/reports/**,test.html,**/test/**,**/__tests__/**,*.config.*,**/*.test.*,esbuild.js"

.install:
  stage: install
  script:
    - npm ci
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - oidc/node_modules/
      - api/node_modules/

.build:
  stage: build
  script:    
    - npm run build -ws
  artifacts:
    paths:
      - oidc/dist/
      - api/dist/
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - oidc/node_modules/
      - api/node_modules/

install-feature:
  extends: .install
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^(feature\/|bugfix\/|hotfix\/)/
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

install-release:
  extends: .install
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^(release\/)/ && $CI_PIPELINE_SOURCE != 'merge_request_event'

install-master:
  extends: .install
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/
      when: on_success

install-tags:
  extends: .install
  rules:
    - if: "$CI_COMMIT_TAG"
      when: on_success

build-feature:
  extends:
    - .build
  dependencies:
    - install-feature
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^(feature\/|bugfix\/|hotfix\/)/
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

build-master:
  extends:
    - build-feature
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/
      when: on_success

build-release:
  extends:
    - build-feature
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^(release\/)/ && $CI_PIPELINE_SOURCE != 'merge_request_event'

build-tags:
  extends:
    - build-feature
  rules:
    - if: "$CI_COMMIT_TAG"
      when: on_success

trivy:scan-master:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/

checkmarx-sast:scan-master:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/

checkmarx-sca:scan-master:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^(main|develop)$/
    
.npm-raw-publish:
  stage: publish
  variables:
    ASSET_NAME: ""
  extends:
    - .nexus-raw-upload
  before_script:
    - apk add --no-cache nodejs npm zip
    - SCOPE=$(node -p "require('./${ASSET_NAME}/package.json').name.split('/')[0]")
    - PACKAGE_NAME=$(node -p "require('./${ASSET_NAME}/package.json').name.split('/').pop()")
    - NEXUS_SOURCE_FILE="${PACKAGE_NAME}.zip"
    - NEXUS_TARGET_FILE="${SCOPE}/${PACKAGE_NAME}/${NEXUS_SOURCE_FILE}"
    - echo "Source file $NEXUS_SOURCE_FILE"
    - echo "Target file $NEXUS_TARGET_FILE"
    - zip -j "$NEXUS_SOURCE_FILE" ${ASSET_NAME}/dist/*
    - ls -al
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && '$CI_COMMIT_BRANCH =~ /^(feature|hotfix|bugfix|release)\//'
      when: never
    - if: $CI_COMMIT_BRANCH == 'develop'

npm-raw-publish-oidc:
  variables:
    ASSET_NAME: oidc
  extends:
    - .npm-raw-publish
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && '$CI_COMMIT_BRANCH =~ /^(feature|hotfix|bugfix|release)\//'
      when: never
    - if: $CI_COMMIT_BRANCH == 'develop'

npm-raw-publish-api:
  variables:
    ASSET_NAME: api
  extends:
    - .npm-raw-publish
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && '$CI_COMMIT_BRANCH =~ /^(feature|hotfix|bugfix|release)\//'
      when: never
    - if: $CI_COMMIT_BRANCH == 'develop'

patch-versions:
  stage: tag
  extends: release-branch-auto
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0 # Full git history needed
  script:
    - apk add --no-cache git jq npm
    - ROOT_VERSION=$(node -p "require('./package.json').version")
    - LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
    - echo "Current root version $ROOT_VERSION, Last tag $LAST_TAG"

    # Define known workspaces
    - WORKSPACES="api oidc"
    - echo "Workspaces - $WORKSPACES"

    # Check each workspace for changes
    - |
      for WORKSPACE in $WORKSPACES; do
        echo "Checking workspace: $WORKSPACE"
        if git diff --name-only $LAST_TAG HEAD -- $WORKSPACE; then
          echo "Changes detected in $WORKSPACE, updating version"
          # Update the version
          cd $WORKSPACE
          npm version patch --no-git-tag-version
          cd ..
        else
          echo "No changes in $WORKSPACE, skipping version update"
        fi
      done
    - |
      if [[ -n "$(git status --porcelain)" ]]; then
        # Stage and commit the changes
        git status

        git commit -am "[ci-npm-skip] Increment npm workspace versions"
        git log --oneline -5
        
        # Push to the source branch of the merge request
        git push origin HEAD:$CI_COMMIT_BRANCH
      else
        echo "No version changes needed"
      fi
  rules:
    - if: $CI_COMMIT_BRANCH == 'develop' && $CI_COMMIT_MESSAGE =~ /^\[ci-npm-branch/
    - if: $CI_COMMIT_BRANCH == 'develop' && $CI_COMMIT_MESSAGE =~ /^\[ci-npm-version/
      allow_failure: true

nexus-publish:
  extends: .runner-2xlarge
  stage: publish
  variables:
    ASSET_NAME: ""
  before_script:
    - apk add --no-cache nodejs npm git
    - SCOPE=$(node -p "require('./${ASSET_NAME}/package.json').name.split('/')[0].replace('@','')")
    - CURRENT_VERSION=$(node -pe "require('./${ASSET_NAME}/package.json').version")

    #Check if publish is needed
    - PREV_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
    - echo "Current tag - $CI_COMMIT_TAG, Previous tag - $PREV_TAG"
    - |
      if [ -n "$PREV_TAG" ]; then
        git checkout $PREV_TAG
        PREV_VERSION=$(node -p "require('./${ASSET_NAME}/package.json').version" 2>/dev/null || echo "0.0.1")
        git checkout $CI_COMMIT_TAG
        if [ "$CURRENT_VERSION" = "$PREV_VERSION" ]; then
          echo "Version unchanged for ${ASSET_NAME}: $CURRENT_VERSION, skipping publish"
          exit 0
        else
          echo "Version changed for ${ASSET_NAME}: $PREV_VERSION -> $CURRENT_VERSION, proceeding with publish"
        fi
      else
        echo "No previous tag found, proceeding with publish"
      fi
    - npm pack -w ${ASSET_NAME}
    - PACKAGE_NAME=$(node -p "require('./${ASSET_NAME}/package.json').name.split('/').pop()")
    - NEXUS_SOURCE_FILE=$(ls | grep -E -o "^${SCOPE}-${PACKAGE_NAME}-${CURRENT_VERSION}+[RC0-9\.\-]*\.tgz$")
    - NEXUS_TARGET_FILE="@${SCOPE}/${PACKAGE_NAME}/${NEXUS_SOURCE_FILE#${SCOPE}-}"
  script:
    # - export SF_NEXUS_URL=${SF_NEXUS_URL%*/}
    - echo "registry=${SF_NEXUS_URL}/repository/${SF_NEXUS_NPM_REPO}/" > .npmrc
    # configure nexus auth token
    - echo "$(echo ${SF_NEXUS_URL} | sed 's/https://')/repository/${SF_NEXUS_NPM_REPO}/:_authToken=${SF_NEXUS_NPM_TOKEN}" >> .npmrc
    - npm publish ${NEXUS_SOURCE_FILE} --no-git-checks -w $ASSET_NAME
    - touch $CI_PROJECT_DIR/success
  artifacts:
    paths:
      - "*.tgz"
    expire_in: 30 minutes
  rules:
    - if: "$CI_COMMIT_TAG"
      when: never

nexus-publish-oidc:
  extends:
    - nexus-publish
  variables:
    ASSET_NAME: oidc
  rules:
    - if: "$CI_COMMIT_TAG"
      when: on_success

nexus-publish-api:
  extends:
    - nexus-publish
  variables:
    ASSET_NAME: api
  rules:
    - if: "$CI_COMMIT_TAG"
      when: on_success