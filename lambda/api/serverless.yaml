service: brands-service-api

provider:
  name: aws
  runtime: nodejs20.x
  region: us-east-1

functions:
  api:
    handler: src/index.handler
    events:
      - http:
          path: /booking/api/{proxy+}
          method: "*"

plugins:
  - serverless-dotenv-plugin
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 3000
    host: 0.0.0.0
    timeout: 30
    keepAliveTimeout: 60000 # Add this line

