{"name": "@accor/brands-api", "version": "0.0.4", "description": "", "main": "index.js", "files": ["dist/*"], "scripts": {"dev": "sls offline --noPrependStageInUrl start", "build": "node ../esbuild.config.js ."}, "author": "", "license": "ISC", "dependencies": {"@vendia/serverless-express": "^4.12.6", "axios": "^1.8.1", "cors": "^2.8.5", "express": "^4.21.2", "http-proxy-agent": "^7.0.2", "http-proxy-middleware": "^3.0.3", "https-proxy-agent": "^7.0.6"}}