import express from "express"
import serverless from "@vendia/serverless-express"
import { createProxyMiddleware } from "http-proxy-middleware"

const app = express()

app.use(
  createProxyMiddleware({
    changeOrigin: true,

    on: {
      proxyReq: (proxyReq, req) => {
        proxyReq.setHeader("Apikey", process.env.API_KEY)
        proxyReq.removeHeader("via")

        if (req.url.includes(process.env.CONTACT_ME_URL)) {
          const cookies = extractCookies(req)

          if (cookies.oidc_access_token) {
            const oidcAccessToken = cookies.oidc_access_token
            proxyReq.setHeader("Authorization", `Bearer ${oidcAccessToken}`)
          }
        }
      },
      proxyRes: (proxyRes, req) => {
        const { app_domain: appDomain } = extractCookies(req)

        // Remove transfer-encoding because api gateway don't handle chunk
        delete proxyRes.headers["transfer-encoding"]
        // Rewrite set cookie on dev env to be able to set it
        if (proxyRes.headers["set-cookie"]) {
          proxyRes.headers["set-cookie"] = proxyRes.headers["set-cookie"].map((setCookie) => {
            //TODO remove samesite and secure replace when https is merged on develop (feature/CONVERT-479)
            return setCookie
              .replace("SameSite=none", "SameSite=lax")
              .replace("SameSite=None", "SameSite=lax")
              .replace("Secure", "")
              .replace(".fairmont.com", appDomain)
              .replace(".accor.com", appDomain)
          })
        }
      },
      error: (err, req, res) => {
        console.log("Proxy error:", err)
        res.status(504).send("Proxy failed: " + err.message)
      }
    },
    logger: console,
    target: process.env.TARGET_URL,
  })
)


function extractCookies(req) {
  const cookieIndex = req.rawHeaders.indexOf("cookie")
  const cookieHeader = cookieIndex !== -1 ? req.rawHeaders[cookieIndex + 1] : null
  if (cookieHeader) {
    return cookieHeader.split(";").reduce((acc, cookie) => {
      const [name, value] = cookie.trim().split("=")
      acc[name] = value
      return acc
    }, {})
  }
  return {}
}

export const handler = serverless({ app })
