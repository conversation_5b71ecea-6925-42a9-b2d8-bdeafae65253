import { extractCookies } from "./cookieUtils.mjs"
import { logout } from "./logout.mjs"
import { redirect } from "./redirect.mjs"
import { refresh } from "./refresh.mjs"

export const handler = async (event) => {
  const { error, error_description: errorDescription } = event.queryStringParameters || {}
  const { path: requestPath } = event || {}
  const { app_base_url: baseUrl, app_domain: cookieDomain } = extractCookies(event)
  const decodedBaseUrl = decodeURIComponent(baseUrl)

  if (error || errorDescription) {
    console.error(`Authorization error: ${error} - ${errorDescription}`)

    return {
      statusCode: 302,
      headers: {
        Location: process.env.FAIRMONT_APPLICATION_ERROR_URL
      },
      body: JSON.stringify({ error, error_description: errorDescription })
    }
  }

  switch (requestPath) {
    case process.env.FAIRMONT_REDIRECT_PATH:
      return redirect(event, decodedBaseUrl, cookieDomain)
    case process.env.FAIRMONT_REFRESH_PATH:
      return refresh(event, decodedBaseUrl, cookieDomain)
    case process.env.FAIRMONT_LOGOUT_PATH:
      return logout(event, decodedBaseUrl, cookieDomain)
    default:
      return {
        statusCode: 302,
        headers: {
          Location: process.env.FAIRMONT_APPLICATION_ERROR_URL
        },
        body: JSON.stringify({ error: "Wrong Path", error_description: `OIDC lambda can't handle the path : ${path}` })
      }
  }
}
