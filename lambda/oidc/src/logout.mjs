import axios from "axios"
import { extractCookies } from "./cookieUtils.mjs"

export const logout = async (event, baseUrl, cookieDomain) => {
  try {
    const clientId = process.env.FAIRMONT_CLIENT_ID
    const clientSecretKey = process.env.FAIRMONT_CONVERT_SECRET_KEY
    const credentials = `${clientId}:${clientSecretKey}`
    const base64Credentials = Buffer.from(credentials).toString("base64")

    const cookies = extractCookies(event)
    const piSri = cookies.oidc_pi_sri

    if (!piSri) {
      return {
        statusCode: 302,
        headers: {
          Location: process.env.FAIRMONT_APPLICATION_ERROR_URL
        },
        body: JSON.stringify({ error: "Error during log out", error_description: "No active session to logout" })
      }
    }

    // Revoke session
    await axios.post(
      process.env.ACCOR_LOGOUT_URL,
      { id: piSri },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Basic ${base64Credentials}`,
          "X-XSRF-HEADER": "PingFederate"
        }
      }
    )

    // Revoke refresh token
    const data = {
      token: cookies.oidc_refresh_token,
      token_type_hint: "refresh_token"
    }

    await axios.post(process.env.ACCOR_REVOKE_REFRESH_TOKEN_URL, data, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${base64Credentials}`
      }
    })

    const redirectApplicationUri =
    decodeURIComponent(cookies.oidc_after_login_app_redirect_url) || baseUrl + process.env.FAIRMONT_APPLICATION_DEFAULT_REDIRECT_URL


    return {
      statusCode: 302,
      headers: {
        Location: redirectApplicationUri
      },
      multiValueHeaders: {
        "Set-Cookie": [
          `oidc_user_logged=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_access_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_refresh_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_id_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_expires_in=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_pi_sri=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax; Domain=${cookieDomain}`
        ]
      },
      body: JSON.stringify({ message: "Logged out successfully" })
    }
  } catch (error) {
    console.error("Error during logout:", error)
    return {
      statusCode: 302,
      headers: {
        Location: process.env.FAIRMONT_APPLICATION_ERROR_URL
      },
      body: JSON.stringify({
        error: error.response?.data?.error || "Error during log out",
        error_description: error.response?.data?.error_description || error.message
      })
    }
  }
}
