import axios from "axios"
import { extractCookies } from "./cookieUtils.mjs"

const decodeJWT = (token) => {
  try {
    const payloadB64 = token.split(".")[1]
    const payloadJson = Buffer.from(payloadB64, "base64").toString()
    return JSON.parse(payloadJson)
  } catch (error) {
    console.error("Error decoding JWT:", error)
    return null
  }
}

export const redirect = async (event, baseUrl, cookieDomain) => {
  const { code: authorizationCode } = event.queryStringParameters || {}

  if (!authorizationCode) {
    console.error(`Authorization error: No authorization code provided`)
    return {
      statusCode: 302,
      headers: {
        Location: process.env.FAIRMONT_APPLICATION_ERROR_URL
      },
      body: JSON.stringify({ error: "Authorization error", error_description: "No authorization code provided" })
    }
  }

  try {
    const clientId = process.env.FAIRMONT_CLIENT_ID
    const clientSecretKey = process.env.FAIRMONT_CONVERT_SECRET_KEY
    const credentials = `${clientId}:${clientSecretKey}`
    const base64Credentials = Buffer.from(credentials).toString("base64")

    const cookies = extractCookies(event)
    
    const redirectApplicationUri =
    decodeURIComponent(cookies.oidc_after_login_app_redirect_url) || baseUrl + process.env.FAIRMONT_APPLICATION_DEFAULT_REDIRECT_URL

    const data = {
      grant_type: "authorization_code",
      code: authorizationCode,
      redirect_uri: baseUrl + process.env.FAIRMONT_REDIRECT_PATH
    }

    const response = await axios.post(process.env.ACCOR_CONVERT_URL, data, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${base64Credentials}`
      }
    })

    const {
      access_token: accessToken,
      refresh_token: refreshToken,
      id_token: idToken,
      expires_in: tokenExpiresIn
    } = response.data

    const idTokenDecode = decodeJWT(idToken)
    const isSecure = new URL(baseUrl).protocol === "https:"
    const cookieOptions = `HttpOnly; Path=/; SameSite=Lax; Domain=${cookieDomain}${isSecure ? "; Secure" : ""}`

    return {
      statusCode: 302,
      headers: {
        Location: redirectApplicationUri,
      },
      multiValueHeaders: {
        "Set-Cookie": [
          `oidc_user_logged=true; Path=/; SameSite=Lax; Domain=${cookieDomain}`,
          `oidc_access_token=${accessToken}; ${cookieOptions}`,
          `oidc_refresh_token=${refreshToken}; ${cookieOptions}`,
          `oidc_id_token=${idToken}; ${cookieOptions}`,
          `oidc_expires_in=${tokenExpiresIn}; ${cookieOptions}`,
          `oidc_pi_sri=${idTokenDecode?.["pi.sri"]}; ${cookieOptions}`
        ]
      },
      body: JSON.stringify({ message: `Redirecting to ${redirectApplicationUri}` })
    }
  } catch (error) {
    console.error("Error during token exchange:", error)
    return {
      statusCode: 302,
      headers: {
        Location: process.env.FAIRMONT_APPLICATION_ERROR_URL
      },
      body: JSON.stringify({
        error: error.response?.data?.error || "Error during sign in",
        error_description: error.response?.data?.error_description || error.message
      })
    }
  }
}
