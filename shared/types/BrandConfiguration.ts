import { BrandForm } from "./FormStructure"

export interface BrandConfiguration {
  allHostname: string
  apiHeaders: {
    clientId: string
  }
  apiUrl: string | ""
  appDomain: string
  brandCode: string
  endPoints: {
    commerceTracking: string
    hotels: string
    identification: string
    offers: string
    pricing: string
  }
  formFieldStructure: BrandForm
  gtmId: string
  kameleoonId: string
  oneTrustId: string
  name: string
  legacy: {
    endpoints: {
      moreNumbers: string
      sitemap: string
      termsAndConditions: string
    }
    hostname: string
  }
  logo: string
  logoInvert: string
  myBookingsUrl: string
  paymentModuleApiKey: string
  paymentModuleUrl: string
  siteCode: string
  splunk?: {
    realm: string
    token: string
    appName: string
    env: string
    domain: string
    leanixId: string
    snowId: string
    itDomain: string
    itDepartment: string
    pci: string
    critical: string
  }
  step3Permalink: {
    client: string
    host: string
    path: string
  }
  theme: object
}
