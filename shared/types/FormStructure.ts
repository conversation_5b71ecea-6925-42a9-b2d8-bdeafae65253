import { ApiBasketDisplayField } from "../../apps/Common/src/services/form/form.types"
import { FormFieldKey } from "../constants/form"

export interface BrandForm {
  sections: BrandFormSection[]
}

export interface BrandFormSection {
  key: string
  title: string
  subtitle: string
  optional?: boolean
  fields: BrandFormSectionRow[]
}

type BrandFormSectionAccordion = {
  type: "accordion"
  title?: string
  description?: string
  optional?: boolean
  fields: FormStructureField[]
}

type BrandFormSectionText = {
  type: "text"
  title?: string
  subtitle?: string
  optional?: boolean
}

type BrandFormSectionFields = {
  description?: string
  type: "fields"
  keys: FormFieldKey[]
}

type BrandFormSectionHelper = {
  type: "helper"
  label: string
  description: string
}

type BrandFormSectionLoyalty = {
  type: "loyalty"
}

export type BrandFormSectionRow =
  | BrandFormSectionText
  | BrandFormSectionFields
  | BrandFormSectionHelper
  | BrandFormSectionLoyalty
  | BrandFormSectionAccordion

// Technical form
export interface FormStructure {
  sections: FormStructureSection[]
}

export interface FormStructureSection {
  key: string
  title: string
  subtitle: string
  fields: FormStructureField[]
  expanded?: boolean
}

export interface FormStructureField {
  key: string
  type: BrandFormSectionRow["type"]
  label?: string
  description?: string
  fields?: FormField[]
  optional?: boolean
  accordionField?: FormStructureField[]
}

export interface FormField {
  key: string
  displayField: ApiBasketDisplayField
  options?: FormFieldOptions
}

export interface FormFieldOptions {
  inputType: "select" | "radios" | "text"
  options?: { label: string; value: unknown }[]
}
